{"name": "caixa-magica-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "test": "jest", "routes:list": "npx next-list", "commit": "cz", "i18n:check": "i18n-check -l src/locales -s pt", "preinstall": "node -e \"if(!process.env.npm_config_user_agent.includes('yarn')) throw new Error('You must use Yarn to install, not NPM')\""}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/material-nextjs": "^7.2.0", "@mui/x-date-pickers": "^8.7.0", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@types/react-date-range": "^1.4.10", "date-fns": "^4.1.0", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "next": "^15.4.0-canary.42", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "papaparse": "^5.5.3", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "zod": "^3.24.4", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@lingual/i18n-check": "^0.8.4", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^24.1.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.8", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^19.1.6", "commitizen": "^4.3.1", "critters": "^0.0.23", "cz-conventional-changelog": "^3.3.0", "eslint": "^9", "eslint-config-next": "15.4.0", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^15.5.2", "tailwindcss": "^4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5"}, "lint-staged": {"*.{js,ts,tsx}": "eslint --fix"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "preferYarn": true, "engines": {"npm": "please-use-yarn", "yarn": "*"}}