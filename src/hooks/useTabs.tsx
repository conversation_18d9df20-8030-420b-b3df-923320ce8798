'use client';
import { HREF_TAB } from '@/components/ui/sidebar/SidebarItems';
import { BOOL, BoolType, VIEW } from '@/constants/enums';
import { useTabStore } from '@/store/tabStore';
import { getDefaultValuesWithInitial, pathParentFinaleNew, pathWithoutLocale } from '@/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { z, ZodObject } from 'zod';
import { useShallow } from 'zustand/react/shallow';

// IMPORTANT
// if your application doesnt use tabs, you can remove this file and the tabStore
// when changing routes =>> router.push instead of handleChangeTab
// all variables stored on tabs, like VIEW mode, Subtabs, etc etc should now be local variables on the components
// no need for other methods that exist here

// TabChangeArgs type for handleChangeTab
export type TabChangeArgs = {
  id?: string;
  title?: string;
  isStatic?: BoolType;
  noTab?: boolean;
  shouldAddDependencyRelation?: boolean;
  view?: VIEW;
  isReplace?: boolean;
};

// use if you need any variable outside of the hook
// if you want states, you should import directly from the store
export const useTabsState = () =>
  useTabStore(
    useShallow((state) => ({
      openTabs: state.openTabs,
      formContext: state.formContext,
      formGetValues: state.formGetValues,
    }))
  );

// USE THE FOLLOWING 2 ONLY & ALWAYS ON DYNAMIC PAGES for formvalues persistence

// 1. call this to apply formcontext values if they exist
// this way, we dont need to keep components alive and we have the information stored in the store to be used later and saved to the backend
export const useDefaultValuesUnsaved = (initial: any, schema: z.ZodObject<any>) => {
  const tabId = pathWithoutLocale(usePathname());
  let initialValues = initial;
  const formContext = useTabStore.getState().formContext;
  const defaultValuesUnsaved = formContext?.[tabId!];
  if (!defaultValuesUnsaved) initialValues = getDefaultValuesWithInitial(schema, initial);
  console.log('initial values are', initialValues);
  return defaultValuesUnsaved ?? initialValues;
};

// 2 . call this to register a form context to be used on tab exit
export const useRegisterFormGetValues = (tabId: string, getValues: () => any) => {
  const { setFormGetValues } = useTabsMethods();

  useEffect(() => {
    setFormGetValues({ id: tabId, getValues });
  }, []);
};

export const useTabsMethods = () => {
  const router = useRouter();

  // only get stable method references
  const {
    removeTab,
    changeOpenTab,
    isLimitReached,
    constructTab,
    openTab,
    openSubTab,
    setFormContext,
    updateHasChanges,
    setFormGetValues,
    updateSubTab,
    updateTabView,
  } = useTabStore(
    useShallow((state) => ({
      removeTab: state.removeTab,
      changeOpenTab: state.changeOpenTab,
      isLimitReached: state.isLimitReached,
      constructTab: state.constructTab,
      openTab: state.openTab,
      openSubTab: state.openSubTab,
      setFormContext: state.setFormContext,
      updateHasChanges: state.updateHasChanges,
      setFormGetValues: state.setFormGetValues,
      updateSubTab: state.updateSubTab,
      updateTabView: state.updateTabView,
    }))
  );
  const pathname = usePathname();
  const pathLocale = pathWithoutLocale(pathname);

  // TODO forms need to update has changes on their own on change, this is a workaround
  // when usiong formgetValues, ALWAYS clear it after
  const handleFormContext = async () => {
    const { formGetValues } = useTabStore.getState();
    const activeTab = openTab();
    const id = activeTab?.id;
    if (id && formGetValues?.id === id && typeof formGetValues?.getValues === 'function') {
      const formValues = formGetValues.getValues();
      setFormContext({ id, formValues });
    }
    //cleanup here
    setFormGetValues({ clear: true });
  };

  const handleSubTabChange = ({ subTab }: { subTab: string }) => {
    updateSubTab({ subTab });
  };

  const handleUpdateHasChanges = (hasChanges: boolean) => {
    const { openTab } = useTabStore.getState();
    const activeTab = openTab();
    if (!activeTab) return;
    updateHasChanges({ id: activeTab.id, hasChanges });
  };

  // generaete docs for this method simplified, explicit versions will be on the store definitions
  const handleChangeTab = async ({
    id,
    title,
    isStatic = BOOL.UNDEFINED,
    noTab = false,
    shouldAddDependencyRelation,
    view = undefined,
    isReplace = false,
  }: TabChangeArgs) => {
    const { openTabs } = useTabStore.getState();

    if (isLimitReached()) {
      // TODO launch something here, maybe a boolean to be used for a modal warning
      console.warn('Maximum number of open tabs reached');
      return;
    }
    await handleFormContext();
    const currentTab = openTabs.find((iterateTab) => iterateTab.id === id);
    if (currentTab && view) currentTab.view = view;
    const tab = currentTab ?? constructTab({ id: id ?? '/dashboard', isStatic, title, view });
    // TODO: add here check for differences on form
    if (!noTab && tab) changeOpenTab({ isReplace, tab, shouldAddDependencyRelation });
    // add here case for no tabs open
    if (tab?.id !== pathLocale) {
      console.log('Changing tab to:', tab?.id);
      router.push(tab?.id ?? '/dashboard');
    }
  };

  // generaete docs for this method simplified, explicit versions will be on the store definitions
  const handleCloseTab = ({
    id,
    routerPush = true,
  }: {
    id: string;
    // href?: string;
    routerPush?: boolean;
  }) => {
    // TODO add here check for differences on form
    const { openTabs } = useTabStore.getState();
    const tab = openTabs.find((t) => t.id === id);
    if (!tab) {
      console.log('Tab not found');
      return;
    }
    if (tab?.hasChanges) return console.log('you cant close a tab with unsaved changes'); // TODO add here a modal to confirm closing tab with unsaved changes
    // TODO this returns boolean of sucessful state, maybe after should return ENUM for different cases
    const hasRemovedTab = removeTab({ tab });
    if (!hasRemovedTab) {
      return;
    }
    setFormContext({ id, clear: true }); // clear form context for this tab
    const hrefDestination = openTab()?.id;
    if (routerPush && hrefDestination) handleChangeTab({ id: hrefDestination });
    if (routerPush && !hrefDestination) handleChangeTab({ noTab: true });
  };

  const handleUpdateTabView = ({ view, id }: { view: VIEW; id?: string }) => {
    updateTabView({ id, view });
  };

  //most are returned just because, most shouldnt be used outside this hook because they dont work alone properly
  return {
    handleChangeTab,
    handleCloseTab,
    handleSubTabChange,
    handleUpdateHasChanges,
    handleFormContext,
    handleUpdateTabView,
    updateTabView, // to remove
    openSubTab,
    openTab,
    updateSubTab, // to remove
    setFormGetValues,
    setFormContext,
  };
};

/**
 * runs when the URL changes (e.g., browser navigation) to sync the open tab.
 * requires handleChangeTab to be passed in from the main useTabs hook.
 */
export const useTabsSyncOnUrlChange = (handleChangeTab: (args: TabChangeArgs) => void) => {
  const openTabs = useTabStore.getState().openTabs;
  const openTab = useTabStore((state) => state.openTab);
  const pathname = usePathname();
  const pathLocale = pathWithoutLocale(pathname);

  useEffect(() => {
    const activeTab = openTab();
    if (activeTab?.id !== pathLocale) {
      const matchingTab = openTabs.find((tab) => tab.id === pathLocale);
      if (matchingTab) {
        handleChangeTab({ id: matchingTab.id });
      }
    }
    // Only runs when pathLocale changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathLocale]);
};

/**
 * runs only on first mount to open a tab for static pages (e.g., navbar pages).
 */
export const useTabsStaticInitializer = () => {
  const openTabs = useTabStore.getState().openTabs;
  const pathname = usePathname();
  const pathLocale = pathWithoutLocale(pathname);
  const { handleChangeTab } = useTabsMethods();
  useTabsSyncOnUrlChange(handleChangeTab);

  useEffect(() => {
    if (openTabs.find((tab) => tab.id === pathLocale)) return;
    if (!HREF_TAB[pathLocale]) return;
    handleChangeTab({ id: pathLocale, isStatic: BOOL.TRUE });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

/**
 * runs only on first mount to open a tab for dynamic pages (e.g., entity forms).
 */
export const useTabsDynamicInitializer = ({
  initial,
  location,
  view,
}: {
  initial: any;
  location: string;
  view?: VIEW;
}) => {
  const openTabs = useTabStore.getState().openTabs;
  const t = useTranslations();
  const { handleChangeTab, openTab } = useTabsMethods();
  useTabsSyncOnUrlChange(handleChangeTab);

  useEffect(() => {
    if (initial && !openTabs.some((tab) => tab.id === location)) {
      handleChangeTab({
        isReplace: openTab()?.id.endsWith('new'),
        id: location,
        //we need to uniformize requests, so we use same format or simillar for all
        title:
          initial?.info?.details?.name ??
          initial.tabName ??
          initial?.nome ??
          initial?.name ??
          t(`dashboard.tabs.new.${pathParentFinaleNew(location ?? '')}`),
        view: initial.id?.startsWith('new-') ? VIEW.CREATE : view,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export const useDynamicFormTabs = ({
  initialData,
  inputSchema,
  defaultView,
  defaultTab,
}: {
  initialData: any;
  inputSchema: ZodObject<any>;
  defaultView: any;
  defaultTab?: any;
}) => {
  const tabId = pathWithoutLocale(usePathname());
  const defaultValues = useDefaultValuesUnsaved(initialData, inputSchema);
  useTabsDynamicInitializer({ initial: initialData, location: tabId });

  const { view, subTab } = useTabStore(
    useShallow((state) => ({
      view: state.openTabs.find((t) => t.id === tabId)?.view ?? defaultView,
      subTab: state.openTabs.find((t) => t.id === tabId)?.subTab ?? defaultTab,
    }))
  );

  const methods = useForm({
    resolver: zodResolver(inputSchema),
    mode: 'onChange',
    defaultValues,
    disabled: view === VIEW.VIEW,
  });

  const { getValues, formState, handleSubmit } = methods;
  useRegisterFormGetValues(tabId, getValues);

  const readonly = view === VIEW.VIEW;

  return {
    tabId,
    defaultValues,
    view,
    readonly,
    subTab,
    methods,
    getValues,
    formState,
    handleSubmit,
  };
};
