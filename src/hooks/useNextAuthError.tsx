'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

export function useNextAuthError() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const t = useTranslations();

  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    const error = searchParams.get('error');
    if (!error) return;

    switch (error) {
      case 'CredentialsSignin':
        setAuthError(t('errors.invalidCredentials'));
        break;
      default:
        setAuthError(t('errors.unexpectedError'));
    }

    const params = new URLSearchParams(searchParams);
    params.delete('error');
    router.replace(params.size ? `${pathname}?${params.toString()}` : pathname, { scroll: false });
  }, [router, pathname, searchParams]);

  return authError;
}
