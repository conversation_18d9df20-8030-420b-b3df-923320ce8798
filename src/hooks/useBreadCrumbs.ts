import { usePathname } from 'next/navigation';
import { GROUPS } from '@/components/ui/sidebar/SidebarItems';
import { EXTRA_TITLES } from '@/config/breadcrumbExtra';

export type Crumb = { label: string; href?: string };

export function useBreadcrumbs(): Crumb[] {
  const raw = usePathname() || '';
  const clean = raw.replace(/^\/[a-z]{2}(?:-[A-Z]{2})?/, ''); // remove locale

  const crumbs: Crumb[] = [];

  for (const g of GROUPS) {
    for (const sec of g.sections) {
      if (sec.href && clean.startsWith(sec.href)) {
        crumbs.push({ label: g.title }, { label: sec.label, href: sec.href });
      }
      if (sec.items?.some((it) => clean.startsWith(it.href))) {
        crumbs.push({ label: g.title }, { label: sec.label, href: sec.href });

        const item = sec.items.find((it) => clean === it.href);

        if (item) {
          crumbs.push({ label: item.label, href: item.href });
        }
      }
    }
  }

  const parts = clean.split('/').filter(Boolean);
  const parent = parts.at(-2);
  const last = parts.at(-1);

  if (parent && last && EXTRA_TITLES[parent]) {
    const map = EXTRA_TITLES[parent];
    const label = map[last] ?? (map[':id'] && last !== 'new' ? map[':id'] : undefined);
    console.log('my label is: ', label);
    if (label) crumbs.push({ label });
  }

  return crumbs;
}
