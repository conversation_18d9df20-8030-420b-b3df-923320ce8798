import type { Metadata } from 'next';
import { Providers } from '../providers';
import { getLocale } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import '@/styles/globals.css';

export const metadata: Metadata = {
  title: 'Cai<PERSON> Mágica', // TODO rename?
  description: 'Boilerplate com Next.js 15 + Auth + i18n + MUI',
};

export default async function LocaleLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();
  const session = await getServerSession(authOptions);

  return (
    <NextIntlClientProvider locale={locale}>
      <Providers session={session}>{children}</Providers>
    </NextIntlClientProvider>
  );
}
