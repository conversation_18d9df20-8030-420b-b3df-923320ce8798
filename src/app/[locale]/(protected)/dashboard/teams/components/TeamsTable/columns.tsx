import { TeamTableRow } from '@/lib/api/teams';
import { LocationOn as LocationIcon, Group as GroupIcon } from '@mui/icons-material';
import { ColumnDef } from '../../types';
import { Box } from '@mui/material';
import { useTranslations } from 'next-intl';

export const useColumns = (): ColumnDef<TeamTableRow>[] => {
  const t = useTranslations('dashboard.teams');

  return [
    {
      key: 'name',
      label: t('table.columns.name'),
      align: 'left',
    },
    {
      key: 'pole',
      label: t('table.columns.pole'),
      align: 'left',
      render: (value) => (
        <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <LocationIcon fontSize="small" color="primary" />
          {String(value)}
        </Box>
      ),
    },
    {
      key: 'assignedUsers',
      label: t('table.columns.assignedUsers'),
      align: 'left',
      render: (value) => (
        <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <GroupIcon fontSize="small" color="primary" />
          {String(value)}
        </Box>
      ),
    },
  ];
};
