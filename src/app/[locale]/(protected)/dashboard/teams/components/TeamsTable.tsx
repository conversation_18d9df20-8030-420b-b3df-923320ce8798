'use client';
import type { TeamTableRow } from '@/lib/api/teams';
import { ColumnDef } from '../types';
import TableGeneric from '@/components/ui/TableGeneric';
import { useTranslations } from 'next-intl';

export type TeamsTableProps = {
  rows: TeamTableRow[];
  columns?: readonly ColumnDef<TeamTableRow>[];
  onEditAction: (id: string) => void;
  onDeleteAction: (id: string) => void;
};

export default function TeamsTable({
  rows,
  columns,
  onEditAction,
  onDeleteAction,
}: TeamsTableProps) {
  const t = useTranslations('dashboard.teams');

  return (
    <TableGeneric
      rows={rows}
      columns={columns}
      actions={[
        {
          label: t('table.actions.edit'),
          icon: 'edit',
          onClick: onEditAction,
        },
        {
          label: t('table.actions.delete'),
          icon: 'delete',
          onClick: onDeleteAction,
          color: 'error',
        },
      ]}
      pagination
    />
  );
}
