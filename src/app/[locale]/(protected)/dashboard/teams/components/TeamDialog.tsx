'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Box, Typography } from '@mui/material';
import { AddCircleRounded, Edit as EditIcon } from '@mui/icons-material';
import FormBuilderDialog from '@/components/ui/dialog/FormBuilderDialog';
import { ComposableForm } from '@/components/form-builder/ComposableFormBuilder';
import UserTransferList from './UserTransferList';
import { User, NewTeamFormData, fetchUsers, fetchPoles } from '@/lib/api/teams';
import { useTranslations } from 'next-intl';

interface TeamDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: NewTeamFormData) => void;
  mode?: 'create' | 'edit';
  initialData?: NewTeamFormData;
}

export default function TeamDialog({
  open,
  onClose,
  onSubmit,
  mode = 'create',
  initialData,
}: TeamDialogProps) {
  const t = useTranslations('dashboard.teams');
  const [users, setUsers] = useState<User[]>([]);
  const [poles, setPoles] = useState<{ value: string; label: string }[]>([]);
  const [assignedUserIds, setAssignedUserIds] = useState<string[]>([]);

  const methods = useForm<NewTeamFormData>({
    defaultValues: {
      name: initialData?.name || '',
      pole: initialData?.pole || '',
      assignedUsers: initialData?.assignedUsers || [],
    },
  });

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData && poles.length > 0) {
      methods.reset({
        name: initialData.name,
        pole: initialData.pole,
        assignedUsers: initialData.assignedUsers,
      });
      setAssignedUserIds(initialData.assignedUsers);
    } else if (!initialData) {
      methods.reset({
        name: '',
        pole: '',
        assignedUsers: [],
      });
      setAssignedUserIds([]);
    }
  }, [initialData, methods, poles]);

  useEffect(() => {
    const loadData = async () => {
      try {
        const [usersData, polesData] = await Promise.all([fetchUsers(), fetchPoles()]);
        setUsers(usersData);
        setPoles(polesData);

        // Se temos dados iniciais, resetar o formulário após carregar os poles
        if (initialData) {
          methods.reset({
            name: initialData.name,
            pole: initialData.pole,
            assignedUsers: initialData.assignedUsers,
          });
          setAssignedUserIds(initialData.assignedUsers);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    if (open) {
      loadData();
    }
  }, [open, initialData, methods]);

  const handleFormSubmit = (data: NewTeamFormData) => {
    const formData = {
      ...data,
      assignedUsers: assignedUserIds,
    };
    onSubmit(formData);
    handleClose();
  };

  const handleClose = () => {
    methods.reset();
    setAssignedUserIds([]);
    onClose();
  };

  const handleAssignUsers = (userIds: string[]) => {
    setAssignedUserIds((prev) => [...prev, ...userIds]);
  };

  const handleUnassignUsers = (userIds: string[]) => {
    setAssignedUserIds((prev) => prev.filter((id) => !userIds.includes(id)));
  };

  const availableUsers = users.filter((user) => !assignedUserIds.includes(user.id));
  const assignedUsers = users.filter((user) => assignedUserIds.includes(user.id));

  return (
    <FormBuilderDialog
      open={open}
      onCancel={handleClose}
      onConfirm={methods.handleSubmit(handleFormSubmit)}
      title={mode === 'create' ? t('newTeam.title') : t('editTeam.title')}
      maxWidth="lg"
      cancelLabel={t('buttons.cancel')}
      confirmLabel={mode === 'create' ? t('buttons.create') : t('buttons.save')}
      confirmIcon={mode === 'create' ? <AddCircleRounded /> : <EditIcon />}
      confirmColor="primary"
      confirmDisabled={mode === 'create' && assignedUserIds.length === 0}
    >
      <ComposableForm.Provider methods={methods} columns={1}>
        <ComposableForm.Field
          name="name"
          type="text"
          label={t('form.name.label')}
          placeholder={t('form.name.placeholder')}
          required
        />

        <ComposableForm.Field
          name="pole"
          type="select"
          label={t('form.pole.label')}
          placeholder={t('form.pole.placeholder')}
          options={poles}
          required
        />
      </ComposableForm.Provider>

      <Box sx={{ width: '100%', mt: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          {t('form.users.label')}
        </Typography>
        <UserTransferList
          availableUsers={availableUsers}
          assignedUsers={assignedUsers}
          onAssignUsers={handleAssignUsers}
          onUnassignUsers={handleUnassignUsers}
        />
      </Box>
    </FormBuilderDialog>
  );
}
