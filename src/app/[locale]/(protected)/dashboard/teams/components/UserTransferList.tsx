'use client';

import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  IconButton,
  Paper,
  InputAdornment,
  Checkbox,
} from '@mui/material';
import {
  Search as SearchIcon,
  ChevronRight as ChevronRightIcon,
  ChevronLeft as ChevronLeftIcon,
} from '@mui/icons-material';
import { User } from '@/lib/api/teams';
import { useTranslations } from 'next-intl';

interface UserTransferListProps {
  availableUsers: User[];
  assignedUsers: User[];
  onAssignUsers: (userIds: string[]) => void;
  onUnassignUsers: (userIds: string[]) => void;
}

export default function UserTransferList({
  availableUsers,
  assignedUsers,
  onAssignUsers,
  onUnassignUsers,
}: UserTransferListProps) {
  const t = useTranslations('dashboard.teams');
  const [availableSearch, setAvailableSearch] = useState('');
  const [assignedSearch, setAssignedSearch] = useState('');
  const [selectedAvailable, setSelectedAvailable] = useState<string[]>([]);
  const [selectedAssigned, setSelectedAssigned] = useState<string[]>([]);

  const filteredAvailableUsers = useMemo(() => {
    return availableUsers.filter((user) =>
      user.name.toLowerCase().includes(availableSearch.toLowerCase())
    );
  }, [availableUsers, availableSearch]);

  const filteredAssignedUsers = useMemo(() => {
    return assignedUsers.filter((user) =>
      user.name.toLowerCase().includes(assignedSearch.toLowerCase())
    );
  }, [assignedUsers, assignedSearch]);

  const handleAvailableToggle = (userId: string) => {
    setSelectedAvailable((prev) =>
      prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]
    );
  };

  const handleAssignedToggle = (userId: string) => {
    setSelectedAssigned((prev) =>
      prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]
    );
  };

  const handleAssign = () => {
    if (selectedAvailable.length > 0) {
      onAssignUsers(selectedAvailable);
      setSelectedAvailable([]);
    }
  };

  const handleUnassign = () => {
    if (selectedAssigned.length > 0) {
      onUnassignUsers(selectedAssigned);
      setSelectedAssigned([]);
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, height: 400, width: '100%' }}>
      <Paper sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h6" sx={{ p: 2, pb: 1 }}>
          ({availableUsers.length}) {t('form.users.available')}
        </Typography>
        <TextField
          variant="outlined"
          placeholder={t('form.users.searchPlaceholder')}
          value={availableSearch}
          onChange={(e) => setAvailableSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{ mx: 2, mb: 1 }}
          size="small"
        />
        <List
          dense
          sx={{
            flex: 1,
            overflow: 'auto',
            mx: 2,
            mb: 2,
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          {filteredAvailableUsers.map((user) => (
            <ListItem
              key={user.id}
              disablePadding
              sx={{
                backgroundColor: selectedAvailable.includes(user.id)
                  ? 'action.selected'
                  : 'transparent',
              }}
            >
              <ListItemButton onClick={() => handleAvailableToggle(user.id)} dense>
                <Checkbox
                  edge="start"
                  checked={selectedAvailable.includes(user.id)}
                  tabIndex={-1}
                  disableRipple
                  sx={{ mr: 1 }}
                />
                <ListItemText
                  primary={user.name}
                  secondary={user.role}
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Paper>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          gap: 1,
        }}
      >
        <IconButton
          onClick={handleAssign}
          disabled={selectedAvailable.length === 0}
          size="small"
          color="primary"
          sx={{
            border: '1px solid',
            borderRadius: 1,
            padding: '4px 10px',
            height: '30px',
            width: '64px',
            '&:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        >
          <ChevronRightIcon />
        </IconButton>
        <IconButton
          onClick={handleUnassign}
          disabled={selectedAssigned.length === 0}
          size="small"
          color="primary"
          sx={{
            border: '1px solid',
            borderRadius: 1,
            padding: '4px 10px',
            height: '30px',
            width: '64px',
            '&:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        >
          <ChevronLeftIcon />
        </IconButton>
      </Box>

      <Paper sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h6" sx={{ p: 2, pb: 1 }}>
          ({assignedUsers.length}) {t('form.users.assigned')}
        </Typography>
        <TextField
          variant="outlined"
          placeholder={t('form.users.searchPlaceholder')}
          value={assignedSearch}
          onChange={(e) => setAssignedSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{ mx: 2, mb: 1 }}
          size="small"
        />
        <List
          dense
          sx={{
            flex: 1,
            overflow: 'auto',
            mx: 2,
            mb: 2,
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          {filteredAssignedUsers.length === 0 ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                color: 'text.secondary',
              }}
            >
              <Typography variant="body2">{t('form.users.emptyMessage')}</Typography>
            </Box>
          ) : (
            filteredAssignedUsers.map((user) => (
              <ListItem
                key={user.id}
                disablePadding
                sx={{
                  backgroundColor: selectedAssigned.includes(user.id)
                    ? 'action.selected'
                    : 'transparent',
                }}
              >
                <ListItemButton onClick={() => handleAssignedToggle(user.id)} dense>
                  <Checkbox
                    edge="start"
                    checked={selectedAssigned.includes(user.id)}
                    tabIndex={-1}
                    disableRipple
                    sx={{ mr: 1 }}
                  />
                  <ListItemText
                    primary={user.name}
                    secondary={user.role}
                    primaryTypographyProps={{ variant: 'body2' }}
                    secondaryTypographyProps={{ variant: 'caption' }}
                  />
                </ListItemButton>
              </ListItem>
            ))
          )}
        </List>
      </Paper>
    </Box>
  );
}
