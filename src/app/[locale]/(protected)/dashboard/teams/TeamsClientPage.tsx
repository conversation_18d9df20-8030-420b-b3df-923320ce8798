'use client';

import { useState, useEffect } from 'react';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import type { TeamTableRow, NewTeamFormData } from '@/lib/api/teams';
import { fetchTeamAssignedUsers } from '@/lib/api/teams';
import TeamsTable from './components/TeamsTable';
import TeamDialog from './components/TeamDialog';
import { useColumns } from './components/TeamsTable/columns';
import { useTabsStaticInitializer } from '@/hooks/useTabs';
import usePageHeaderStore from '@/store/PageHeader';
import { Delete } from '@mui/icons-material';
import { useTranslations } from 'next-intl';

type PendingAction = {
  id: string;
  type: 'delete';
};

export default function TeamsClientPage({ initialRows }: { initialRows: TeamTableRow[] }) {
  useTabsStaticInitializer();
  const t = useTranslations('dashboard.teams');
  const columns = useColumns();
  const [pendingAction, setPendingAction] = useState<PendingAction | null>(null);
  const [openTeamDialog, setOpenTeamDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [modalData, setModalData] = useState<NewTeamFormData | undefined>(undefined);
  const { setHandlers, reset } = usePageHeaderStore();

  useEffect(() => {
    setHandlers({
      newTeam: () => {
        setDialogMode('create');
        setModalData(undefined);
        setOpenTeamDialog(true);
      },
    });
    return reset;
  }, [setHandlers, reset]);

  const handleEdit = async (id: string) => {
    const team = initialRows.find((row) => row.id === id);
    if (team) {
      try {
        // Buscar usuários atribuídos à equipa
        const assignedUsers = await fetchTeamAssignedUsers(id);

        setDialogMode('edit');
        setOpenTeamDialog(true);

        // Preparar os dados para a modal
        // Mapear o nome do polo para o valor correto do select
        const poleValue = team.pole.toLowerCase();
        const teamData: NewTeamFormData = {
          name: team.name,
          pole: poleValue,
          assignedUsers: assignedUsers,
        };

        setModalData(teamData);
      } catch (error) {
        console.error('Error loading team data:', error);
      }
    }
  };

  const handleDelete = (id: string) => {
    setPendingAction({ id, type: 'delete' });
  };

  const handleCancel = () => {
    setPendingAction(null);
  };

  const handleConfirm = () => {
    if (!pendingAction) return;
    console.log(pendingAction.type, pendingAction.id);
    //TODO: Action de delete
    setPendingAction(null);
  };

  const handleTeamSubmit = (data: NewTeamFormData) => {
    if (dialogMode === 'create') {
      console.log('New team data:', data);
      //TODO: Action de criar equipa
    } else {
      console.log('Edit team data:', data);
      //TODO: Action de editar equipa
    }
  };

  return (
    <>
      <TeamsTable
        rows={initialRows}
        onEditAction={handleEdit}
        onDeleteAction={handleDelete}
        columns={columns}
      />

      <TeamDialog
        open={openTeamDialog}
        onClose={() => setOpenTeamDialog(false)}
        onSubmit={handleTeamSubmit}
        mode={dialogMode}
        initialData={modalData}
      />

      <ConfirmDialog
        open={!!pendingAction}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        maxWidth="sm"
        title={t('deleteTeam.title')}
        subtitle={t('deleteTeam.subtitle')}
        description={t('deleteTeam.description')}
        cancelLabel={t('buttons.cancel')}
        confirmLabel={t('buttons.delete')}
        confirmColor="error"
        confirmIcon={<Delete />}
      />
    </>
  );
}
