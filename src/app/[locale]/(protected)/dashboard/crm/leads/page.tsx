import { mockLeads as leads } from '@/lib/mocks/leads';
import LeadsTable from './components/LeadsTable';

export default async function LeadsPage() {
  // const session = await auth();
  // if (!session || !session.user || !session.user.roles || !can(session.user.roles, 'LEADS_VIEW')) {
  //   redirect('/login?unauthorized=1');
  // }

  return (
    <div>
      <LeadsTable leads={leads} />
    </div>
  );
}
