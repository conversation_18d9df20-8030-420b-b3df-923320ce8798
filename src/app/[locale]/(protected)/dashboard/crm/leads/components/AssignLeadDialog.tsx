'use client';

import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Stack,
  Alert,
  IconButton,
  Typography,
  Box,
  CircularProgress,
} from '@mui/material';
import { PersonRounded, RemoveRedEyeRounded as VisibilityIcon } from '@mui/icons-material';
import { Lead } from '@/lib/mocks/leads';
import TableGeneric, { ColumnDef } from '@/components/ui/TableGeneric';

import { useState } from 'react';
import { useTabsMethods } from '@/hooks/useTabs';
import { ComposableForm } from '@/components/form-builder/ComposableFormBuilder';
import { FormProvider, useForm } from 'react-hook-form';

const mockUsers: UserOption[] = [
  { value: '1', label: '<PERSON>', email: '<EMAIL>' },
  { value: '2', label: '<PERSON>', email: '<EMAIL>' },
  { value: '3', label: '<PERSON><PERSON>', email: '<EMAIL>' },
  { value: '4', label: '<PERSON>', email: '<EMAIL>' },
  { value: '5', label: '<PERSON><PERSON>', email: '<EMAIL>' },
];
type UserOption = {
  value: string;
  label: string;
  email?: string;
};

type Props = {
  lead: Lead;
  open: boolean;
  onClose: () => void;
  dialogTitle: string;
  alertMessage?: string;
  instructionText: string;
  searchPlaceholder: string;
  confirmLabel: string;
  cancelLabel: string;
};

export default function AssignLeadDialog({
  lead,
  open,
  onClose,
  dialogTitle,
  alertMessage,
  instructionText,
  searchPlaceholder,
  confirmLabel,
  cancelLabel,
}: Props) {
  const hasDup = !!lead.duplicateContacts?.length;
  const { handleChangeTab } = useTabsMethods();
  const [loadingId, setLoadingId] = useState<string | null>(null);

  const form = useForm<{ assignedUser: string }>({
    defaultValues: { assignedUser: '' },
    mode: 'onChange',
  });
  const { watch, handleSubmit } = form;
  const assignedUser = watch('assignedUser');
  console.log('seachPlaceholder', searchPlaceholder);
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {dialogTitle} ({lead.id})
      </DialogTitle>

      <DialogContent dividers>
        {hasDup && alertMessage && (
          <Stack spacing={2}>
            <Alert severity="info">{alertMessage}</Alert>

            {lead.duplicateContacts && (
              <TableGeneric
                rows={lead.duplicateContacts.map((dc) => ({
                  ...dc,
                  id: String(dc.id),
                }))}
                pagination={false}
                columns={
                  [
                    { key: 'id', label: 'ID', width: 80 },
                    { key: 'nome', label: 'Nome' },
                    { key: 'email', label: 'Email' },
                    { key: 'contato', label: 'Contato' },
                    { key: 'nif', label: 'NIF' },
                    { key: 'criadoPor', label: 'Criado por' },
                    {
                      key: 'view',
                      label: '',
                      width: 56,
                      align: 'right',
                      render: (_val, row) => {
                        const isLoading = loadingId === row.id;
                        return (
                          <IconButton
                            disabled={isLoading}
                            onClick={async () => {
                              setLoadingId(lead.id);
                              await handleChangeTab({
                                id: `/dashboard/contacts/people/${lead.id}`,
                                title: lead.nome,
                              });
                            }}
                          >
                            {isLoading ? (
                              <CircularProgress color="inherit" />
                            ) : (
                              <VisibilityIcon fontSize="small" />
                            )}
                          </IconButton>
                        );
                      },
                    },
                  ] as ColumnDef<{
                    id: string;
                    nome: string;
                    email?: string;
                    contato?: string;
                    nif?: string;
                    criadoPor: string;
                  }>[]
                }
              />
            )}
          </Stack>
        )}
        <Box
          display="flex"
          flexDirection="column"
          alignItems="stretch"
          justifyContent="center"
          sx={{ mt: 4 }}
        >
          <Typography variant="body1" align="left" sx={{ width: '100%' }}>
            {instructionText}
          </Typography>
          <FormProvider {...form}>
            <ComposableForm.Field
              fullWidth
              type="select"
              name="assignedUser"
              required
              placeholder={searchPlaceholder}
              options={mockUsers.map(({ label, value }) => ({ label, value }))}
            />
          </FormProvider>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button variant="outlined" onClick={onClose}>
          {cancelLabel}
        </Button>
        <Button
          variant="contained"
          disabled={!assignedUser}
          startIcon={<PersonRounded />}
          onClick={handleSubmit(({ assignedUser }) => {
            console.log('Atribuir lead a →', assignedUser);
            onClose();
          })}
        >
          {confirmLabel}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
