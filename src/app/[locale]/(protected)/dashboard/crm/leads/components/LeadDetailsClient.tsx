'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { pathWithoutLocale } from '@/utils';
import { useTabsDynamicInitializer, useTabsMethods } from '@/hooks/useTabs';
import usePageHeaderStore from '@/store/PageHeader';

import StatusChip from '@/components/ui/StatusChip';
import TitleDividerCard from '@/components/ui/TitleDividerCard';
import AssignLeadDialog from './AssignLeadDialog';
import ReasonDialog, { Option } from './ReasonDialog';
import CloseLeadDialog from './CloseLeadDialog';

import { Lead } from './types';
import { TABNAV } from '@/constants/enums';

const failOptions: Option[] = [
  { value: 'incontactavel', label: 'Incontactável, telefone não atribuído e email devolvido' },
  { value: 'repetida_mesmo_curso', label: 'Repetida, vários pedidos para o mesmo curso' },
  {
    value: 'repetida_cursos_diferentes',
    label: 'Repetida, mais que dois pedidos para cursos diferentes',
  },
];

export default function LeadDetailsClient({ lead }: { lead: Lead }) {
  const router = useRouter();
  const pathName = pathWithoutLocale(usePathname());

  useTabsDynamicInitializer({ initial: lead, location: pathName });

  const { setHandlers, reset } = usePageHeaderStore();
  const { handleChangeTab } = useTabsMethods();
  const t = useTranslations('forms');
  const tDialog = useTranslations('dialogs.leads');

  const [openFailDialog, setOpenFailDialog] = useState(false);
  const [openAssignDialog, setOpenAssignDialog] = useState(false);
  const [openCloseDialog, setOpenCloseDialog] = useState(false);

  useEffect(() => {
    setHandlers({
      reassignLead: () => setOpenAssignDialog(true),
      closeSuccess: () => setOpenCloseDialog(true),
      closeFail: () => setOpenFailDialog(true),
    });
    return reset;
  }, [setHandlers, reset]);

  async function handleFailSubmit(/* reason: string, description: string */) {
    setOpenFailDialog(false);
    // await closeLead(lead.id, false, { reason, description });
    router.refresh();
  }

  async function handleCloseWithoutContact() {
    setOpenCloseDialog(false);
    handleChangeTab({
      title: TABNAV.OPPORTUNITIES,
      id: `/dashboard/crm/opportunities/new`,
    });
  }

  function Field({ label, value }: { label: string; value: React.ReactNode }) {
    return (
      <Box>
        <Typography variant="body2" color="text.secondary">
          {label}
        </Typography>
        {React.isValidElement(value) ? (
          <Box>{value}</Box>
        ) : (
          <Typography variant="body2">{value}</Typography>
        )}
      </Box>
    );
  }

  function TwoColumnGrid({ children }: { children: React.ReactNode }) {
    return (
      <Box
        display="grid"
        rowGap={2}
        columnGap={4}
        sx={{ gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' } }}
      >
        {children}
      </Box>
    );
  }

  const sections = [
    {
      key: 'basicInfo',
      fields: [
        { key: 'id', value: lead.id, fullWidth: false },
        { key: 'status', value: <StatusChip status={lead.estado} />, fullWidth: false },
        { key: 'name', value: lead.nome, fullWidth: false },
        { key: 'email', value: lead.email, fullWidth: false },
        { key: 'contact', value: lead.contato, fullWidth: false },
        { key: 'nif', value: lead.nif, fullWidth: false },
      ],
    },
    {
      key: 'location',
      fields: [
        { key: 'postalCode', value: lead.codigoPostal, fullWidth: false },
        { key: 'district', value: lead.distrito, fullWidth: false },
        { key: 'locality', value: lead.localidade, fullWidth: false },
        { key: 'municipality', value: lead.concelho, fullWidth: false },
      ],
    },
    {
      key: 'assignment',
      fields: [
        { key: 'assignedTo', value: lead.atribuidoA, fullWidth: false },
        { key: 'assignmentDate', value: lead.dataAtribuicao, fullWidth: false },
        { key: 'creationDate', value: lead.createdAt, fullWidth: false },
      ],
    },
    {
      key: 'course',
      fields: [
        { key: 'course', value: lead.curso, fullWidth: false },
        { key: 'assignedCampus', value: lead.poloAtribuido, fullWidth: false },
        { key: 'campus', value: lead.polo, fullWidth: false },
      ],
    },
    {
      key: 'message',
      fields: [
        { key: 'educationLevel', value: lead.escolaridade, fullWidth: false },
        { key: 'preferredSchedule', value: lead.horario, fullWidth: false },
        { key: 'message', value: lead.mensagem, fullWidth: true },
      ],
    },
  ] as const;

  return (
    <Box>
      <Box display="grid" gap={3} sx={{ gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' } }}>
        {sections.map((section) => (
          <TitleDividerCard key={section.key} title={t(`subtab.${section.key}`)}>
            <TwoColumnGrid>
              {section.fields.map(({ key, value, fullWidth }) =>
                fullWidth ? (
                  <Box key={key} sx={{ gridColumn: '1 / -1' }}>
                    <Field label={t(`${key}.label`)} value={value} />
                  </Box>
                ) : (
                  <Field key={key} label={t(`${key}.label`)} value={value} />
                )
              )}
            </TwoColumnGrid>
          </TitleDividerCard>
        ))}
      </Box>

      <ReasonDialog
        open={openFailDialog}
        leadId={lead.id}
        options={failOptions}
        onClose={() => setOpenFailDialog(false)}
        onSubmit={handleFailSubmit}
      />

      <AssignLeadDialog
        open={openAssignDialog}
        lead={lead}
        onClose={() => setOpenAssignDialog(false)}
        dialogTitle={tDialog('assign.dialogTitle')}
        alertMessage={tDialog('assign.alertMessage')}
        instructionText={tDialog('assign.instructionText')}
        searchPlaceholder={'assign.searchPlaceholder'}
        confirmLabel={tDialog('assign.confirmLabel')}
        cancelLabel={tDialog('assign.cancelLabel')}
      />

      <CloseLeadDialog
        open={openCloseDialog}
        lead={lead}
        dialogTitle={tDialog('closeSuccess.dialogTitle', { id: lead.id })}
        alertMessage={tDialog('closeSuccess.alertMessage')}
        cancelLabel={tDialog('closeSuccess.cancelLabel')}
        confirmWithoutContactLabel={tDialog('closeSuccess.confirmWithoutContact')}
        confirmWithContactLabel={tDialog('closeSuccess.confirmWithContact')}
        onClose={() => setOpenCloseDialog(false)}
        onConfirmWithoutContact={handleCloseWithoutContact}
        onConfirmWithContact={() => {
          setOpenCloseDialog(false);
          handleChangeTab({
            title: lead.nome,
            id: `/dashboard/crm/opportunities/${lead.id}`,
          });
        }}
      />
    </Box>
  );
}
