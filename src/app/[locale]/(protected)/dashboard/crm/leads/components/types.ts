import { Status } from '@/components/ui/StatusChip';

export type DuplicateContact = {
  id: string;
  nome: string;
  email?: string;
  contato?: string;
  nif?: string;
  criadoPor: string;
};

export interface Lead {
  id: string;
  estado: Status;
  nome: string;
  email: string;
  contato: string;
  nif: string;
  codigoPostal: string;
  distrito: string;
  localidade: string;
  concelho: string;
  atribuidoA: string;
  dataAtribuicao: string;
  createdAt: string;
  curso: string;
  poloAtribuido: string;
  polo: string;
  escolaridade: string;
  horario: string;
  mensagem: string;
  atribuido: string;
  duplicateContacts?: DuplicateContact[];
}

export type FilterMeta =
  | { filterType: 'text' }
  | { filterType: 'select'; options: { value: string; label: string }[] }
  | { filterType: 'date' };

export type FailOption = {
  value: string;
  label: string;
};
