import { PageProps } from '../../../../../../../types';
import { Lead } from '../components/types';
import LeadDetailsClient from '../components/LeadDetailsClient';

export default async function LeadDetailsPage({ params }: PageProps<{ id: string }>) {
  const { id } = await params;
  const lead: Lead = {
    id,
    estado: 'Nova',
    nome: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    contato: '917470173',
    nif: '210495045',
    codigoPostal: '1800-912',
    distrito: 'Distrito',
    localidade: 'Localidade',
    concelho: 'Concelho',
    atribuidoA: '<PERSON><PERSON>',
    dataAtribuicao: '16/01/2024, 12:33:50',
    createdAt: '16/01/2024, 12:33:50',
    curso: 'Auxiliar reabilitação e fisioterapia',
    poloAtribuido: 'Lisboa',
    polo: 'Lisboa',
    escolaridade: 'Licenciatura',
    horario: '<PERSON>h<PERSON>',
    mensagem: '<PERSON><PERSON> frequentou algum curso na SA Formação? Não',
    atribuido: '<PERSON><PERSON>',
    duplicateContacts: [
      {
        id: '29485',
        nome: 'Filipa <PERSON>',
        email: '<EMAIL>',
        contato: '917470173',
        nif: '210683285',
        criadoPor: 'Maria Carvalho',
      },
      {
        id: '29486',
        nome: 'Filipa <PERSON>',
        email: '<EMAIL>',
        contato: '917470173',
        nif: '210683285',
        criadoPor: 'Maria Carvalho',
      },
      {
        id: '29487',
        nome: 'Filipa Castro',
        email: '<EMAIL>',
        contato: '917470173',
        nif: '210683285',
        criadoPor: 'Maria Carvalho',
      },
    ],
  };

  return <LeadDetailsClient lead={lead} />;
}
