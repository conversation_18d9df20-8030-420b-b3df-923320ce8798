import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { useOpportunityTypesOptions } from '../../../../../../../../schemas/opportunitySchema';
import { useTranslations } from 'next-intl';
import { EntitySelectField } from '../../../../../../../../components/form-builder/presets/entityField';
import { ContactsField } from '../../../../../../../../components/form-builder/presets/contactsField';

export function NewDetailsForm() {
  const t = useTranslations('forms');
  const opportunityTypesOptions = useOpportunityTypesOptions();
  return (
    <ComposableForm.Internal.Layout columns={2}>
      <ComposableForm.Field
        name="description"
        label={t('description.label')}
        placeholder={t('description.placeholder')}
        required
        type="text"
      />
      <ContactsField name={'userId'} required />
      <ComposableForm.Field
        name="type"
        label={t('type.label')}
        placeholder={t('type.placeholder')}
        options={opportunityTypesOptions}
        required
        type="select"
      />
      <EntitySelectField name={'entity'} />
      <ContactsField
        name={'assignedTo'}
        label={t('assignedTo.label')}
        placeholder={t('assignedTo.placeholder')}
      />
      <ComposableForm.Field
        name="observations"
        label={t('observations.label')}
        placeholder={t('observations.placeholder')}
        type="text"
        multiline
        colSpan={12}
      />
    </ComposableForm.Internal.Layout>
  );
}
