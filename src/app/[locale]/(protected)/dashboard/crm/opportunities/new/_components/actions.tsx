import { AddOutlined } from '@mui/icons-material';
import { Box, Button } from '@mui/material';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

export function NewOpportunityActions() {
  const t = useTranslations();

  return (
    <Box display={'flex'} gap={'8px'}>
      <Button component={Link} variant={'ghost'} href="/dashboard" type={'submit'}>
        {t('button.cancel')}
      </Button>
      <Button
        component={Button}
        variant={'contained'}
        startIcon={<AddOutlined />}
        type={'submit'}
        form={'new-opportunity-form'}
      >
        {t('button.create')}
      </Button>
    </Box>
  );
}
