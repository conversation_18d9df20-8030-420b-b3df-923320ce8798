'use client';

import { useForm } from 'react-hook-form';
import { SectionCardInternal, SectionTab } from '../../../../../../../../components/ui/SectionCard';
import { useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import { useToast } from '../../../../../../../../components/ui/ToastProvider';
import { useRouter } from 'next/navigation';
import { NewDetailsForm } from './newDetailsForm';
import { EnrollmentForm } from './enrollmentForm';
import { useCentersQuery } from '../../../../../../../../lib/queries/centers';
import ComposableFormWrapper from '../../../../../../../../components/ui/FormWrapper';
import { DefaultFooterActions } from '../../../../../../../../components/ui/form/FormActionsOptions';
import { VIEW } from '../../../../../../../../constants/enums';
import { useSessionFetch } from '../../../../../../../../utils/fetch';
import { useQueryClient } from '@tanstack/react-query';

enum NewOpportunityTab {
  DETAILS,
  DETAILS_ENROLLMENT,
}

// not used to be removed
export function NewOpportunitiesPage() {
  const [selectedTab, setSelectedTab] = useState<NewOpportunityTab>(NewOpportunityTab.DETAILS);

  const { centers = [] } = useCentersQuery();

  const t = useTranslations();
  const router = useRouter();
  const { showToast } = useToast();

  const methods = useForm();

  const centersOptions = useMemo(
    () =>
      centers.map((c) => ({
        label: c,
        value: c,
      })),
    [centers]
  );

  const tabs = useMemo<SectionTab[]>(
    () => [
      {
        label: t('forms.subtab.details'),
        value: NewOpportunityTab.DETAILS,
      },
      {
        label: t('forms.subtab.detailsEnrollment'),
        value: NewOpportunityTab.DETAILS_ENROLLMENT,
      },
    ],
    [t]
  );

  const fetch = useSessionFetch();
  const queryClient = useQueryClient();

  const createOpportunityHandler = methods.handleSubmit(async (payload) => {
    try {
      await fetch('opportunities', {
        method: 'POST',
        body: JSON.stringify(payload),
      });

      await queryClient.refetchQueries({ queryKey: ['opportunitiesTable'] });

      showToast(t('dashboard.crm.opportunities.new.sucessMessage'), 'success');

      //router.push('/dashboard/crm/opportunities');
    } catch (err: any) {
      console.error(err);
      showToast(err.toString(), 'error');
    }
  });

  return (
    <SectionCardInternal
      addSpacer
      tabs={tabs}
      selectedTab={selectedTab}
      onSelectTab={setSelectedTab}
    >
      <ComposableFormWrapper
        footerSlot={<DefaultFooterActions view={VIEW.CREATE} />}
        columns={2}
        methods={methods}
        onSubmit={createOpportunityHandler}
      >
        {selectedTab === NewOpportunityTab.DETAILS && <NewDetailsForm />}
        {selectedTab === NewOpportunityTab.DETAILS_ENROLLMENT && (
          <EnrollmentForm centersOptions={centersOptions} />
        )}
      </ComposableFormWrapper>
    </SectionCardInternal>
  );
}
