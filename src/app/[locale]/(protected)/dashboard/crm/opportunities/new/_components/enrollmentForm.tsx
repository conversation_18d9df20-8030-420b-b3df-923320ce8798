import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { Search } from '@mui/icons-material';
import { ShiftField } from '../../../../../../../../components/form-builder/presets/shiftField';
import { BaseLearningFields } from '../../../../../../../../components/form-builder/presets/baseLearningFields';
import { useTranslations } from 'next-intl';

export function EnrollmentForm({
  centersOptions,
  readonly,
}: {
  centersOptions: FieldOption[];
  readonly?: boolean;
}) {
  const t = useTranslations('forms');
  return (
    <ComposableForm.Internal.Layout columns={2}>
      <ComposableForm.Field
        name="course"
        label={t('course.label')}
        placeholder={t('course.placeholder')}
        icon={<Search />}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="center"
        label={t('center.label')}
        placeholder={t('center.placeholder')}
        options={centersOptions}
        type="select"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="modality"
        label={t('modality.label')}
        placeholder={t('modality.placeholder')}
        type="select"
        readonly={readonly}
      />
      <ShiftField readonly={readonly} />
      <BaseLearningFields readonly={readonly} />
    </ComposableForm.Internal.Layout>
  );
}
