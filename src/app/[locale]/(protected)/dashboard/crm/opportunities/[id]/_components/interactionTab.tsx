'use client';

import { ColumnDef } from '@tanstack/react-table';
import DataTable from '../../../../../../../../components/ui/DataTable';
import { interactionMocks } from '../../../../../../../../lib/mocks/interactions';
import { InteractionTableType } from '../../../../../../../../schemas/interactionSchema';
import { useCallback, useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import RowActions from '../../../../../../../../components/ui/RowActions';
import { AddCircleRounded, Delete, Edit } from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { EditInteractionDialog, NewInteractionDialog } from './editInteraction';
import { Typography } from '@mui/material';
import IconButtonText from '@/components/form-builder/inputs/IconButtonText';

const columnNames = ['description', 'type', 'scheduledDate', 'doneDate', 'doneBy'];

export function InteractionTab({
  opportunityId,
  readonly,
}: {
  opportunityId: string;
  readonly?: boolean;
}) {
  const t = useTranslations('forms');
  const tTable = useTranslations('tables');
  const tMenu = useTranslations('menu');

  const [interactionToEdit, setInteractionToEdit] = useState<string | null>(null);
  const [newInteractionOpen, setNewInteractionOpen] = useState(false);

  const client = useQueryClient();

  const { mutate: deleteInteraction } = useMutation({
    mutationFn: async (interactionId: string) => {
      void interactionId;
      //todo delete interaction
    },
    onSuccess: (_, interactionId) => {
      client.invalidateQueries({
        queryKey: ['opportunities', opportunityId, 'interactions', interactionId],
      });
    },
  });

  const columns = useMemo<ColumnDef<InteractionTableType>[]>(
    () => [
      ...columnNames.map((n) => ({
        accessorKey: n,
        header: tTable(`columns.${n}`),
      })),

      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions
            lineId={row.original.id}
            actions={[
              {
                icon: Edit,
                label: tMenu('editInteraction'),
                onClick: () => setInteractionToEdit(row.original.id),
              },
              {
                icon: Delete,
                label: tMenu('deleteInteraction'),
                iconColor: 'error',
                color: 'error',
                onClick: () => deleteInteraction(row.original.id),
              },
            ]}
          />
        ),
      },
    ],
    [t]
  );

  const openNewDialog = useCallback(() => setNewInteractionOpen(true), []);

  return (
    <>
      {interactionMocks.length ? (
        <>
          <DataTable
            enableFilters={false}
            data={interactionMocks}
            columns={columns}
            onRowClick={() => null}
          />
          <IconButtonText
            sx={{ alignSelf: 'flex-start' }}
            field={{
              readonly: readonly,
              icon: <AddCircleRounded />,
              action: openNewDialog,
              actionLabel: t('interaction.add'),
            }}
          />
        </>
      ) : (
        <>
          <Typography>{t('noneExisting')}</Typography>
          <IconButtonText
            field={{
              readonly: readonly,
              icon: <AddCircleRounded />,
              action: openNewDialog,
              actionLabel: t('interaction.add'),
            }}
          />
        </>
      )}
      {interactionToEdit != null && (
        <EditInteractionDialog
          opportunityId={opportunityId}
          interactionId={interactionToEdit}
          onClose={() => setInteractionToEdit(null)}
        />
      )}
      {newInteractionOpen && (
        <NewInteractionDialog
          opportunityId={opportunityId}
          onClose={() => setNewInteractionOpen(false)}
        />
      )}
    </>
  );
}
