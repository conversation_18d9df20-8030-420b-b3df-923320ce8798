'use client';

import { interactionMocks } from '../../../../../../../../lib/mocks/interactions';
import FormBuilderDialog, {
  LoadingDialog,
} from '../../../../../../../../components/ui/dialog/FormBuilderDialog';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { useForm, useFormContext, useWatch } from 'react-hook-form';
import { DescriptionField } from '../../../../../../../../components/form-builder/presets/descriptionField';
import { useMutation, useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { InteractionTableType } from '../../../../../../../../schemas/interactionSchema';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { mapLabel } from '../../../../../../../../utils';
import {
  opportunityStateOptions,
  useInteractionStateOptions,
  useInteractionTypesOptions,
} from '../../../../../../../../schemas/opportunitySchema';
import { Suspense } from 'react';

const closingResultOptions: FieldOption[] = mapLabel(['success', 'insuccess']);

export function OpportunityInteractionFormFields({
  edit = false,
  loading = false,
}: {
  edit?: boolean;
  loading?: boolean;
}) {
  const { control } = useFormContext();

  const [opportunityState] = useWatch({
    control,
    name: ['opportunityState'],
  });

  const t = useTranslations('forms');
  const interactionTypesOptions = useInteractionTypesOptions();
  const interactionStates = useInteractionStateOptions();

  return (
    <>
      <DescriptionField loading={loading} />
      <ComposableForm.Field
        type="select"
        placeholder={t('type.placeholder')}
        label={t('type.label')}
        name={'type'}
        options={interactionTypesOptions}
        loading={loading}
      />
      <ComposableForm.Field
        type="select"
        options={interactionStates}
        placeholder={t('state.placeholder')}
        label={t('state.label')}
        name={'state'}
        loading={loading}
      />
      <ComposableForm.Field
        type="datetime"
        label={t('scheduledDate.label')}
        name={'scheduledDate'}
        loading={loading}
      />
      <ComposableForm.Field
        type="datetime"
        label={t('doneDate.label')}
        name={'doneDate'}
        loading={loading}
      />
      <ComposableForm.Field
        name={'text'}
        type={'text'}
        label={t('text.label')}
        placeholder={t('text.placeholder')}
        multiline
        loading={loading}
      />
      <ComposableForm.Field
        name={'opportunityState'}
        disableClearable={false}
        label={t('opportunityState.label')}
        placeholder={t('opportunityState.placeholder')}
        options={opportunityStateOptions}
        type={'select'}
        loading={loading}
      />
      {opportunityState === 'P2 + 3B' && (
        <>
          <ComposableForm.Field
            name={'closingResult'}
            label={t('closingResult.label')}
            placeholder={t('closingResult.placeholder')}
            options={closingResultOptions}
            type={'select'}
          />
          <ComposableForm.Field
            name={'closingDate'}
            label={t('closingDate.label')}
            type={'datetime'}
          />
          <ComposableForm.Field
            name={'closingDescription'}
            label={t('closingDescription.label')}
            type={'text'}
            multiline
          />
        </>
      )}
      {edit && (
        <>
          <ComposableForm.Field
            name={'doneBy'}
            label={t('doneBy.label')}
            type={'text'}
            loading={loading}
            disabled
          />
          <ComposableForm.Field
            name={'createdAt'}
            label={t('createdAt.label')}
            type={'text'}
            loading={loading}
            disabled
          />
        </>
      )}
    </>
  );
}

type NewInteractionDialogProps = {
  onClose: () => any;
  opportunityId: string;
};

function NewInteractionDialogInternal({ onClose, opportunityId }: NewInteractionDialogProps) {
  const methods = useForm<InteractionTableType>();

  const client = useQueryClient();
  const t = useTranslations('dialogs');
  const tMenu = useTranslations('menu');

  const { mutate: addInteraction } = useMutation({
    mutationFn: async (interactionInput: InteractionTableType) => {
      void interactionInput;
      //todo add interaction
    },
    onSuccess: () => {
      client.invalidateQueries({
        queryKey: ['opportunities', opportunityId, 'interactions'],
      });
    },
  });

  const onSubmit = methods.handleSubmit((input) => addInteraction(input));

  return (
    <ComposableForm.Provider methods={methods} columns={1}>
      <FormBuilderDialog
        confirmLabel={tMenu('add')}
        cancelLabel={tMenu('cancel')}
        open={true}
        onConfirm={() => onSubmit().then(onClose)}
        onCancel={() => {
          onClose();
        }}
        title={t('interactions.add')}
      >
        <OpportunityInteractionFormFields />
      </FormBuilderDialog>
    </ComposableForm.Provider>
  );
}

export function NewInteractionDialog(props: NewInteractionDialogProps) {
  const t = useTranslations('dialogs');

  return (
    <Suspense fallback={<LoadingDialog onCancel={props.onClose} title={t('interactions.add')} />}>
      <NewInteractionDialogInternal {...props} />
    </Suspense>
  );
}

type EditInteractionDialogProps = {
  interactionId: string;
  opportunityId: string;
  onClose: () => any;
};

function EditInteractionDialogInternal({
  interactionId,
  opportunityId,
  onClose,
}: EditInteractionDialogProps) {
  const t = useTranslations('dialogs');

  const client = useQueryClient();

  const { data } = useSuspenseQuery<InteractionTableType>({
    queryKey: ['opportunities', opportunityId, 'interactions', interactionId],
    queryFn: () => interactionMocks.find((i) => i.id == interactionId)!,
  });

  const { mutate: editInteraction } = useMutation({
    mutationFn: async (interactionInput: InteractionTableType) => {
      void interactionInput;
      //todo add interaction
    },
    onSuccess: () => {
      client.invalidateQueries({
        queryKey: ['opportunities', opportunityId, 'interactions', interactionId],
      });
    },
  });

  const methods = useForm<InteractionTableType>({
    defaultValues: data,
  });

  const onSubmit = methods.handleSubmit((input) => editInteraction(input));

  return (
    <ComposableForm.Provider methods={methods} columns={1}>
      <FormBuilderDialog
        open={true}
        onCancel={() => {
          onClose();
        }}
        onConfirm={() => {
          onSubmit();
          onClose();
        }}
        title={t('interactions.editDialog', {
          id: interactionId,
        })}
      >
        <OpportunityInteractionFormFields edit />
      </FormBuilderDialog>
    </ComposableForm.Provider>
  );
}

export function EditInteractionDialog(props: EditInteractionDialogProps) {
  const t = useTranslations('dialogs');

  return (
    <Suspense
      fallback={
        <LoadingDialog
          title={t('interactions.editDialog', {
            id: props.interactionId,
          })}
        />
      }
    >
      <EditInteractionDialogInternal {...props} />
    </Suspense>
  );
}