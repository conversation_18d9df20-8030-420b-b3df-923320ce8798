'use client';

import { SectionCardInternal } from '../../../../../../../../components/ui/SectionCard';
import { useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { opportunityEditSchema } from '../../../../../../../../schemas/opportunitySchema';
import { useToast } from '../../../../../../../../components/ui/ToastProvider';
import { EnrollmentForm } from '../../new/_components/enrollmentForm';
import { EditDetailsForm } from './editDetailsForm';
import { InteractionTab } from './interactionTab';
import { useQueryClient } from '@tanstack/react-query';
import { BOOL, VIEW } from '../../../../../../../../constants/enums';
import { useFilteredTabs } from '../../../../../../../../utils';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { useDynamicFormTabs, useTabsMethods } from '../../../../../../../../hooks/useTabs';
import { DefaultFooterActions } from '@/components/ui/form/FormActionsOptions';
import { DropdownOptions } from '@/components/ui/dropdown/Dropdown';

import SwapVerticalCircleIcon from '@mui/icons-material/SwapVerticalCircle';
import EditIcon from '@mui/icons-material/Edit';
import WarningIcon from '@mui/icons-material/Warning';
import DeleteIcon from '@mui/icons-material/Delete';
import { useOpportunity } from '../../../../../../../../lib/queries/opportunities';
import { useCentersQuery } from '../../../../../../../../lib/queries/centers';
import { useSessionFetch } from '../../../../../../../../utils/fetch';
import { useParams } from 'next/navigation';
import z from 'zod';

const EditOpportunityTab = {
  DETAILS: 'details',
  DETAILS_ENROLLMENT: 'detailsEnrollment',
  INTERACTION: 'interaction',
};

const ParamValidator = z.object({
  id: z.string(),
});

export function OpportunitiesClientPage({ defaultView }: { defaultView: VIEW }) {
  const t = useTranslations();
  const tForm = useTranslations('forms');
  const tMenu = useTranslations('menu');

  const { centers = [] } = useCentersQuery();

  const { id: opportunityId } = ParamValidator.parse(useParams());

  const opportunity = useOpportunity(opportunityId);

  const { showToast } = useToast();

  const { handleSubTabChange, handleChangeTab } = useTabsMethods();

  const { view, subTab, methods, readonly } = useDynamicFormTabs({
    initialData: opportunity,
    inputSchema: opportunityEditSchema,
    defaultView,
    defaultTab: EditOpportunityTab.DETAILS,
  });

  const centersOptions = useMemo(
    () =>
      centers.map((c) => ({
        label: c,
        value: c,
      })),
    [centers]
  );

  const tabs = useFilteredTabs({
    tabsObj: EditOpportunityTab,
    createTabValues: Object.values(EditOpportunityTab),
    view,
    t: tForm,
  });

  const fetch = useSessionFetch();
  const queryClient = useQueryClient();

  const editOpportunityHandler = methods.handleSubmit(async (payload) => {
    try {
      await fetch(`opportunities/${opportunityId}`, {
        method: 'PUT',
        body: JSON.stringify(payload),
      });

      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['opportunities', opportunityId] }),
        queryClient.refetchQueries({ queryKey: ['opportunitiesTable'] }),
      ]);

      showToast(t('dashboard.crm.opportunities.edit.sucessMessage'), 'success');
      await handleChangeTab({
        isStatic: BOOL.TRUE,
        id: '/dashboard/crm/opportunities',
        isReplace: true,
      });
    } catch (err: any) {
      console.error(err);
      showToast(err.toString(), 'error');
    }
  });

  const extraActions = useMemo(
    () => [
      {
        label: tMenu('manage'),
        menuOptions: (
          <>
            <DropdownOptions.SubOptions
              id="enrollment"
              xDirection="left"
              width={250}
              yBottomPoint="option"
              label={tMenu('enrollment')}
            >
              <DropdownOptions.Option
                id="changeStatus"
                label={tMenu('changeStatus')}
                onClick={() => alert('Change status clicked')}
                icon={SwapVerticalCircleIcon}
              />
              <DropdownOptions.Option
                id="editPayments"
                label={tMenu('editPayments')}
                onClick={() => alert('Change payments clicked')}
                icon={EditIcon}
              />
              <DropdownOptions.Option
                id="occurrence"
                label={tMenu('occurrence')}
                onClick={() => alert('Add occurence clicked')}
                icon={WarningIcon}
              />
              <DropdownOptions.Option
                id="delete"
                label={tMenu('deleteEnrollment')}
                onClick={() => alert('delete enrollment clicked')}
                icon={DeleteIcon}
                color="error"
                iconColor="error"
              />
            </DropdownOptions.SubOptions>
            <DropdownOptions.SubOptions
              id="print"
              xDirection="left"
              yBottomPoint="option"
              label={tMenu('print')}
            />
            <DropdownOptions.SubOptions
              id="payments"
              xDirection="left"
              yBottomPoint="option"
              label={tMenu('payments')}
            />
          </>
        ),
      },
    ],
    [tMenu]
  );

  return (
    <SectionCardInternal
      addSpacer
      tabs={tabs}
      selectedTab={subTab ?? EditOpportunityTab.DETAILS}
      onSelectTab={(tab) => {
        handleSubTabChange({ subTab: String(tab) });
      }}
    >
      <ComposableFormWrapper
        methods={methods}
        onSubmit={editOpportunityHandler}
        footerSlot={<DefaultFooterActions view={view} extraActions={extraActions} />}
      >
        {subTab === EditOpportunityTab.DETAILS && <EditDetailsForm />}
        {subTab === EditOpportunityTab.DETAILS_ENROLLMENT && (
          <EnrollmentForm centersOptions={centersOptions} readonly={readonly} />
        )}
        {subTab === EditOpportunityTab.INTERACTION && (
          <InteractionTab readonly={readonly} opportunityId={opportunityId} />
        )}
      </ComposableFormWrapper>
    </SectionCardInternal>
  );
}
