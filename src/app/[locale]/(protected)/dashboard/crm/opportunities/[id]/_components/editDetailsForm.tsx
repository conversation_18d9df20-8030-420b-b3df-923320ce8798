import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import {
  opportunityStateOptions,
  useOpportunityTypesOptions,
} from '../../../../../../../../schemas/opportunitySchema';
import { useTranslations } from 'next-intl';
import { ContactsField } from '../../../../../../../../components/form-builder/presets/contactsField';
import { EntitySelectField } from '../../../../../../../../components/form-builder/presets/entityField';
import { useSuspenseQuery } from '@tanstack/react-query';
import { Lead, mockLeads } from '../../../../../../../../lib/mocks/leads';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import { useMemo } from 'react';

export function EditDetailsForm() {
  const opportunityTypesOptions = useOpportunityTypesOptions();
  const t = useTranslations('forms');

  const { data: leads } = useSuspenseQuery<Lead[]>({
    queryKey: ['leads'],
    queryFn: () => Promise.resolve(mockLeads),
  });

  const leadOptions = useMemo<FieldOption[]>(
    () =>
      leads.map((l) => ({
        value: l.id,
        label: l.nome,
      })),
    [leads]
  );

  return (
    <ComposableForm.Internal.Layout columns={2}>
      <ComposableForm.Field
        name="description"
        label={t('description.label')}
        placeholder={t('description.placeholder')}
        required
        type="text"
      />
      <ContactsField name={'userId'} required />
      <ComposableForm.Field
        name="type"
        label={t('type.label')}
        placeholder={t('type.placeholder')}
        required
        options={opportunityTypesOptions}
        type="select"
      />
      <EntitySelectField name={'entity'} />
      <ContactsField
        name={'assignedTo'}
        label={t('assignedTo.label')}
        placeholder={t('assignedTo.placeholder')}
      />
      <ComposableForm.Field
        name="status"
        label={t('status.label')}
        placeholder={t('status.placeholder')}
        options={opportunityStateOptions}
        type="select"
      />
      <ComposableForm.Field name="statusDate" label={t('statusDate.label')} type="date" />
      <ComposableForm.Field name="closingDate" label={t('closingDate.label')} type="date" />
      <ComposableForm.Field
        name="closingStatus"
        label={t('closingStatus.label')}
        type="select"
        options={opportunityStateOptions}
      />
      <ComposableForm.Field name="closingReason" label={t('closingReason.label')} type="text" />
      <ComposableForm.Field
        name="closingDescription"
        label={t('closingDescription.label')}
        type="text"
        multiline
        colSpan={12}
      />
      <ComposableForm.Field name="createdBy" label={t('createdBy.label')} type="text" readonly />
      <ComposableForm.Field name="createdAt" label={t('createdAt.label')} type="date" readonly />
      <ComposableForm.Field
        name="sourceLead"
        label={t('sourceLead.label')}
        type="select"
        options={leadOptions}
      />
      <ComposableForm.Field
        name="observations"
        label={t('observations.label')}
        placeholder={t('observations.placeholder')}
        type="text"
        multiline
        colSpan={12}
      />
    </ComposableForm.Internal.Layout>
  );
}
