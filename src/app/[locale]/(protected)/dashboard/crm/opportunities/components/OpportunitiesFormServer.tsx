'use server';

import { fetchCenters, fetchContactPeople } from '../../../../../../../lib/api/contactPeople';
import { fetchEmployerEntities } from '../../../../../../../lib/api/entities';
import { mockLeads } from '../../../../../../../lib/mocks/leads';
import { NewOpportunitiesPage } from '../new/_components/newOpportunitiesClient';
import { getQueryClient } from '../../../../../../../utils';
import { RQHydrate } from '../../../../../../../lib/RQHydrate';
import { Suspense } from 'react';
import { mockOpportunities } from '../../../../../../../lib/mocks/opportunities';

export async function OpportunitiesFormServer({
  opportunityId,
}: {
  readOnly?: boolean;
  opportunityId?: string;
}) {
  const isNew = !opportunityId;

  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: ['centers'],
    queryFn: fetchCenters,
  });

  await queryClient.prefetchQuery({
    queryKey: ['contactUsers'],
    queryFn: fetchContactPeople,
  });

  await queryClient.prefetchQuery({
    queryKey: ['entitiesOptions'],
    queryFn: fetchEmployerEntities,
  });

  await queryClient.prefetchQuery({
    queryKey: ['leads'],
    queryFn: () => Promise.resolve(mockLeads),
  });

  if (opportunityId) {
    await queryClient.prefetchQuery({
      queryKey: ['opportunities', opportunityId],
      queryFn: () => Promise.resolve(mockOpportunities.find((o) => o.id == opportunityId)),
    });
  }

  return (
    <RQHydrate>
      <Suspense>{isNew ? <NewOpportunitiesPage /> : <></>}</Suspense>
    </RQHydrate>
  );
}
