'use client';

import { useMemo, useState } from 'react';
import DataTable, { defaultPaginationParams } from '@/components/ui/DataTable';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import StatusChip, { Status } from '@/components/ui/StatusChip';
import RowActions, { ActionItem } from '@/components/ui/RowActions';

import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';

import { TABNAV } from '@/constants/enums';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { useTranslations } from 'next-intl';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import { Delete } from '@mui/icons-material';
import { Opportunity } from '../../../../../../../../schemas/opportunitySchema';
import { useOpportunities } from '../../../../../../../../lib/queries/opportunities';
import { useQueryClient } from '@tanstack/react-query';
import { useSessionFetch } from '../../../../../../../../utils/fetch';

export default function OpportunitiesTable() {
  const queryClient = useQueryClient();

  useTabsStaticInitializer();
  const { handleChangeTab } = useTabsMethods();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedOpp, setSelectedOpp] = useState<Opportunity | null>(null);
  const tMenu = useTranslations('menu');
  const tDialog = useTranslations('dialogs.opportunities');
  const [pagination, setPagination] = useState<PaginationState>(() => defaultPaginationParams);

  const { rows: opportunities, total } = useOpportunities(pagination);

  const onDeleteAction = (op: Opportunity) => {
    setSelectedOpp(op);
    setOpenDeleteDialog(true);
  };

  const fetch = useSessionFetch();

  const getOpportunityActions = (op: Opportunity): ActionItem[] => [
    {
      label: tMenu('view'),
      icon: VisibilityIcon,
      onClick: () =>
        handleChangeTab({
          id: `/dashboard/crm/${TABNAV.OPPORTUNITIES}/${op.id}`,
          title: op.description,
        }),
    },
    {
      label: tMenu('delete'),
      icon: DeleteIcon,
      color: 'error',
      iconColor: 'error',
      onClick: () => onDeleteAction(op),
    },
  ];

  // lógica de exclusão
  const handleDeleteConfirm = async () => {
    if (!selectedOpp) return;
    setOpenDeleteDialog(false);

    await fetch(`opportunities/${selectedOpp.id}`, {
      method: 'DELETE',
    });

    await queryClient.invalidateQueries({ queryKey: ['opportunitiesTable'] });
  };

  const baseColumns = useMemo<ColumnDef<Opportunity>[]>(
    () => [
      { accessorKey: 'id', header: 'ID', meta: { filterType: 'text' }, minSize: 128 },
      {
        accessorKey: 'status',
        header: 'Estado',
        minSize: 128,
        meta: {
          filterType: 'select',
          options: [
            { value: 'new', label: 'Nova' },
            { value: 'assigned', label: 'Atribuída' },
            { value: 'Em qualificação', label: 'Em qualificação' },
            { value: 'Fechada c/ sucesso', label: 'Fechada c/ sucesso' },
            { value: 'Fechada s/ sucesso', label: 'Fechada s/ sucesso' },
            { value: 'Em validação', label: 'Em validação' },
            { value: 'cancelled', label: 'Anulada' },
            { value: 'inactive', label: 'Inativa' },
            { value: 'active', label: 'Ativa' },
          ],
        },
        cell: (ctx) => <StatusChip status={ctx.getValue<Status>()} />,
      },
      {
        accessorKey: 'createdAt',
        header: 'Data criação',
        meta: { filterType: 'date' },
        minSize: 128,
        cell: (ctx) =>
          new Date(ctx.getValue<string>()).toLocaleString('pt-PT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }),
        filterFn: (row, id, filterValue: [Date | null, Date | null]) => {
          if (!Array.isArray(filterValue)) return true;
          const [start, end] = filterValue;
          const rowDate = new Date(row.getValue<string>(id));
          if (start && end) {
            return (
              rowDate.setHours(0, 0, 0, 0) >= start.setHours(0, 0, 0, 0) &&
              rowDate.setHours(0, 0, 0, 0) <= end.setHours(0, 0, 0, 0)
            );
          }
          if (start) return rowDate.setHours(0, 0, 0, 0) >= start.setHours(0, 0, 0, 0);
          if (end) return rowDate.setHours(0, 0, 0, 0) <= end.setHours(0, 0, 0, 0);
          return true;
        },
      },
      {
        accessorKey: 'description',
        header: 'Descrição',
        meta: { filterType: 'text' },
        minSize: 128,
      },
      { accessorKey: 'userId', header: 'Contacto', meta: { filterType: 'text' }, minSize: 128 },
      {
        accessorKey: 'center',
        header: 'Polo atribuição',
        meta: { filterType: 'text' },
        minSize: 128,
      },
      { accessorKey: 'payment', header: 'Pagamento', meta: { filterType: 'text' }, size: 128 },
      { accessorKey: 'assignedTo', header: 'Atribuído a', meta: { filterType: 'text' }, size: 128 },
    ],
    []
  );

  const columns = useMemo<ColumnDef<Opportunity>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions lineId={row.original.id} actions={getOpportunityActions(row.original)} />
        ),
      },
    ],
    [baseColumns]
  );

  const handleRowClick = (row: Opportunity) => {
    console.info('clicou em', row);
  };

  return (
    <>
      <DataTable<Opportunity>
        data={opportunities}
        total={total}
        columns={columns}
        onRowClick={handleRowClick}
        pagination={pagination}
        onPaginationChange={setPagination}
        enableFilters
        showTabs
      />
      <ConfirmDialog
        open={openDeleteDialog}
        onCancel={() => setOpenDeleteDialog(false)}
        title={tDialog('delete.title')}
        subtitle={tDialog('delete.subtitle')}
        description={tDialog('delete.description')}
        onConfirm={handleDeleteConfirm}
        confirmLabel={tDialog('delete.delete')}
        cancelLabel={tDialog('delete.cancel')}
        confirmColor="error"
        confirmIcon={<Delete />}
      />
    </>
  );
}
