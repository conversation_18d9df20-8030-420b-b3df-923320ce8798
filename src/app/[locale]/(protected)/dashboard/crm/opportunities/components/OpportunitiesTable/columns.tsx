import { OpportunitiesObject } from '@/lib/api/opportunities';
import { ColumnDef } from '../../../../profiles/types';

export const opportunitiesColumns: ColumnDef<OpportunitiesObject>[] = [
  { key: 'id', label: 'ID', align: 'left' },
  { key: 'state', label: 'Estado', align: 'center' },
  { key: 'description', label: 'Descrição', align: 'center' },
  { key: 'type', label: 'Tipo', align: 'center' },
  { key: 'contactInfo', label: 'Contacto', align: 'center' },
  { key: 'entity', label: 'Entidade', align: 'center' },
  { key: 'assignedTo', label: 'Atribuido a', align: 'center' },
];