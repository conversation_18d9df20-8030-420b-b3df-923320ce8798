'use client';

import { useState } from 'react';
import ConfirmDialog from '@/components/ui/ConfirmDialog';
import type { ProfileTableRow } from '@/lib/api/profiles';
import ProfilesTable from './components/ProfilesTable';
import { columns } from './components/ProfilesTable/columns';
import { VIEW } from '@/constants/enums';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { Delete, FileCopy } from '@mui/icons-material';
import { useTranslations } from 'next-intl';

type PendingAction = {
  id: string;
  type: 'duplicate' | 'delete';
};

export default function ProfilesClient({ initialRows }: { initialRows: ProfileTableRow[] }) {
  useTabsStaticInitializer();
  const t = useTranslations('dialogs.profiles');
  const tMenu = useTranslations('menu');
  const [pendingAction, setPendingAction] = useState<PendingAction | null>(null);
  const { handleChangeTab } = useTabsMethods();

  const handleEdit = (id: string) => {
    handleChangeTab({
      id: `/dashboard/profiles/${id}`,
      title: `Perfil ${id}`,
      view: VIEW.EDIT,
    });
  };

  const handleDuplicate = (id: string) => {
    setPendingAction({ id, type: 'duplicate' });
  };

  const handleDelete = (id: string) => {
    setPendingAction({ id, type: 'delete' });
  };

  const handleCancel = () => {
    setPendingAction(null);
  };

  const handleConfirm = () => {
    if (!pendingAction) return;
    console.log(pendingAction.type, pendingAction.id);
    //TODO: Action de duplicate/delete
    setPendingAction(null);
  };

  return (
    <>
      <ProfilesTable
        rows={initialRows}
        onEditAction={handleEdit}
        onDuplicateAction={handleDuplicate}
        onDeleteAction={handleDelete}
        columns={columns}
      />

      <ConfirmDialog
        open={!!pendingAction}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        maxWidth="sm"
        title={pendingAction?.type === 'delete' ? t('delete.title') : t('duplicate.title')}
        subtitle={pendingAction?.type === 'delete' ? t('delete.subtitle') : t('duplicate.subtitle')}
        description={
          pendingAction?.type === 'delete' ? t('delete.description') : t('duplicate.description')
        }
        cancelLabel={tMenu('cancel')}
        confirmLabel={pendingAction?.type === 'delete' ? tMenu('delete') : tMenu('duplicate')}
        confirmColor={pendingAction?.type === 'delete' ? 'error' : 'primary'}
        confirmIcon={pendingAction?.type === 'delete' ? <Delete /> : <FileCopy />}
      />
    </>
  );
}
