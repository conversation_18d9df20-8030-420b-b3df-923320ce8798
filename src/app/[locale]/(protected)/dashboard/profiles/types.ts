export enum Level {
  none = 'none',
  own = 'own',
  team = 'team',
  all = 'all',
}

export interface PermissionAction {
  id: string;
  label: string;
  level: Level;
}

export interface PermissionSection {
  id: string;
  label: string;
  checked: boolean;
  actions: PermissionAction[];
}

export interface PermissionModule {
  id: string;
  label: string;
  checked: boolean;
  sections: PermissionSection[];
}

export interface ProfileFormValues {
  description: string;
  modules: PermissionModule[];
}
export type ColumnDef<Row> = {
  /** chave do row: por ex. 'name' ou 'modules' */
  key: keyof Row;
  /** rótulo a aparecer no header */
  label: string;
  /** alinhamento da coluna */
  align?: 'left' | 'center' | 'right';
  /** opcionalmente, uma função de formatação de cell */
  render?: (value: Row[keyof Row], row: Row) => React.ReactNode;
};
