import { fetchProfiles } from '@/lib/api/profiles';
import { notFound } from 'next/navigation';
import ProfileForm from '../components/ProfileForm';
import { PageProps } from '../../../../../../types';

export default async function EditProfilePage({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const profile = (await fetchProfiles()).find((p) => p.id === urlParams.id);
  if (!profile) return notFound();

  return <ProfileForm mode="edit" initial={profile} />;
}
