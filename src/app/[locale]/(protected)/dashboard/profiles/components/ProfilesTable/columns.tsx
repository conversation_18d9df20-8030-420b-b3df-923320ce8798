import { ProfileTableRow } from '@/lib/api/profiles';
import { CheckCircleRounded as CheckIcon, Remove as RemoveIcon } from '@mui/icons-material';
import { ColumnDef } from '../../types';

export const columns: ColumnDef<ProfileTableRow>[] = [
  { key: 'name', label: 'Perfil', align: 'left' },

  {
    key: 'modules',
    label: '<PERSON>ódu<PERSON>',
    align: 'center',

    render: (value) => {
      if (typeof value === 'boolean') {
        return value ? (
          <CheckIcon fontSize="small" color="action" />
        ) : (
          <RemoveIcon fontSize="small" color="disabled" />
        );
      }

      return String(value);
    },
  },

  {
    key: 'management',
    label: 'Gestão',
    align: 'center',
    render: (value) => {
      if (typeof value === 'boolean') {
        return value ? (
          <CheckIcon fontSize="small" color="action" />
        ) : (
          <RemoveIcon fontSize="small" color="disabled" />
        );
      }
      return String(value);
    },
  },

  {
    key: 'config',
    label: 'Configurações',
    align: 'center',
    render: (value) => {
      if (typeof value === 'boolean') {
        return value ? (
          <CheckIcon fontSize="small" color="action" />
        ) : (
          <RemoveIcon fontSize="small" color="disabled" />
        );
      }
      return String(value);
    },
  },
];
