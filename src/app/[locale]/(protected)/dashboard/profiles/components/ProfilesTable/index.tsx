'use client';
import type { ProfileTableRow } from '@/lib/api/profiles';
import { ColumnDef } from '../../types';
import TableGeneric from '@/components/ui/TableGeneric';

export type ProfilesTableProps = {
  rows: ProfileTableRow[];
  columns?: readonly ColumnDef<ProfileTableRow>[];
  onEditAction: (id: string) => void;
  onDuplicateAction: (id: string) => void;
  onDeleteAction: (id: string) => void;
};

export default function ProfilesTable({
  rows,
  columns,
  onEditAction,
  onDuplicateAction,
  onDeleteAction,
}: ProfilesTableProps) {
  return (
    <TableGeneric
      rows={rows}
      columns={columns}
      actions={[
        {
          label: 'Editar perfil',
          icon: 'edit',
          onClick: onEditAction,
        },
        {
          label: 'Duplicar perfil',
          icon: 'copy',
          onClick: onDuplicateAction,
        },
        {
          label: 'Eliminar perfil',
          icon: 'delete',
          onClick: onDeleteAction,
          color: 'error',
        },
      ]}
      pagination
    />
  );
}