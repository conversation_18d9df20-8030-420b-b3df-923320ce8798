import { ProfileRow, ProfileTableRow } from '@/lib/api/profiles';
import { PermissionCode } from '@/lib/auth/permissions';

const isAllowed = (perm: ProfileRow['permissions'], code: PermissionCode) =>
  perm === '*' ||
  ((perm as Partial<Record<PermissionCode, string | undefined>>)[code] !== undefined &&
    (perm as Partial<Record<PermissionCode, string | undefined>>)[code] !== 'none');

export function toTableRow(r: ProfileRow): ProfileTableRow {
  return {
    id: r.id,
    name: r.name,
    modules: isAllowed(r.permissions, 'crm.leads.ver'),
    management: isAllowed(r.permissions, 'crm.matriculas.ver'),
    config: isAllowed(r.permissions, 'crm.matriculas.apagar'),
  };
}
