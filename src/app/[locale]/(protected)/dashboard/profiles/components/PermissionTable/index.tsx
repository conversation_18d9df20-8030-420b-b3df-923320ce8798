'use client';

import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Controller, useFormContext } from 'react-hook-form';
import PermissionSelect from './PermissionSelect';
import type { PermissionCode, PermissionLevel } from '@/lib/api/profiles';

export type PermissionRowDef = { label: string; code: PermissionCode };

type Props = {
  rows: PermissionRowDef[];
};

export default function PermissionTable({ rows }: Props) {
  const { control } = useFormContext<Record<PermissionCode, PermissionLevel>>();

  return (
    <TableContainer
      component={Paper}
      elevation={0}
      sx={{
        border: (t) => `1px solid ${t.palette.divider}`,
        borderRadius: 1,
        overflowX: 'auto',
        width: '95%',
        mx: 'auto',
      }}
    >
      <Table
        size="small"
        sx={{
          width: '100%',
        }}
      >
        <TableHead>
          <TableRow>
            <TableCell
              sx={{
                fontWeight: 600,
                width: '87%',
              }}
            >
              Ações
            </TableCell>
            <TableCell
              sx={{
                fontWeight: 600,
                width: '13%',
              }}
            >
              <Stack direction="row" alignItems="center" gap={0.5}>
                Nível de Permissão
                <Tooltip
                  title={
                    <>
                      <Typography variant="caption" display="block">
                        <strong>Sem acesso</strong>: não pode realizar a ação
                      </Typography>
                      <Typography variant="caption" display="block">
                        <strong>Próprio</strong>: apenas sobre si
                      </Typography>
                      <Typography variant="caption" display="block">
                        <strong>Equipa</strong>: sobre a sua equipa
                      </Typography>
                      <Typography variant="caption" display="block">
                        <strong>Todos</strong>: qualquer utilizador
                      </Typography>
                    </>
                  }
                >
                  <InfoOutlinedIcon fontSize="inherit" />
                </Tooltip>
              </Stack>
            </TableCell>
          </TableRow>
        </TableHead>

        <TableBody>
          {rows.map(({ label, code }) => (
            <TableRow hover key={code}>
              <TableCell>
                <Typography variant="body2">{label}</Typography>
              </TableCell>

              <TableCell align="right">
                <Controller
                  name={code}
                  control={control}
                  render={({ field }) => (
                    <PermissionSelect
                      value={field.value as PermissionLevel}
                      onChange={field.onChange}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}