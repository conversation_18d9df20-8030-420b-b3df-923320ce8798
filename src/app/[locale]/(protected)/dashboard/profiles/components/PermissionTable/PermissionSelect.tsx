'use client';

import { MenuItem, Select, SelectChangeEvent, Typography } from '@mui/material';
import { PERM_LEVELS, PermLevel } from './levels';

type Props = {
  value: PermLevel;
  onChange: (val: PermLevel) => void;
};

export default function PermissionSelect({ value, onChange }: Props) {
  return (
    <Select
      size="small"
      value={value}
      onChange={(e: SelectChangeEvent) => onChange(e.target.value as PermLevel)}
      sx={{
        minWidth: 160,
        '& .MuiSelect-select': { display: 'flex', alignItems: 'center', gap: 1 },
      }}
    >
      {PERM_LEVELS.map(({ value, label, icon: Icon }) => (
        <MenuItem
          key={value}
          value={value}
          sx={{ display: 'flex', alignItems: 'center', gap: 1, px: 2 }}
        >
          <Icon fontSize="small" />
          <Typography variant="body2">{label}</Typography>
        </MenuItem>
      ))}
    </Select>
  );
}
