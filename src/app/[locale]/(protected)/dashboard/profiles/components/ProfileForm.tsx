'use client';

import { usePathname, useRouter } from 'next/navigation';
import { Controller, useForm } from 'react-hook-form';

import LabeledTextarea from '@/components/ui/LabeledTextarea';

import type { PermissionCode, PermissionLevel, ProfileRow } from '@/lib/api/profiles';
import { permissionSections } from '@/lib/mocks/permissionSections.mock';
import PermissionAccordionField from '@/components/form-builder/inputs/PermissionAccordionField';
import { useTranslations } from 'next-intl';
import { pathWithoutLocaleAndId } from '@/utils';
import { VIEW } from '@/constants/enums';
import { useEffect } from 'react';
import { useTabsDynamicInitializer, useTabsMethods, useTabsState } from '@/hooks/useTabs';
import { SectionCardHolder } from '@/components/ui/SectionCardHolder';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import TitleDividerCard from '@/components/ui/TitleDividerCard';
import { useDefaultActions } from '@/components/ui/form/FormActionsOptions';

type Props = { mode: 'create' | 'edit'; initial?: ProfileRow | null };

// TODO: old way to initilizate tab, change to new, needs GEOVANNA bc of way of funcitoing
export default function ProfileForm({ mode, initial }: Props) {
  const pathName = pathWithoutLocaleAndId(usePathname());
  // 1. need id here
  const tabId = `${pathName}/${initial?.id}`;

  const { formContext } = useTabsState();
  const { setFormGetValues } = useTabsMethods();
  useTabsDynamicInitializer({
    initial,
    location: tabId,
    view: VIEW.EDIT,
  });
  // 2. see if unsaved changes exist
  const defaultValuesUnsaved = formContext?.[tabId!];

  const t = useTranslations('forms');
  const allPermissionCodes = permissionSections.flatMap((s) =>
    s.groups.flatMap((g) => g.features.flatMap((f) => f.rows.map((r) => r.code)))
  );

  const defaultPermissionValues = Object.fromEntries(
    allPermissionCodes.map((c) => [c, 'none'] as const)
  );
  const router = useRouter();

  type FormValues = {
    description: string;
  } & Partial<Record<PermissionCode, PermissionLevel>>;

  const methods = useForm<FormValues>({
    defaultValues: defaultValuesUnsaved ?? {
      description: initial?.name ?? '',
      ...defaultPermissionValues,
      ...(initial?.permissions !== '*' ? initial?.permissions : {}),
    },
  });
  const {
    handleSubmit,
    formState: { isSubmitting },
    getValues,
  } = methods;

  const onReset = () => {
    methods.reset({
      description: initial?.name ?? '',
      ...defaultPermissionValues,
      ...(initial?.permissions !== '*' ? initial?.permissions : {}),
    });
  };

  const onSubmit = async (data: FormValues) => {
    const payload = {
      description: data.description,
      permissions: {
        modules: data['crm.leads.apagar'],
        management: data['crm.leads.apagar'],
        config: data['crm.leads.apagar'],
      },
    };

    if (mode === 'create') {
      await fetch('/api/profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    } else {
      await fetch(`/api/profiles/${initial!.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    }

    router.push('/dashboard/profiles');
  };

  useEffect(() => {
    // 4. on attach, save id and getValues to run getValues only on tab exiting
    // only one variable holds the value for every form (id + getValues callback), when changing tabs, this sets the needed value and clears the field
    setFormGetValues({ id: tabId!, getValues: () => getValues() });
  }, []);

  const formActions = useDefaultActions({ view: VIEW.EDIT, formState: methods.formState });

  return (
    <ComposableFormWrapper
      methods={methods}
      onReset={onReset}
      onSubmit={handleSubmit(onSubmit)}
      actions={formActions}
    >
      <SectionCardHolder addSpacer>
        <TitleDividerCard title={t('subtab.profileDataTitle')}>
          <Controller
            name="description"
            rules={{ required: true }}
            render={({ field, fieldState }) => (
              <LabeledTextarea
                {...field}
                label={`${t('subtab.labelTextArea')}*`}
                rows={1}
                error={!!fieldState.error}
              />
            )}
          />
        </TitleDividerCard>

        <TitleDividerCard title={t('subtab.accordionTitle')}>
          <PermissionAccordionField
            permissionSections={permissionSections}
            disabled={isSubmitting}
          />
        </TitleDividerCard>
      </SectionCardHolder>
    </ComposableFormWrapper>
  );
}
