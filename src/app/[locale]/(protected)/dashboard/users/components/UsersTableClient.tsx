'use client';

import { useMemo, useState } from 'react';
import DataTable from '@/components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { User } from '@/lib/mocks/users';
import StatusChip, { Status } from '@/components/ui/StatusChip';
import RowActions, { ActionItem } from '@/components/ui/RowActions';
import { TABNAV } from '@/constants/enums';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ReplayIcon from '@mui/icons-material/Replay';
import AddHomeIcon from '@mui/icons-material/AddHome';
import HubsModal, { HubsModalProps } from './HubsModal';
import ActionModal, { ActionModalProps } from './ActionModal';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { useTranslations } from 'next-intl';

export type FilterMeta =
  | { filterType: 'text' }
  | { filterType: 'select'; options: { value: string; label: string }[] }
  | { filterType: 'date' };

interface UsersTableClientProps {
  users: User[];
}

export default function UsersTableClient({ users }: UsersTableClientProps) {
  const [hubsModalPayload, setHubsModalPayload] = useState<HubsModalProps | null>(null);
  const [actionModalPayload, setActionModalPayload] = useState<ActionModalProps | null>(null);
  const { handleChangeTab } = useTabsMethods();
  const tTables = useTranslations('tables.columns');
  const tMenu = useTranslations('menu');
  useTabsStaticInitializer();

  const buildHubsModalPayload = (props: HubsModalProps) => {
    setHubsModalPayload({ ...props, isOpen: true });
  };

  const buildActionModalPayload = (props: ActionModalProps) => {
    setActionModalPayload({ ...props, isOpen: true });
  };

  function buildHubsModalProps(user: User) {
    buildHubsModalPayload({
      user,
      isOpen: true,
      handleCancel: () => setHubsModalPayload(null),
      handleConfirm: () => {
        console.log('Confirmed', user);
        setHubsModalPayload(null);
      },
    });
  }

  function buildActionModalProps(user: User, type: 'resetPasswordDialog' | 'deleteDialog') {
    buildActionModalPayload({
      user,
      isOpen: true,
      type: type,
      handleClose: () => setActionModalPayload(null),
    });
  }

  const getUserActions = (user: User): ActionItem[] => [
    {
      label: tMenu('editUser'),
      icon: EditIcon,
      onClick: () => handleChangeTab({ id: `/dashboard/users/${user.id}`, title: user.name }),
    },
    {
      label: tMenu('assignHubs'),
      icon: AddHomeIcon,
      onClick: () => buildHubsModalProps(user),
    },
    {
      label: tMenu('resetPassword'),
      icon: ReplayIcon,
      onClick: () => buildActionModalProps(user, 'resetPasswordDialog'),
    },
    {
      label: tMenu('deleteUser'),
      icon: DeleteIcon,
      color: 'error',
      iconColor: 'error',
      onClick: () => buildActionModalProps(user, 'deleteDialog'),
    },
  ];

  const baseColumns = useMemo<ColumnDef<User>[]>(
    () => [
      { accessorKey: 'id', header: tTables('id'), meta: { filterType: 'text' }, minSize: 128 },
      {
        accessorKey: 'name',
        header: 'Nome',
        minSize: 128,
        meta: {
          filterType: 'text',
        },
      },
      {
        accessorKey: 'username',
        header: tTables('username'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'email',
        header: tTables('email'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'profile',
        header: tTables('profile'),
        minSize: 128,
        filterFn: 'equals',
        meta: {
          filterType: 'select',
          options: [
            { value: 'Administrador', label: 'Administrador' },
            { value: 'Supervisor Comercial', label: 'Supervisor Comercial' },
          ],
        },
      },
      {
        accessorKey: 'status',
        header: tTables('status'),
        minSize: 128,
        filterFn: 'equals',
        meta: {
          filterType: 'select',
          options: [
            { value: 'Ativo', label: 'Ativo' },
            { value: 'Inativo', label: 'Inativo' },
          ],
        },

        cell: (ctx) => <StatusChip status={ctx.getValue<Status>()} />,
      },
    ],
    []
  );

  const columns = useMemo<ColumnDef<User>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions lineId={row.original.id} actions={getUserActions(row.original)} />
        ),
      },
    ],
    [baseColumns]
  );

  const handleRowClick = (row: User) => {
    console.info('clicou em', row);
  };

  return (
    <>
      <DataTable<User>
        data={users}
        columns={columns}
        onRowClick={handleRowClick}
        enableFilters
        pageType={TABNAV.USERS}
        showTabs
      />
      {hubsModalPayload?.isOpen && (
        <HubsModal
          user={hubsModalPayload.user}
          isOpen={hubsModalPayload.isOpen}
          handleCancel={hubsModalPayload.handleCancel}
          handleConfirm={hubsModalPayload.handleConfirm}
        />
      )}
      {actionModalPayload?.isOpen && (
        <ActionModal
          user={actionModalPayload.user}
          isOpen={actionModalPayload.isOpen}
          type={actionModalPayload.type}
          handleClose={actionModalPayload.handleClose}
        />
      )}
    </>
  );
}
