'use client';

import FormBuilderDialog from '@/components/ui/dialog/FormBuilderDialog';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { User } from '@/lib/mocks/users';
import { Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import DeleteIcon from '@mui/icons-material/Delete';
import ReplayIcon from '@mui/icons-material/Replay';

export interface ActionModalProps {
  user: User;
  isOpen: boolean;
  type?: 'resetPasswordDialog' | 'deleteDialog';
  handleClose: () => void;
}

export default function ActionModal({ user, isOpen, type, handleClose }: ActionModalProps) {
  const methods = useForm({
    defaultValues: user,
  });

  const t = useTranslations('dialogs.users');

  const dialogContent = {
    handleCancel: () => handleClose(),
    handleConfirm: () => handleConfirm(),
    confirmIcon: type === 'deleteDialog' ? <DeleteIcon /> : <ReplayIcon />,
    confirmColor: type === 'deleteDialog' ? 'error' : 'primary',
    title: t(`${type}.title`),
    confirmLabel: t(`${type}.confirmLabel`),
    confirmText: t(`${type}.confirmText`),
    instructionText: t(`${type}.instructionText`),
    cancelLabel: t(`${type}.cancelLabel`),
  };

  function onSubmit() {}
  function onReset() {}

  function handleConfirm() {
    if (type === 'deleteDialog') {
      //logica para apagar
    } else {
      //logica para reset da pass
    }
    handleClose();
  }
  return (
    <ComposableFormWrapper methods={methods} onReset={onReset} onSubmit={onSubmit} footer={false}>
      <FormBuilderDialog
        open={isOpen}
        onConfirm={dialogContent.handleConfirm}
        onCancel={dialogContent.handleCancel}
        title={dialogContent.title}
        confirmLabel={dialogContent.confirmLabel}
        confirmIcon={dialogContent.confirmIcon}
        cancelLabel={dialogContent.cancelLabel}
        confirmColor={
          dialogContent.confirmColor as
            | 'primary'
            | 'secondary'
            | 'error'
            | 'info'
            | 'success'
            | 'warning'
        }
        maxWidth="sm"
      >
        <Stack spacing={1}>
          <Typography variant="body1">{dialogContent.confirmText}</Typography>
          <Typography variant="body2">{dialogContent.instructionText}</Typography>
        </Stack>
      </FormBuilderDialog>
    </ComposableFormWrapper>
  );
}
