import { Stack } from '@mui/material';
import UserDetailsFormClient from '../../components/UserDetailsFormClient';
import { User } from '@/lib/mocks/users';
import { PageProps } from '@/types';

export const dynamic = 'force-dynamic';

export default async function UserDetailsPage({ params }: PageProps<{ id: string }>) {
  const { id } = await params;
  const user: User = {
    id: id,
  };

  return (
    <Stack direction="column" gap={3}>
      <UserDetailsFormClient user={user} />
    </Stack>
  );
}
