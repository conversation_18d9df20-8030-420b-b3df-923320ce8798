import {
  fetchContactPeopleOptions,
  fetchContactPeople,
  fetchFileTypes,
} from '@/lib/api/contactPeople';
import { notFound } from 'next/navigation';
import { VIEW } from '@/constants/enums';
import { fetchEmployerEntities } from '@/lib/api/entities';
import { PageProps } from '@/types';
import ContactPerson from '../components/ContactPersonClientPage';

export default async function ContactPersonPage({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const people = await fetchContactPeople();
  const contactPerson = people.find((p) => p.id === urlParams.id);
  const options = await fetchContactPeopleOptions();
  options.users = people.map((user) => ({ value: user.id, label: user.name || '' }));
  options.fileTypes = await fetchFileTypes();
  options.employer = await fetchEmployerEntities();
  // if id starts with new, it should continue because its a new contact user
  if (!contactPerson) return notFound();

  return <ContactPerson defaultView={VIEW.VIEW} initial={contactPerson} selectOptions={options} />;
}
