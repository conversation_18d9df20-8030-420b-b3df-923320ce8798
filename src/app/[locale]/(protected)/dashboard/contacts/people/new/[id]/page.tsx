import {
  fetchContactPeopleOptions,
  fetchContactPeople,
  fetchFileTypes,
} from '@/lib/api/contactPeople';
import { VIEW } from '@/constants/enums';
import { fetchEmployerEntities } from '@/lib/api/entities';
import { PageProps } from '../../../../../../../../types';
import ContactPerson from '../../components/ContactPersonClientPage';

export default async function ContactPersonPage({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const person = await fetchContactPeople();
  const options = await fetchContactPeopleOptions();
  options.users = person.map((user) => ({ value: user.id, label: user.name || '' }));
  options.fileTypes = await fetchFileTypes();
  options.employer = await fetchEmployerEntities();

  const contactPerson = {
    id: urlParams.id,
  };

  return (
    <ContactPerson defaultView={VIEW.CREATE} initial={contactPerson} selectOptions={options} />
  );
}
