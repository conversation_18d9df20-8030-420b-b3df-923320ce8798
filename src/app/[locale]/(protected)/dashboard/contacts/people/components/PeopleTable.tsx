'use client';

import { useMemo, useState } from 'react';
import DataTable from '@/components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';

import { ContactPersonType } from '@/types/contactUser';
import RowActions, { ActionItem } from '@/components/ui/RowActions';
import { useTranslations } from 'next-intl';
import { Visibility, Delete } from '@mui/icons-material';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { TABNAV } from '@/constants/enums';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

interface PeopleTableProps {
  people: ContactPersonType[];
}

export default function PeopleTable({ people }: PeopleTableProps) {
  const t = useTranslations('tables');
  const tMenu = useTranslations('menu');
  const tDialog = useTranslations('dialogs.people');
  const { handleChangeTab } = useTabsMethods();
  useTabsStaticInitializer();

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<ContactPersonType | null>(null);

  const onDeleteAction = (user: ContactPersonType) => {
    setSelectedUser(user);
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedUser) return;
    setOpenDeleteDialog(false);
    // TODO: chamar API para eliminar utilizador
    // await deleteUserApi(selectedUser.id);
    console.log('Delete user:', selectedUser.id);
  };

  const getUserActions = (user: ContactPersonType): ActionItem[] => [
    {
      label: tMenu('detailsContact'),
      icon: Visibility,
      onClick: () =>
        handleChangeTab({
          id: `/dashboard/contacts/people/${user.id}`,
          title: user.name || 'User Details',
        }),
    },
    {
      label: tMenu('delete'),
      icon: Delete,
      color: 'error',
      iconColor: 'error',
      onClick: () => onDeleteAction(user),
    },
  ];

  const baseColumns = useMemo<ColumnDef<ContactPersonType>[]>(
    () => [
      {
        accessorKey: 'id',
        header: t('columns.id'),
        meta: { filterType: 'text' },
        minSize: 100,
      },
      {
        accessorKey: 'name',
        header: t('columns.name'),
        meta: { filterType: 'text' },
        minSize: 200,
      },
      {
        accessorKey: 'email',
        header: t('columns.email'),
        meta: { filterType: 'text' },
        minSize: 200,
      },
      {
        accessorKey: 'mobile',
        header: t('columns.contact'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => {
          const mobile = ctx.getValue<string>();
          const phone = ctx.row.original.phone;
          return mobile || phone || '-';
        },
      },
      {
        accessorKey: 'country',
        header: t('columns.country'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
      {
        accessorKey: 'district',
        header: t('columns.district'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
      {
        accessorKey: 'nif',
        header: t('columns.nif'),
        meta: { filterType: 'text' },
        minSize: 120,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
      {
        accessorKey: 'clientId',
        header: t('columns.clientId'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
    ],
    [t]
  );

  const columns = useMemo<ColumnDef<ContactPersonType>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions lineId={row.original.id} actions={getUserActions(row.original)} />
        ),
      },
    ],
    [baseColumns]
  );

  return (
    <>
      <DataTable<ContactPersonType>
        data={people}
        columns={columns}
        enableFilters
        showTabs
        pageType={TABNAV.PEOPLE}
      />
      <ConfirmDialog
        open={openDeleteDialog}
        onCancel={() => setOpenDeleteDialog(false)}
        title={tDialog('delete.title')}
        subtitle={tDialog('delete.subtitle')}
        description={tDialog('delete.description')}
        onConfirm={handleDeleteConfirm}
        confirmLabel={tDialog('delete.delete')}
        cancelLabel={tDialog('delete.cancel')}
        confirmColor="error"
        confirmIcon={<Delete />}
      />
    </>
  );
}
