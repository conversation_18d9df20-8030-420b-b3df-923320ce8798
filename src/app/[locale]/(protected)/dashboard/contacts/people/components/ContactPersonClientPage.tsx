'use client';

import IconButtonText from '@/components/form-builder/inputs/IconButtonText';
import DataTable from '@/components/ui/DataTable';
import RowActions, { ActionItem } from '@/components/ui/RowActions';
import { AddCircleRounded, Delete, Edit, Visibility } from '@mui/icons-material';
import { VIEW } from '@/constants/enums';
import { contactUserSchema } from '@/schemas/contactUserSchema';
import {
  ContactUserSubTab,
  ContactUserSubTabType,
  ContactPersonType,
  CRMHistoryType,
  SelectOptions,
} from '@/types/contactUser';
import { useFilteredTabs } from '@/utils';
import { ColumnDef } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import { useCallback, useMemo, useState } from 'react';
import { SectionCardInternal } from '@/components/ui/SectionCard';
import { useDynamicFormTabs, useTabsMethods } from '@/hooks/useTabs';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import {
  AttachmentsFields,
  ContactUserDetailsFields,
  SequenceFields,
} from '@/components/form-builder/formConfigs/userConfig';
import { RGPDForm } from '@/components/form-builder/presets/rgpdForm';
import { EditInteractionDialog } from '../../../crm/opportunities/[id]/_components/editInteraction';
import {
  NewExternalInteractionDialog,
  PreviewExternalInteraction,
  PreviewSystemInteraction,
} from './crmInteractionsDialogs';
import { ContactEnrollmentsTable } from './contactEnrollmentsTable';
import { DefaultFooterActions } from '../../../../../../../components/ui/form/FormActionsOptions';

export type FormProps = {
  defaultView: VIEW;
  initial?: ContactPersonType | null;
  selectOptions?: SelectOptions;
};

export default function ContactPerson({ defaultView, initial, selectOptions }: FormProps) {
  const t = useTranslations('forms');
  const tTables = useTranslations('tables');
  const tMenu = useTranslations('menu');
  const { handleChangeTab, handleSubTabChange } = useTabsMethods();

  const { defaultValues, view, subTab, methods, readonly, handleSubmit } = useDynamicFormTabs({
    initialData: initial,
    defaultView: defaultView,
    defaultTab: ContactUserSubTab.DETAILS,
    inputSchema: contactUserSchema,
  });

  const [formOptions, setFormOptions] = useState(() => ({
    relationshipsTypes: Object?.keys?.(defaultValues?.relationships ?? {}),
    entitiesTypes: Object?.keys?.(defaultValues?.entities ?? {}),
    files: Object.keys(defaultValues?.attachments ?? {}).map((id) => ({ id, published: true })),
  }));

  const [openEditOpportunityInteractionModal, setOpenEditOpportunityInteractionModal] =
    useState<CRMHistoryType | null>(null);

  const [openEditSystemInteractionModal, setOpenEditSystemInteractionModal] =
    useState<CRMHistoryType | null>(null);

  const [openEditExternalInteractionModal, setOpenEditExternalInteractionModal] =
    useState<CRMHistoryType | null>(null);

  const [openNewExternalInteractionModal, setOpenNewExternalInteractionModal] =
    useState<boolean>(false);

  const onSubmit = () => {
    //handleSubmit()
    // handle form submission
  };

  const onReset = () => {
    console.log('Form reset');
    methods.reset({ ...initial });
  };

  const getCrmHistoryActions = useCallback(
    (item: CRMHistoryType): ActionItem[] => {
      switch (item.category) {
        case 'INTERACTION_EXTERNAL':
          return [
            {
              label: tMenu('editInteraction'),
              icon: Edit,
              disabled: readonly,
              onClick: () => setOpenEditExternalInteractionModal(item),
            },
            {
              label: tMenu('deleteInteraction'),
              icon: Delete,
              color: 'error',
              iconColor: 'error',
              disabled: readonly,
              onClick: () => console.info('Eliminar interação', item),
            },
          ];
        case 'INTERACTION_OPPORTUNITY':
          return [
            {
              label: tMenu('editInteraction'),
              icon: Edit,
              disabled: readonly,
              onClick: () => setOpenEditOpportunityInteractionModal(item),
            },
          ];
        case 'INTERACTION_SYSTEM':
          return [
            {
              label: tMenu('previewInteraction'),
              icon: Visibility,
              onClick: () => setOpenEditSystemInteractionModal(item),
            },
          ];
        case 'OPPORTUNITY':
          return [
            {
              label: tMenu('previewOpportunity'),
              icon: Visibility,
              onClick: () => {
                handleChangeTab({
                  id: `/dashboard/crm/opportunities/${item.id}`,
                  title: item.description ?? `Opportunidade #${item.id}`,
                });
              },
            },
          ];
      }
    },
    [readonly, t, view]
  );

  const baseColumns = useMemo<ColumnDef<CRMHistoryType>[]>(
    () => [
      {
        accessorKey: 'category',
        header: tTables('columns.category'),
        minSize: 256,
        cell: (props) => tTables(`categories.${props.row.original.category}`),
      },
      { accessorKey: 'id', header: 'ID', minSize: 128 },
      {
        accessorKey: 'description',
        header: tTables('columns.description'),
        minSize: 128,
      },
      {
        accessorKey: 'type',
        header: tTables('columns.type'),
        minSize: 128,
      },
      {
        accessorKey: 'madeBy',
        header: tTables('columns.madeBy'),
        minSize: 128,
      },
      {
        accessorKey: 'creationDate',
        header: tTables('columns.creationDate'),
        minSize: 128,
      },
      {
        accessorKey: 'enrollment',
        header: tTables('columns.enrollment'),
        minSize: 128,
      },
    ],
    [t]
  );

  const columns = useMemo<ColumnDef<CRMHistoryType>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions lineId={row.original.id} actions={getCrmHistoryActions(row.original)} />
        ),
      },
    ],
    [baseColumns, view, readonly]
  );

  const detailsFields = useMemo(
    () => <ContactUserDetailsFields readonly={readonly} selectOptions={selectOptions} />,
    [readonly, selectOptions]
  );

  const rgpdFields = useMemo(() => <RGPDForm readonly={readonly} />, [readonly]);

  const relationshipsFields = useMemo(
    () => (
      <SequenceFields
        formOptions={formOptions}
        selectOptions={selectOptions}
        readonly={readonly}
        setFormOptions={setFormOptions}
        fieldKey="relationships"
      />
    ),
    [formOptions, selectOptions, readonly, setFormOptions]
  );

  const entitiesFields = useMemo(
    () => (
      <SequenceFields
        formOptions={formOptions}
        selectOptions={selectOptions}
        readonly={readonly}
        setFormOptions={setFormOptions}
        fieldKey="entities"
      />
    ),
    [formOptions, selectOptions, readonly, setFormOptions]
  );

  const attachmentsFields = useMemo(
    () => (
      <AttachmentsFields
        readonly={readonly}
        formOptions={formOptions}
        setFormOptions={setFormOptions}
        selectOptions={selectOptions}
        methods={methods}
      />
    ),
    [readonly, formOptions, setFormOptions, selectOptions, methods]
  );

  const crmHistoryFields = useMemo(
    () => (
      <>
        <DataTable<CRMHistoryType>
          data={initial?.crmHistory ?? []}
          columns={columns}
          enableFilters={false}
          removePaginationOnLowResults
        />
        <IconButtonText
          field={{
            readonly: readonly,
            icon: <AddCircleRounded />,
            action: () => /*setOpen(true)**/ console.log('shoot popuip'),
            actionLabel: t('interaction.add'),
          }}
        />
      </>
    ),
    [initial, columns, readonly, t]
  );

  function renderActiveSubTab(activeSubTab: ContactUserSubTabType) {
    switch (activeSubTab) {
      case ContactUserSubTab.DETAILS:
        return detailsFields;
      case ContactUserSubTab.RGPD:
        return rgpdFields;
      case ContactUserSubTab.RELATIONSHIPS:
        return relationshipsFields;
      case ContactUserSubTab.ENTITIES:
        return entitiesFields;
      case ContactUserSubTab.ATTACHMENTS:
        return attachmentsFields;
      case ContactUserSubTab.CRM_HISTORY:
        return crmHistoryFields;
      case ContactUserSubTab.ENROLLMENTS:
        return <ContactEnrollmentsTable userId={initial!.id} />;
      case ContactUserSubTab.PAYMENTS:
      default:
        return null;
    }
  }

  const tabs = useFilteredTabs({
    tabsObj: ContactUserSubTab,
    createTabValues: [ContactUserSubTab.DETAILS, ContactUserSubTab.RGPD],
    view: view,
    t,
  });

  return (
    <>
      <SectionCardInternal
        key={Object.keys(tabs).join('-')}
        tabs={tabs}
        addSpacer
        selectedTab={subTab ?? ContactUserSubTab.DETAILS}
        onSelectTab={(subTab) => {
          handleSubTabChange({ subTab: String(subTab) });
        }}
      >
        <ComposableFormWrapper
          methods={methods}
          footerSlot={<DefaultFooterActions view={view} />}
          onSubmit={handleSubmit(onSubmit)}
          onReset={onReset}
        >
          {renderActiveSubTab(subTab ?? ContactUserSubTab.DETAILS)}
        </ComposableFormWrapper>
      </SectionCardInternal>
      {openEditOpportunityInteractionModal ? (
        <EditInteractionDialog
          onClose={() => setOpenEditOpportunityInteractionModal(null)}
          opportunityId={openEditOpportunityInteractionModal.extraData!}
          interactionId={openEditOpportunityInteractionModal.id}
        />
      ) : null}

      {openEditSystemInteractionModal ? (
        <PreviewSystemInteraction
          onClose={() => setOpenEditSystemInteractionModal(null)}
          interactionId={openEditSystemInteractionModal.id}
          disabled={openEditSystemInteractionModal.category === 'INTERACTION_SYSTEM'}
        />
      ) : null}
      {openEditExternalInteractionModal ? (
        <PreviewExternalInteraction
          onClose={() => setOpenEditExternalInteractionModal(null)}
          interactionId={openEditExternalInteractionModal.id}
          disabled={openEditExternalInteractionModal.category === 'INTERACTION_SYSTEM'}
        />
      ) : null}
      {openNewExternalInteractionModal ? (
        <NewExternalInteractionDialog onClose={() => setOpenNewExternalInteractionModal(false)} />
      ) : null}
    </>
  );
}
