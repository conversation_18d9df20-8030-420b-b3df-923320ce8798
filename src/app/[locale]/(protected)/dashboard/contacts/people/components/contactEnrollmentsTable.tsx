'use client';
import { useQuery } from '@tanstack/react-query';
import DataTable from '../../../../../../../components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { useTranslations } from 'next-intl';
import RowActions from '../../../../../../../components/ui/RowActions';
import { DropdownOptions } from '@/components/ui/dropdown/Dropdown';

type ContactEnrollment = {
  id: string;
  state: string;
  course: string;
  center: string;
  creationDate: Date;
};

const mockContactEnrollments: ContactEnrollment[] = [
  {
    id: 'ce1',
    state: 'active',
    course: 'Web Development Bootcamp',
    center: 'Lisbon Tech Hub',
    creationDate: new Date('2025-05-01'),
  },
  {
    id: 'ce2',
    state: 'pending',
    course: 'Data Science Fundamentals',
    center: 'Porto Innovation Center',
    creationDate: new Date('2025-06-15'),
  },
  {
    id: 'ce3',
    state: 'cancelled',
    course: 'UX/UI Design Sprint',
    center: 'Remote',
    creationDate: new Date('2025-04-20'),
  },
  {
    id: 'ce4',
    state: 'completed',
    course: 'Cybersecurity Essentials',
    center: 'Faro Tech Campus',
    creationDate: new Date('2025-03-10'),
  },
  {
    id: 'ce5',
    state: 'active',
    course: 'AI & Machine Learning',
    center: 'Coimbra Digital Hub',
    creationDate: new Date('2025-07-05'),
  },
];

export function ContactEnrollmentsTable({ userId }: { userId: string }) {
  const t = useTranslations('tables');
  const tMenu = useTranslations('menu');

  // HERE LETS MAKE ROWACTIONS ACCEPT COMPONENTSACTIONS ALSO SO ITS EASIER FOR SUBMENUS
  const columns = useMemo<ColumnDef<ContactEnrollment>[]>(
    () => [
      { accessorKey: 'id', header: t('columns.id') },
      { accessorKey: 'state', header: t('columns.state') },
      { accessorKey: 'course', header: t('columns.course') },
      { accessorKey: 'center', header: t('columns.center') },
      { accessorKey: 'creationDate', header: t('columns.creationDate') },

      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions
            lineId={row.original.id}
            componentActions={
              <>
                <DropdownOptions.SubOptions
                  label={tMenu('enrollment')}
                  yBottomPoint="option"
                  xDirection="left"
                ></DropdownOptions.SubOptions>
                <DropdownOptions.SubOptions
                  label={tMenu('print')}
                  yBottomPoint="option"
                  xDirection="left"
                ></DropdownOptions.SubOptions>
                <DropdownOptions.SubOptions
                  label={tMenu('invoicing')}
                  yBottomPoint="option"
                  xDirection="left"
                ></DropdownOptions.SubOptions>
              </>
            }
          />
        ),
      },
    ],
    [t]
  );

  const { data = [] } = useQuery({
    queryKey: ['contacts', userId, 'enrollments'],
    queryFn: async () => mockContactEnrollments,
  });

  return (
    <>
      <DataTable data={data} columns={columns} enableFilters={false} />
    </>
  );
}