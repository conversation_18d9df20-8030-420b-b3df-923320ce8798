import { useMutation, useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { ContactPersonType } from '../../../../../../../types/contactUser';
import { ComposableForm } from '../../../../../../../components/form-builder/ComposableFormBuilder';
import FormBuilderDialog, {
  LoadingDialog,
} from '../../../../../../../components/ui/dialog/FormBuilderDialog';
import { useForm, useFormContext } from 'react-hook-form';
import { DescriptionField } from '../../../../../../../components/form-builder/presets/descriptionField';
import { Suspense, useMemo } from 'react';
import { mapLabel, withPrefix } from '../../../../../../../utils';
import { useToast } from '../../../../../../../components/ui/ToastProvider';
import {
  InteractionState,
  InteractionType,
  useInteractionStateOptions,
  useInteractionTypesOptions,
} from '../../../../../../../schemas/opportunitySchema';
import { Save } from '@mui/icons-material';

type SystemInteraction = {
  description: string;
  doneBy: ContactPersonType;
  doneDate: Date;
  createdAt: Date;
  text: string;
  enrollmentYear: number;
  enrollmentNumber: number;
};

type ExternalInteraction = SystemInteraction & {
  type: InteractionType;
  state: InteractionState;
};

type SystemInteractionInput = SystemInteraction & {
  doneBy: string;
};

type ExternalInteractionInput = ExternalInteraction & {
  doneBy: string;
};

type InternalProps<T extends boolean> = GeneralProps & {
  title: string;
  isExternal: T;
  onConfirm: (input: T extends true ? ExternalInteractionInput : SystemInteractionInput) => any;
  defaultValues: T extends true ? ExternalInteractionInput : SystemInteractionInput;
};

type FetcherProps = GeneralProps & {
  interactionId: string;
};

type GeneralProps = {
  onClose: () => any;
  disabled?: boolean;
};

const systemInteractionMocks: SystemInteraction[] = [
  {
    description: 'Atualização automática de dados do contacto',
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactPersonType,
    doneDate: new Date('2024-01-15T10:00:00'),
    createdAt: new Date('2024-01-15T10:00:00'),
    text: 'O sistema atualizou automaticamente os dados do contacto com base nas informações recebidas do sistema externo. Foram atualizados os campos: telefone, morada e código postal.',
    enrollmentNumber: 3,
    enrollmentYear: 2020,
  },
];

const externalInteractionMocks: ExternalInteraction[] = [
  {
    description: 'Atualização externa de dados do contacto',
    doneBy: {
      id: 'system',
      name: 'Sistema Externo',
      email: '<EMAIL>',
    } as ContactPersonType,
    type: 'call',
    state: 'success',
    doneDate: new Date('2024-01-15T10:00:00'),
    createdAt: new Date('2024-01-15T10:00:00'),
    text: 'atualizado exteriormente',
    enrollmentNumber: 3,
    enrollmentYear: 2020,
  },
];

function InteractionFormBuilder({
  isExternal,
  onConfirm,
  onClose,
  title,
  disabled = false,
}: {
  isExternal: boolean;
  onConfirm: (input: any) => any;
  onClose: () => any;
  title: string;
  disabled?: boolean;
}) {
  const methods = useFormContext();
  const t = useTranslations('forms');

  const interactionTypesOptions = useInteractionTypesOptions();
  const stateOptions = useInteractionStateOptions();

  const yearOptions = useMemo(() => {
    const start = new Date().getFullYear();

    return mapLabel(Array.from({ length: 20 }, (_, i) => start - i).map(String));
  }, []);

  return (
    <FormBuilderDialog
      open={true}
      showActions={!disabled}
      onCancel={onClose}
      confirmIcon={<Save />}
      confirmLabel={t('label.save')}
      cancelLabel={t('label.cancel')}
      onConfirm={() => {
        onConfirm(methods.getValues() as any);
        onClose();
      }}
      title={title}
    >
      <DescriptionField disabled={disabled} />
      {isExternal && (
        <>
          <ComposableForm.Field
            type="select"
            placeholder={t('type.placeholder')}
            label={t('type.label')}
            name={'type'}
            options={interactionTypesOptions}
          />
          <ComposableForm.Field
            type="select"
            options={stateOptions}
            placeholder={t('state.placeholder')}
            label={t('state.label')}
            name={'state'}
          />
        </>
      )}
      <ComposableForm.Field
        type="datetime"
        label={t('doneDate.label')}
        name={'doneDate'}
        disabled={disabled}
      />
      <ComposableForm.Field
        name={'createdAt'}
        label={t('createdAt.label')}
        type={'datetime'}
        disabled={disabled}
      />
      <ComposableForm.Field
        name={'text'}
        type={'text'}
        label={t('text.label')}
        placeholder={t('text.placeholder')}
        multiline
        disabled={disabled}
      />
      <ComposableForm.Field
        name={'enrollmentYear'}
        type={'select'}
        label={t('enrollmentYear.label')}
        placeholder={t('enrollmentYear.placeholder')}
        options={yearOptions}
        disabled={disabled}
      />
      <ComposableForm.Field
        name={'enrollmentNumber'}
        type={'number'}
        label={t('enrollmentNumber.label')}
        placeholder={t('enrollmentNumber.placeholder')}
        disabled={disabled}
      />
    </FormBuilderDialog>
  );
}

function PreviewInteractionInternal<T extends boolean>({
  onClose,
  defaultValues,
  onConfirm,
  isExternal,
  title,
  disabled = false,
}: InternalProps<T>) {
  const methods = useForm({
    defaultValues,
  });

  return (
    <ComposableForm.Provider methods={methods} columns={1}>
      <InteractionFormBuilder
        onClose={onClose}
        onConfirm={onConfirm}
        isExternal={isExternal}
        title={title}
        disabled={disabled}
      />
    </ComposableForm.Provider>
  );
}

function PreviewSystemInteractionFetcher({ interactionId, ...props }: FetcherProps) {
  const t = useTranslations('dialogs');
  //const { showToast } = useToast();

  const client = useQueryClient();

  const { data } = useSuspenseQuery<SystemInteraction>({
    queryKey: ['interactions', { system: true }, interactionId],
    queryFn: () => systemInteractionMocks[0],
  });

  const input = { ...data, doneBy: data.doneBy.name } as SystemInteractionInput;

  const { mutate: editInteraction } = useMutation({
    mutationFn: async (interactionInput: SystemInteractionInput) => {
      void interactionInput;
      //TODO: fix toast
      // showToast(tPrefix('editedMessage'), 'success');
      client.invalidateQueries({ queryKey: ['interactions', { system: true }] });
    },
  });
  return (
    <PreviewInteractionInternal
      onConfirm={editInteraction}
      isExternal={false}
      defaultValues={input}
      title={t('interactions.details', {
        id: interactionId,
      })}
      {...props}
    />
  );
}

function PreviewExternalInteractionFetcher({ interactionId, ...props }: FetcherProps) {
  const t = useTranslations('dialogs');
  //const { showToast } = useToast();

  const client = useQueryClient();

  const { data } = useSuspenseQuery<ExternalInteraction>({
    queryKey: ['interactions', { external: true }, interactionId],
    queryFn: () => externalInteractionMocks[0],
  });

  const input = { ...data, doneBy: data.doneBy.name } as ExternalInteractionInput;

  const { mutate: editInteraction } = useMutation({
    mutationFn: async (interactionInput: ExternalInteractionInput) => {
      void interactionInput;

      //TODO: fix toast
      //showToast(tPrefix('editedMessage'), 'success');
      client.invalidateQueries({ queryKey: ['interactions', { external: true }] });
    },
  });
  return (
    <PreviewInteractionInternal
      onConfirm={editInteraction}
      isExternal
      defaultValues={input}
      title={t('interactions.details', {
        id: interactionId,
      })}
      {...props}
    />
  );
}

export function PreviewSystemInteraction(props: FetcherProps) {
  return (
    <Suspense fallback={<LoadingDialog />}>
      <PreviewSystemInteractionFetcher {...props} />
    </Suspense>
  );
}

export function PreviewExternalInteraction(props: FetcherProps) {
  return (
    <Suspense fallback={<LoadingDialog />}>
      <PreviewExternalInteractionFetcher {...props} />
    </Suspense>
  );
}

export function NewExternalInteractionDialog({ onClose }: { onClose: () => any }) {
  const methods = useForm<ExternalInteractionInput>();

  const t = useTranslations('dialogs');
  //const { showToast } = useToast();

  const client = useQueryClient();

  const { mutate: addInteraction } = useMutation({
    mutationFn: async (interactionInput: ExternalInteractionInput) => {
      void interactionInput;

      //TODO: fix toast
      // showToast(tPrefix('addedMessage'), 'success');
      client.invalidateQueries({ queryKey: ['interactions', { external: true }] });
      onClose();
    },
  });

  return (
    <ComposableForm.Provider methods={methods}>
      <InteractionFormBuilder
        isExternal
        onConfirm={addInteraction}
        title={t('new')}
        onClose={onClose}
      />
    </ComposableForm.Provider>
  );
}
