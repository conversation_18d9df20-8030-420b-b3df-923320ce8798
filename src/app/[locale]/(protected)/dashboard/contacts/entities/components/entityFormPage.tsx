'use client';

import { SectionCardInternal } from '../../../../../../../components/ui/SectionCard';
import { onSubmitEntity } from '../actions';
import { EntityInputSchema } from '../../../../../../../schemas/entitySchema';
import { ComposableForm } from '../../../../../../../components/form-builder/ComposableFormBuilder';
import { useTranslations } from 'next-intl';
import { ObservationsField } from '../../../../../../../components/form-builder/presets/observationsField';
import { NameField } from '../../../../../../../components/form-builder/presets/nameField';
import { EmailField } from '../../../../../../../components/form-builder/presets/emailField';
import { PhoneField } from '../../../../../../../components/form-builder/presets/phoneField';
import { NifField } from '../../../../../../../components/form-builder/presets/nifField';
import { useDynamicFormTabs, useRegisterFormGetValues, useTabsMethods } from '@/hooks/useTabs';
import { VIEW } from '../../../../../../../constants/enums';
import { useFilteredTabs } from '../../../../../../../utils';
import { EntityFormTabs } from '@/types/entities';

import { ProtocolsForm } from './protocolsForm';
import { AddressFields } from './addressFields';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { DefaultFooterActions } from '@/components/ui/form/FormActionsOptions';
import { RGPDForm } from '@/components/form-builder/presets/rgpdForm';

// missing distinction between new and not new to properly reder form tabs
// instead of any, use entitytype
export function EntityFormPage({ initial, defaultView }: { initial?: any; defaultView: VIEW }) {
  const t = useTranslations('forms');
  const { handleSubTabChange } = useTabsMethods();

  const { view, subTab, methods, readonly, getValues, handleSubmit, tabId } = useDynamicFormTabs({
    initialData: initial,
    defaultView,
    inputSchema: EntityInputSchema,
    defaultTab: EntityFormTabs.DETAILS,
  });

  const tabs = useFilteredTabs({
    tabsObj: EntityFormTabs,
    createTabValues: [EntityFormTabs.DETAILS, EntityFormTabs.RGPD],
    //need default view
    view: view,
    t,
  });
  // used for unsaved changes registration
  useRegisterFormGetValues(tabId, getValues);

  const onSubmit = async (data: any) => {
    // Only submit if there are no validation errors
    console.log('Form data submitted:', data);
    await onSubmitEntity(/*data*/);
  };

  const onError = (errors: any) => {
    console.log('Validation errors:', errors);
  };

  return (
    <SectionCardInternal
      tabs={tabs}
      selectedTab={subTab ?? EntityFormTabs.DETAILS}
      addSpacer
      onSelectTab={(tab) => {
        handleSubTabChange({ subTab: String(tab) });
      }}
    >
      <ComposableFormWrapper
        methods={methods}
        onSubmit={handleSubmit(onSubmit, onError)}
        footerSlot={<DefaultFooterActions view={view} />}
      >
        {subTab === EntityFormTabs.DETAILS && (
          <ComposableForm.Internal.Layout columns={2}>
            <NameField readonly={readonly} required />
            <EmailField readonly={readonly} />
            <PhoneField readonly={readonly} />
            <NifField readonly={readonly} />
            <ComposableForm.Field
              name="website"
              label={t('website.label')}
              placeholder={t('website.placeholder')}
              type="text"
              readonly={readonly}
            />
            <ComposableForm.Field
              name="location"
              label={t('locality.label')}
              placeholder={t('locality.placeholder')}
              type="text"
              readonly={readonly}
            />
            <AddressFields />
            <ComposableForm.Field
              name="type"
              label={t('type.label')}
              placeholder={t('type.placeholder')}
              multiple
              type="select"
              readonly={readonly}
              options={[
                {
                  value: 'cliente_formacao',
                  label: t('cliente_formacao.label'),
                },
                {
                  value: 'cliente_faturacao',
                  label: t('cliente_faturacao.label'),
                },
                {
                  value: 'entidade_empregadora',
                  label: t('entidade_empregadora.label'),
                },
                { value: 'parceria', label: t('parceria.label') },
              ]}
            />
            <ComposableForm.Field
              name="capital_social"
              label={t('capital_social.label')}
              placeholder={t('capital_social.placeholder')}
              type="number"
              endAdornment={'€'}
              readonly={readonly}
            />
            <ComposableForm.Field
              name="iban"
              label={t('iban.label')}
              placeholder={t('iban.placeholder')}
              type="text"
              readonly={readonly}
            />
            <ObservationsField readonly={readonly} />
            <ComposableForm.Field
              type={'checkbox'}
              name={'maintenanceContract'}
              label={t('maintenanceContract.label')}
              readonly={readonly}
            />
          </ComposableForm.Internal.Layout>
        )}
        {subTab === EntityFormTabs.RGPD && <RGPDForm readonly={view === VIEW.VIEW} />}
        {subTab === EntityFormTabs.PROTOCOLS && (
          <ProtocolsForm view={view} entityId={initial?.id} />
        )}
      </ComposableFormWrapper>
    </SectionCardInternal>
  );
}
