import DataTable from '../../../../../../../../components/ui/DataTable';
import { useTranslations } from 'next-intl';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { AddCircle } from '@mui/icons-material';
import FormBuilderDialog from '../../../../../../../../components/ui/dialog/FormBuilderDialog';
import { FormProvider, useForm } from 'react-hook-form';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { AddressFields } from '../addressFields';
import { PostalCodeField } from '../../../../../../../../components/form-builder/presets/postalCode';
import { useToast } from '../../../../../../../../components/ui/ToastProvider';
import { AddButton } from '../../../../../../../../components/ui/AddButton';
import { EntityType } from '../../../../../../../../schemas/entitySchema';
import { withPrefix } from '@/utils';

type Address = {
  type: string;
  locality: string;
  postalCode: string;
  parish: string;
  municipality: string;
  district: string;
};

const mockData: Address[] = [
  {
    type: 'Headquarters',
    locality: 'Lisbon',
    postalCode: '1000-001',
    parish: 'Areeiro',
    municipality: 'Lisbon',
    district: 'Lisbon',
  },
  {
    type: 'Branch',
    locality: 'Porto',
    postalCode: '4000-001',
    parish: 'Bonfim',
    municipality: 'Porto',
    district: 'Porto',
  },
];

const prefix = 'entities.subTabs.protocols.subTabs.addresses';

const tPrefix = withPrefix(prefix);

export function AddressesForm({
  protocolType,
  readonly = false,
}: {
  disabled?: boolean;
  readonly?: boolean;
  protocolType: EntityType['protocolType'];
}) {
  const t = useTranslations(prefix);
  const [openDialog, setOpenDialog] = useState(false);

  const methods = useForm({
    defaultValues: {
      type: protocolType,
    },
  });

  const { showToast } = useToast();

  const columns = useMemo<ColumnDef<Address>[]>(
    () => [
      {
        accessorKey: 'type',
        header: t('columns.type'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'locality',
        header: t('columns.locality'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'postalCode',
        header: t('columns.postalCode'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'parish',
        header: t('columns.parish'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'municipality',
        header: t('columns.municipality'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
      {
        accessorKey: 'district',
        header: t('columns.district'),
        meta: { filterType: 'text' },
        minSize: 128,
      },
    ],
    [t]
  );

  return (
    <>
      {mockData.length ? (
        <>
          <DataTable columns={columns} data={mockData} enableFilters={false} />
          <AddButton
            disabled={readonly}
            onClick={() => setOpenDialog(true)}
            sx={{
              alignSelf: 'flex-start',
            }}
          >
            {t('add')}
          </AddButton>
        </>
      ) : (
        <Box display={'flex'} flexDirection={'column'} gap={3} alignItems={'center'} my={3}>
          <Typography>{t('noAttachments')}</Typography>
          <AddButton disabled={readonly} onClick={() => setOpenDialog(true)}>
            {t('add')}
          </AddButton>
        </Box>
      )}
      <FormProvider {...methods}>
        {openDialog && (
          <FormBuilderDialog
            open={true}
            onConfirm={methods.handleSubmit((data) => {
              void data;
              showToast(t('success'), 'success');
              setOpenDialog(false);
            })}
            onCancel={() => {
              setOpenDialog(false);
            }}
            confirmIcon={<AddCircle />}
            title={t('add')}
          >
            <ComposableForm.Field
              name={'file'}
              type={'fileUpload'}
              placeholder={tPrefix('fields.file.placeholder')}
              readonly={readonly}
            />
            <ComposableForm.Field
              name={'type'}
              type={'text'}
              label={tPrefix('fields.type.label')}
              placeholder={tPrefix('fields.type.placeholder')}
              readonly={readonly}
            />
            <PostalCodeField name={'postalCode'} />
            <AddressFields />
          </FormBuilderDialog>
        )}
      </FormProvider>
    </>
  );
}