'use client';

import { useMemo, useState } from 'react';
import { InternalTabs, SectionTab } from '../../../../../../../../components/ui/SectionCard';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { AddressesForm } from './addressesForm';
import { AttachmentsForm } from './attachmentsForm';
import { useFormContext, useWatch } from 'react-hook-form';
import { useOptionsIntl } from '../../../../../../../../hooks/useFieldOptionsIntl';
import { VIEW } from '../../../../../../../../constants/enums';
import { useTranslations } from 'next-intl';

enum ProtocolsTabs {
  TYPES = 'protocolTypes',
  ADDRESSES = 'addresses',
  ATTACHMENTS = 'attachments',
}

const useProtocolTypeOptions = () =>
  useOptionsIntl(['Práticas', 'Estágios'], 'forms.protocolTypes.options');

export function ProtocolsForm({ view, entityId }: { view: VIEW; entityId?: string }) {
  const [selectedTab, setSelectedTab] = useState(ProtocolsTabs.TYPES);
  const t = useTranslations('forms');

  const { control } = useFormContext();

  const [protocolType] = useWatch({
    control,
    name: ['protocolType'],
  });

  const protocolTypeOptions = useProtocolTypeOptions();

  const tabs = useMemo<SectionTab[]>(
    () => [
      { label: t('protocolTypes.label'), value: ProtocolsTabs.TYPES },
      ...(view !== VIEW.CREATE
        ? []
        : [
            {
              label: t('addresses.label'),
              value: ProtocolsTabs.ADDRESSES,
              disabled: !protocolType,
            },
            {
              label: t('attachments.label'),
              value: ProtocolsTabs.ATTACHMENTS,
              disabled: !protocolType,
            },
          ]),
    ],
    [protocolType, view]
  );

  const readonly = view === VIEW.VIEW;

  return (
    <>
      <InternalTabs tabs={tabs} selectedTab={selectedTab} onSelectTab={setSelectedTab} />
      {selectedTab === ProtocolsTabs.TYPES && (
        <ComposableForm.Internal.Layout columns={1}>
          <ComposableForm.Field
            name={'protocolType'}
            label={t('protocolTypes.label')}
            type={'select'}
            options={protocolTypeOptions}
            readonly={readonly}
          />
        </ComposableForm.Internal.Layout>
      )}
      {selectedTab === ProtocolsTabs.ADDRESSES && protocolType && (
        <AddressesForm readonly={readonly} protocolType={protocolType} />
      )}
      {selectedTab === ProtocolsTabs.ATTACHMENTS && entityId && protocolType && !readonly && (
        <AttachmentsForm entityId={entityId} protocolType={protocolType} />
      )}
    </>
  );
}