'use client';

import { useMemo, useState } from 'react';
import DataTable from '@/components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';

import { EntityType } from '@/schemas/entitySchema';
import RowActions, { ActionItem } from '@/components/ui/RowActions';
import { useTranslations } from 'next-intl';
import { Visibility, Delete } from '@mui/icons-material';
import { useTabsMethods, useTabsStaticInitializer } from '@/hooks/useTabs';
import { TABNAV } from '@/constants/enums';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

interface EntitiesTableProps {
  entities: EntityType[];
}

export default function EntitiesTable({ entities }: EntitiesTableProps) {
  const t = useTranslations('tables');
  const tMenu = useTranslations('menu');
  const tDialog = useTranslations('dialogs.entities');
  const { handleChangeTab } = useTabsMethods();
  useTabsStaticInitializer();

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState<EntityType | null>(null);

  const onDeleteAction = (entity: EntityType) => {
    setSelectedEntity(entity);
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedEntity) return;
    setOpenDeleteDialog(false);
    // TODO: chamar API para eliminar entidade
    // await deleteEntityApi(selectedEntity.id);
    console.log('Delete entity:', selectedEntity.id);
  };

  const getEntityActions = (entity: EntityType): ActionItem[] => [
    {
      label: tMenu('detailsEntity'),
      icon: Visibility,
      onClick: () =>
        handleChangeTab({
          id: `/dashboard/contacts/entities/${entity.id}`,
          title: entity.name || 'Entity Details',
        }),
    },
    {
      label: tMenu('delete'),
      icon: Delete,
      color: 'error',
      iconColor: 'error',
      onClick: () => onDeleteAction(entity),
    },
  ];

  const baseColumns = useMemo<ColumnDef<EntityType>[]>(
    () => [
      {
        accessorKey: 'id',
        header: t('columns.id'),
        meta: { filterType: 'text' },
        minSize: 100,
      },
      {
        accessorKey: 'name',
        header: t('columns.name'),
        meta: { filterType: 'text' },
        minSize: 200,
      },
      {
        accessorKey: 'email',
        header: t('columns.email'),
        meta: { filterType: 'text' },
        minSize: 200,
      },
      {
        accessorKey: 'phone',
        header: t('columns.contact'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
      {
        accessorKey: 'country',
        header: t('columns.country'),
        meta: { filterType: 'text' },
        minSize: 120,
        cell: (ctx) => {
          const country = ctx.getValue<string>();
          return country === 'PT' ? 'Portugal' : country || '-';
        },
      },
      {
        accessorKey: 'distrito',
        header: t('columns.district'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
      {
        accessorKey: 'nif',
        header: t('columns.nif'),
        meta: { filterType: 'text' },
        minSize: 120,
        cell: (ctx) => ctx.getValue<string>() || '-',
      },
      {
        accessorKey: 'type',
        header: t('columns.type'),
        meta: { filterType: 'text' },
        minSize: 150,
        cell: (ctx) => {
          const type = ctx.getValue<string>();
          const typeMap: Record<string, string> = {
            cliente_formacao: 'Training Client',
            cliente_faturacao: 'Billing Client',
            entidade_empregadora: 'Employer Entity',
            parceria: 'Partnership',
          };
          return typeMap[type] || type || '-';
        },
      },
    ],
    [t]
  );

  const columns = useMemo<ColumnDef<EntityType>[]>(
    () => [
      ...baseColumns,
      {
        id: 'actions',
        header: '',
        size: 56,
        enableSorting: false,
        enableColumnFilter: false,
        enableHiding: false,
        cell: ({ row }) => (
          <RowActions lineId={row.original.id!} actions={getEntityActions(row.original)} />
        ),
      },
    ],
    [baseColumns]
  );

  return (
    <>
      <DataTable<EntityType>
        data={entities}
        columns={columns}
        enableFilters
        showTabs
        pageType={TABNAV.PEOPLE}
      />
      <ConfirmDialog
        open={openDeleteDialog}
        onCancel={() => setOpenDeleteDialog(false)}
        title={tDialog('delete.title')}
        subtitle={tDialog('delete.subtitle')}
        description={tDialog('delete.description')}
        onConfirm={handleDeleteConfirm}
        confirmLabel={tDialog('delete.delete')}
        cancelLabel={tDialog('delete.cancel')}
        confirmColor="error"
        confirmIcon={<Delete />}
      />
    </>
  );
}
