import { useForm } from 'react-hook-form';
import FormBuilderDialog from '../../../../../../../components/ui/dialog/FormBuilderDialog';
import { useTranslations } from 'next-intl';
import { useToast } from '../../../../../../../components/ui/ToastProvider';
import { Delete } from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Typography } from '@mui/material';

export function DeleteEntityModal({ onClose, id }: { id: string; onClose: () => any }) {
  const methods = useForm();

  const { showToast } = useToast();

  const t = useTranslations('dashboard.contacts.entities.delete');

  const queryClient = useQueryClient();

  const { mutate: deleteEntity } = useMutation({
    mutationFn: async () => {
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['entities', id],
      });

      showToast(t('success', { id }), 'success');
      onClose();
    },
  });

  return (
    <FormBuilderDialog
      maxWidth={'xs'}
      open={true}
      onConfirm={methods.handleSubmit(() => deleteEntity())}
      onCancel={onClose}
      confirmIcon={<Delete />}
      confirmLabel={t('action')}
      confirmColor={'error'}
      title={t('title')}
    >
      <Typography fontWeight={'bolder'}>{t('subTitleQuestion')}</Typography>

      <Typography>{t('subTitle')}</Typography>
    </FormBuilderDialog>
  );
}
