'use client';

import { Save } from '@mui/icons-material';
import { Box, Button, Link } from '@mui/material';
import { useTranslations } from 'next-intl';

export default function EntityActions() {
  const t = useTranslations();

  return (
    <Box display={'flex'} gap={'8px'}>
      <Button component={Link} variant={'ghost'} href="/dashboard" type={'submit'}>
        {t('button.cancel')}
      </Button>
      <Button
        component={Button}
        variant={'contained'}
        startIcon={<Save />}
        type={'submit'}
        form={'entity-form'}
      >
        {t('button.createEntity')}
      </Button>
    </Box>
  );
}
