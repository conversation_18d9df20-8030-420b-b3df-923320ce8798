import { useTranslations } from 'next-intl';
import { AttachFile } from '@mui/icons-material';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { withPrefix } from '../../../../../../../../utils';
import { DescriptionField } from '../../../../../../../../components/form-builder/presets/descriptionField';
import { EntityAttachmentInput, ProtocolType } from '../../../../../../../../schemas/entitySchema';
import { TextInputFileUploadField } from '../../../../../../../../components/form-builder/inputs/TextInputFileUpload';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '../../../../../../../../components/ui/ToastProvider';

const prefix = 'entities.subTabs.protocols.subTabs.attachments';

const tPrefix = withPrefix(prefix);

const tempField = 'files.attachments.temp';

type SubmittedFile = {
  id: string;
  file: File;
  type: ProtocolType;
  description: string;
  startDate: Date;
  endDate: Date;
};

type UnsubmittedFile = Omit<Partial<SubmittedFile>, 'id'>;

export function AttachmentsForm({
  protocolType,
  entityId,
}: {
  entityId: string;
  protocolType: ProtocolType;
}) {
  const t = useTranslations(prefix);

  const { showToast } = useToast();

  const queryClient = useQueryClient();

  const queryKey = ['entities', entityId, 'attachments'];

  const { data: files = [] } = useQuery({
    queryKey,
    queryFn: () => [] as SubmittedFile[],
  });

  const { mutate: uploadFile } = useMutation({
    mutationFn: async (file: UnsubmittedFile) => {
      const input = EntityAttachmentInput.parse(file);
      queryClient.setQueryData(queryKey, (files: any[]) => [
        ...files,
        { id: new Date().toString(), ...input } as SubmittedFile,
      ]);
    },
  });

  const methods = useForm({});

  const [temp] = useWatch({
    control: methods.control,
    name: [tempField],
  });

  return (
    <FormProvider {...methods}>
      <ComposableForm.Field
        type={'fileUploadModal'}
        name={'files'}
        createAction={() => {
          methods.setValue(tempField, {
            [withPrefix(tempField)('type')]: protocolType,
          });

          return 'temp';
        }}
        cancelAction={() => methods.setValue(tempField, null)}
        deleteAction={() => methods.setValue(tempField, null)}
        submitAction={() => {
          if (temp) {
            uploadFile(temp);
            methods.unregister(tempField);

            showToast(t('success'), 'success');
          }
        }}
        modalLabel={t('attach')}
        /*confirmIcon={<AttachFile />}
            confirmLabel={t('attach')}*/
        modalNode={(id) => (
          <>
            <TextInputFileUploadField
              name={withPrefix(id)('file')}
              placeholder={tPrefix('fields.file.placeholder')}
              buttonContent={
                <>
                  <AttachFile />
                  <span>{t('fields.file.buttonLabel')}</span>
                </>
              }
            />
            <ComposableForm.Field
              name={withPrefix(id)('type')}
              type={'text'}
              label={tPrefix('fields.type.label')}
              placeholder={tPrefix('fields.type.placeholder')}
              disabled
            />
            <DescriptionField name={'description'} />
            <ComposableForm.Field
              name={withPrefix(id)('labels')}
              type={'text'}
              label={tPrefix('fields.labels.label')}
              placeholder={tPrefix('fields.type.placeholder')}
            />
            <ComposableForm.Field
              name={withPrefix(id)('startDate')}
              type={'date'}
              label={tPrefix('fields.startDate.label')}
              placeholder={tPrefix('fields.startDate.placeholder')}
            />
            <ComposableForm.Field
              name={withPrefix(id)('endDate')}
              type={'date'}
              label={tPrefix('fields.endDate.label')}
              placeholder={tPrefix('fields.endDate.placeholder')}
            />
          </>
        )}
      >
        {files.map((file) => (
          <ComposableForm.Field
            key={file.id}
            name={''}
            id={file.id}
            parent={''}
            label={'forms.attachments.type'}
            type="fileGroup"
            deleteAction={() =>
              queryClient.setQueryData(queryKey, (files: any[]) =>
                files.filter((f) => f.id !== file.id)
              )
            }
          />
        ))}
      </ComposableForm.Field>
    </FormProvider>
  );
}
