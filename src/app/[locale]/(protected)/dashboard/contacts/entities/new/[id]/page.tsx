'use server';

import { PageProps } from '../../../../../../../../types';
import { VIEW } from '@/constants/enums';
import { EntityFormPage } from '../../components/entityFormPage';

export default async function Page({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const entityInfo = { id: urlParams.id };

  // new way with updated formbuilder using composition
  return <EntityFormPage defaultView={VIEW.CREATE} initial={entityInfo} />;
}
