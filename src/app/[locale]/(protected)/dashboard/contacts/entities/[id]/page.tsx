'use server';

import { VIEW } from '@/constants/enums';
import { PageProps } from '../../../../../../../types';
import { EntityFormPage } from '../components/entityFormPage';

export default async function Page({ params }: PageProps<{ id: string }>) {
  const urlParams = await params;
  const entityInfo = { id: urlParams.id };

  // new way with updated formbuilder using composition
  return <EntityFormPage initial={entityInfo} defaultView={VIEW.VIEW} />;
}
