import { useFormContext, useWatch } from 'react-hook-form';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { ProfileSelectField } from '../../../../../../../../components/form-builder/presets/profileSelectField';
import { TraineeTypeField } from '../../../../../../../../components/form-builder/presets/traineeTypeField';
import { FieldOption } from '../../../../../../../../components/form-builder/types/formBuilder';
import SectionCard from '../../../../../../../../components/ui/SectionCard';
import { useOptionsIntl } from '../../../../../../../../hooks/useFieldOptionsIntl';
import { InsuranceFields } from '../insuranceFields';
import { useEffect } from 'react';
import { useTranslations } from 'next-intl';

const enrollmentOptions: FieldOption[] = [
  {
    label: '75€',
    value: 75,
  },
  {
    label: '120€',
    value: 120,
  },
  {
    label: '145€',
    value: 145,
  },
  {
    label: '150€',
    value: 150,
  },
];

const useRecurringPaymentTypeOptions = () =>
  useOptionsIntl(['reference', 'transfer', 'presential'], 'forms.recurringPaymentTypes.options');

const usePaymentTypeOptions = () =>
  useOptionsIntl(['reference', 'transfer', 'cash', 'card', 'check'], 'forms.paymentTypes.options');

export function GesmantEnrollmentInvoicingTab({ readonly }: { readonly?: boolean }) {
  const recurringPaymentTypeOptions = useRecurringPaymentTypeOptions();
  const paymentTypeOptions = usePaymentTypeOptions();
  const t = useTranslations('forms');

  const { control, resetField } = useFormContext();

  const [differentInvoiceContact, paymentType] = useWatch({
    control,
    name: ['differentInvoiceContact', 'upfront.paymentType'],
  });

  useEffect(() => {
    if (paymentType !== 'check') {
      resetField('upfront.checkNumber');
      resetField('upfront.bank');
      resetField('upfront.agency');
    }
  }, [paymentType]);

  return (
    <>
      <SectionCard title={t('subtab.secondary.contract')}>
        <ComposableForm.Internal.Layout columns={2}>
          <ComposableForm.Field
            label={t('courseValue.label')}
            placeholder={t('courseValue.placeholder')}
            name={'courseValue'}
            endAdornment={'€'}
            type={'number'}
            readonly={readonly}
          />
          <ComposableForm.Field
            label={t('monthlyCount.label')}
            placeholder={t('monthlyCount.placeholder')}
            name={'monthlyCount'}
            type={'number'}
            readonly={readonly}
          />
          <ComposableForm.Field
            label={t('enrollmentValue.label')}
            placeholder={t('enrollmentValue.placeholder')}
            name={'enrollmentValue'}
            options={enrollmentOptions}
            type={'select'}
            readonly={readonly}
          />
          <InsuranceFields readonly={readonly} />
          <ComposableForm.Field
            label={t('recurringPaymentTypes.label')}
            placeholder={t('recurringPaymentTypes.placeholder')}
            name={'recurringPaymentType'}
            options={recurringPaymentTypeOptions}
            type={'select'}
            readonly={readonly}
          />
          <ComposableForm.Field
            type={'checkbox'}
            name={'differentInvoiceContact'}
            label={t('differentInvoiceContact.label')}
            colSpan={12}
            readonly={readonly}
          />
          {differentInvoiceContact === true && (
            <>
              <TraineeTypeField name={'invoiceContactType'} readonly={readonly} />
              <ProfileSelectField name={'invoiceContact'} readonly={readonly} />
            </>
          )}
        </ComposableForm.Internal.Layout>
      </SectionCard>
      <SectionCard title={t('subtab.secondary.paymentEnrollment')}>
        <ComposableForm.Internal.Layout columns={2}>
          <ComposableForm.Field
            name={'upfront.monthlyPaymentValue'}
            label={t('monthlyPaymentValue.label')}
            placeholder={t('monthlyPaymentValue.placeholder')}
            type={'number'}
            readonly={readonly}
            endAdornment={'€'}
          />
          <ComposableForm.Field
            name={'upfront.totalPaid'}
            label={t('totalPaid.label')}
            placeholder={t('totalPaid.placeholder')}
            type={'number'}
            endAdornment={'€'}
            readonly={readonly}
          />
          <ComposableForm.Field
            name={'upfront.paymentType'}
            label={t('paymentType.label')}
            placeholder={t('paymentType.placeholder')}
            options={paymentTypeOptions}
            type={'select'}
            readonly={readonly}
          />
          {paymentType === 'check' && (
            <>
              <ComposableForm.Field
                name={'upfront.checkNumber'}
                label={t('checkNumber.label')}
                placeholder={t('checkNumber.placeholder')}
                type={'number'}
                readonly={readonly}
              />
              <ComposableForm.Field
                name={'upfront.bank'}
                label={t('bank.label')}
                placeholder={t('bank.placeholder')}
                type={'text'}
                readonly={readonly}
              />
              <ComposableForm.Field
                name={'upfront.agency'}
                label={t('agency.label')}
                placeholder={t('agency.placeholder')}
                type={'text'}
                readonly={readonly}
              />
            </>
          )}
        </ComposableForm.Internal.Layout>
      </SectionCard>
    </>
  );
}