'use client';

import { SectionCardInternal } from '../../../../../../../components/ui/SectionCard';
import { VIEW } from '../../../../../../../constants/enums';
import { GesmatEnrollmentDetailsTab } from './_tabs/details';
import { GesmantEnrollmentInvoicingTab } from './_tabs/invoicing';
import { GesmatEnrollmentGroupTab } from './_tabs/groups';
import { EnrollmentTabs } from '@/types/gesmat';
import { useTranslations } from 'next-intl';
import { useFilteredTabs } from '@/utils';
import { useParams } from 'next/navigation';
import { useDynamicFormTabs, useTabsMethods } from '@/hooks/useTabs';
import { useSuspenseQuery } from '@tanstack/react-query';
import ComposableFormWrapper from '@/components/ui/FormWrapper';
import { enrollmentSchema } from '@/schemas/enrollmentSchema';
import { DefaultFooterActions } from '../../../../../../../components/ui/form/FormActionsOptions';

export function GesmatEnrollmentsForm({ defaultView }: { defaultView: VIEW }) {
  const t = useTranslations('forms');
  const enrollmentId = useParams<{ id: string }>().id;
  const { data: enrollment } = useSuspenseQuery<any | undefined>({
    queryKey: ['enrollment', enrollmentId],
    //how to fix this shait, this behaviour is needed
    queryFn: () => {
      return Promise.resolve({ id: enrollmentId });
    },
  });

  const { handleSubmit, readonly, methods, subTab, view } = useDynamicFormTabs({
    defaultView: defaultView,
    defaultTab: EnrollmentTabs.DETAILS,
    inputSchema: enrollmentSchema,
    initialData: enrollment,
  });

  const { handleSubTabChange } = useTabsMethods();

  const onSubmit = handleSubmit((data) => {
    void data;
  });

  const tabs = useFilteredTabs({
    tabsObj: EnrollmentTabs,
    createTabValues: Object.values(EnrollmentTabs),
    view: view,
    t,
  });

  return (
    <SectionCardInternal
      addSpacer
      tabs={tabs}
      selectedTab={subTab}
      onSelectTab={(tab) => {
        handleSubTabChange({ subTab: String(tab) });
      }}
    >
      <ComposableFormWrapper
        footerSlot={<DefaultFooterActions view={view} />}
        methods={methods}
        onSubmit={onSubmit}
      >
        {subTab === EnrollmentTabs.DETAILS && <GesmatEnrollmentDetailsTab readonly={readonly} />}
        {subTab === EnrollmentTabs.INVOICING && (
          <GesmantEnrollmentInvoicingTab readonly={readonly} />
        )}
        {subTab === EnrollmentTabs.GROUPS && <GesmatEnrollmentGroupTab readonly={readonly} />}
      </ComposableFormWrapper>
    </SectionCardInternal>
  );
}
