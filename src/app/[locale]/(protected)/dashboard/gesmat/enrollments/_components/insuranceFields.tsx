import { useFormContext, useWatch } from 'react-hook-form';
import { ComposableForm } from '../../../../../../../components/form-builder/ComposableFormBuilder';
import { useOptionsIntl } from '../../../../../../../hooks/useFieldOptionsIntl';
import { FieldOption } from '../../../../../../../components/form-builder/types/formBuilder';
import { useTranslations } from 'next-intl';

const useHasInsuranceOptions = () => useOptionsIntl(['yes', 'no'], 'forms.hasInsurance.options');

const insuranceValueOptions: FieldOption[] = [
  {
    value: 75,
    label: '75€',
  },
  {
    value: 50,
    label: '50€',
  },
];

export function InsuranceFields({ readonly = false }: { readonly?: boolean }) {
  const hasInsuranceOptions = useHasInsuranceOptions();
  const t = useTranslations('forms');

  const { control } = useFormContext();

  const [hasInsurance] = useWatch({
    control,
    name: ['hasInsurance'],
  });

  return (
    <>
      <ComposableForm.Field
        name={'hasInsurance'}
        label={t('hasInsurance.label')}
        placeholder={t('hasInsurance.placeholder')}
        type={'select'}
        options={hasInsuranceOptions}
        readonly={readonly}
      />
      <ComposableForm.Field
        name={'insuranceValue'}
        label={t('insuranceValue.label')}
        placeholder={t('insuranceValue.placeholder')}
        type={'number'}
        options={insuranceValueOptions}
        readonly={readonly || !hasInsurance || hasInsurance === 'no'}
      />
    </>
  );
}