import { useTranslations } from 'next-intl';
import { ComposableForm } from '../../../../../../../../components/form-builder/ComposableFormBuilder';
import { BaseLearningFields } from '../../../../../../../../components/form-builder/presets/baseLearningFields';
import { CentersSelectField } from '../../../../../../../../components/form-builder/presets/centersField';
import { EntitySelectField } from '../../../../../../../../components/form-builder/presets/entityField';
import { ObservationsField } from '../../../../../../../../components/form-builder/presets/observationsField';
import { ProfileSelectField } from '../../../../../../../../components/form-builder/presets/profileSelectField';
import { ShiftField } from '../../../../../../../../components/form-builder/presets/shiftField';
import { TraineeTypeField } from '../../../../../../../../components/form-builder/presets/traineeTypeField';
import { useOptionsIntl } from '../../../../../../../../hooks/useFieldOptionsIntl';
import { MODALITY_OPTIONS } from '@/types/gesmat';

const useModalityOptions = () => useOptionsIntl(MODALITY_OPTIONS, 'forms.modality.options');

export function GesmatEnrollmentDetailsTab({ readonly }: { readonly?: boolean }) {
  const t = useTranslations('forms');
  const modalityOptions = useModalityOptions();

  return (
    <ComposableForm.Internal.Layout columns={2}>
      <ComposableForm.Field
        name={'course'}
        label={t('course.label')}
        placeholder={t('course.placeholder')}
        type={'text'}
        readonly={readonly}
      />
      <ComposableForm.Field
        name={'modality'}
        label={t('modality.label')}
        placeholder={t('modality.placeholder')}
        type={'select'}
        options={modalityOptions}
        readonly={readonly}
      />
      <CentersSelectField name={'center'} readonly={readonly} />
      <ShiftField name={'shift'} readonly={readonly} />
      <BaseLearningFields readonly={readonly} />
      <TraineeTypeField name={'traineeType'} readonly={readonly} />
      <ProfileSelectField name={'student'} readonly={readonly} />
      <EntitySelectField name={'studentParent'} readonly={readonly} />
      <ObservationsField readonly={readonly} />
    </ComposableForm.Internal.Layout>
  );
}