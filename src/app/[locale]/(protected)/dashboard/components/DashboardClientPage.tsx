'use client';
import { useState } from 'react';
import { Box, Grid } from '@mui/material';
import PersistentDrawer from '@/components/ui/Drawer';
import SidebarAccordionNav from '@/components/ui/sidebar/Sidebar';
import DashboardBreadcrumbs from '@/components/ui/DashboardBreadcrumbs';
import PageHeader from '@/components/ui/PageHeader';
import TabsComponent from '@/components/ui/tabBar/TabsComponent';
import Navbar from '@/components/ui/Navbar';

export default function DashboardClientPage({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(false);
  return (
    <Grid sx={{ display: 'flex' }} padding={5}>
      <Navbar onDrawerToggle={() => setOpen(!open)} />
      <PersistentDrawer open={open} onToggle={() => setOpen(!open)}>
        <SidebarAccordionNav mini={!open} />
      </PersistentDrawer>

      <Box component="main" sx={{ flexGrow: 1, minWidth: 0 }}>
        <TabsComponent />
        <DashboardBreadcrumbs />
        <PageHeader />
        {children}
      </Box>
    </Grid>
  );
}