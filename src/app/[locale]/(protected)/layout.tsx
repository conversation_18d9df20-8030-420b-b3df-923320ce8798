import { ReactNode } from 'react';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/authOptions';
import AuthGuard from '@/components/auth/AuthGuard';

export default async function ProtectedLayout({ children }: { children: ReactNode }) {
  const session = await getServerSession(authOptions);

  if (!session) redirect('/sign-in');

  return <AuthGuard>{children}</AuthGuard>;
}
