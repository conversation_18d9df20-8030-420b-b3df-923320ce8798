import { Container, Typography, Box } from '@mui/material';
import { useTranslations } from 'next-intl';
import { redirect } from 'next/navigation';

export default function PublicHomePage() {
  const t = useTranslations();
  redirect('/sign-in'); // ignore first page

  return (
    <main>
      <Container>
        <Box>
          <Typography variant="h2" fontWeight={700} gutterBottom>
            {t('welcome')}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" maxWidth="sm">
            {t('subtitle')}
          </Typography>
        </Box>
      </Container>
    </main>
  );
}
