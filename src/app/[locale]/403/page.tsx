'use client';

import { Box, Typography, Container } from '@mui/material';

export default function ForbiddenPage() {
  return (
    <Container maxWidth="sm" sx={{ textAlign: 'center', py: 10 }}>
      <Typography variant="h3" gutterBottom>
        403 – Acesso Negado
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Você não tem permissão para ver esta página.
      </Typography>
      <Box mt={4}></Box>
    </Container>
  );
}
