import { Container, Box } from '@mui/material';
import LoginCardFields from './_components/LoginCard';

export const dynamic = 'force-dynamic';

export default async function SignInPage() {
  return (
    <Container disableGutters maxWidth={false} sx={{ height: '100vh', display: 'flex' }}>
      <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', p: 4 }}>
        <Box sx={{ width: '100%', maxWidth: 480 }}>
          <LoginCardFields />
        </Box>
      </Box>
    </Container>
  );
}
