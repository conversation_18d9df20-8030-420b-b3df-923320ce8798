import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

export default function PublicLayout({ children }: { children: React.ReactNode }) {
  const t = useTranslations('common');

  return (
    <Container
      disableGutters
      maxWidth={false}
      sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'row',
        backgroundImage: "url('/background.svg')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 8,
        }}
      >
        <Image
          width={325}
          height={92}
          src="/logo.svg"
          alt="Logo"
          style={{ maxWidth: '100%', height: 'auto' }}
        />
        <Box
          sx={{
            textAlign: 'left',
            maxWidth: 470,
            mt: 4,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          <Typography variant="h2" gutterBottom>
            {t('homepage.title')}
          </Typography>
          <Typography variant="h6" color="textSecondary">
            {t('homepage.subtitle')}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4,
        }}
      >
        {children}
      </Box>
    </Container>
  );
}
