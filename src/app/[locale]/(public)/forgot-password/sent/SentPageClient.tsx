'use client';

import { useState, useEffect, useActionState, startTransition } from 'react';
import { Container, Box, Typography, Button } from '@mui/material';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import { useLocale, useTranslations } from 'next-intl';
import Link from 'next/link';
import { ChevronLeft } from '@mui/icons-material';
import { useSearchParams } from 'next/navigation';
import { recoverPasswordAction, RecoverPasswordState } from '@/app/actions/auth';

export default function SentPageClient() {
  const t = useTranslations('common');
  const locale = useLocale();
  const params = useSearchParams();
  const email = params.get('email') ?? '';

  const [state, formAction] = useActionState<RecoverPasswordState, FormData>(
    recoverPasswordAction,
    { success: false, errors: {} }
  );

  const [counter, setCounter] = useState(90);
  useEffect(() => {
    if (counter <= 0) return;
    const id = setInterval(() => setCounter((c) => c - 1), 1000);
    return () => clearInterval(id);
  }, [counter]);

  useEffect(() => {
    if (state.success) setCounter(90);
  }, [state.success]);

  const mm = String(Math.floor(counter / 60)).padStart(2, '0');
  const ss = String(counter % 60).padStart(2, '0');
  const label =
    counter > 0
      ? t('forgotPassword.ResendLinkIn', { time: `${mm}:${ss}` })
      : t('forgotPassword.resendLink');

  const handleResend = () => {
    if (!email) return;
    const fd = new FormData();
    fd.append('email', email);
    fd.append('languageCode', locale);

    startTransition(() => {
      formAction(fd);
    });
  };

  return (
    <Container maxWidth="sm" sx={{ py: 8, textAlign: 'center' }}>
      <Box
        sx={{
          border: '1px dashed',
          borderColor: 'grey.300',
          borderRadius: 2,
          p: 4,
        }}
      >
        <MailOutlineIcon sx={{ fontSize: 48, color: 'grey.500' }} />
        <Typography variant="h5" fontWeight={600} sx={{ mt: 2 }}>
          {t('forgotPassword.sentTitle')}
        </Typography>
        <Typography variant="body1" sx={{ mt: 1, color: 'text.secondary' }}>
          {t('forgotPassword.sentInstructions')}
        </Typography>
        <Box sx={{ width: '100%', gap: 2, mt: 4 }}>
          <Link href={`/forgot-password`} passHref>
            <Button
              variant="contained"
              onClick={handleResend}
              //disabled={counter > 0 || state.pending}
              fullWidth
            >
              {label}
            </Button>
          </Link>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 2 }}>
          <Link href="/sign-in" passHref>
            <Button color="primary" fullWidth startIcon={<ChevronLeft />}>
              {t('forgotPassword.backToSignIn')}
            </Button>
          </Link>
        </Box>
      </Box>
    </Container>
  );
}
