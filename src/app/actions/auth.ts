'use server';

import { redirect } from 'next/navigation';
import ENDPOINTS from '@/constants/endpoints';
import {
  recoverPasswordSchema,
  apiCreateUserSchema,
  formSchema,
  ApiCreateUserData,
  loginFormApi,
} from '@/schemas/authSchema';
import { ZodError } from 'zod';
import { LoginState } from '../[locale]/(public)/sign-in/types';
import { getTranslations } from 'next-intl/server';

/**
 * @returns
 *   - on success: a redirect to /sign-in
 *   - on validation failure: an object { success:false, errors, values }
 */

export async function createUserAction(payload: ApiCreateUserData) {
  const result = apiCreateUserSchema.safeParse(payload);
  if (!result.success) {
    const msg = Object.values(result.error.flatten().fieldErrors).flat()[0]!;
    throw new Error(msg);
  }

  const res = await fetch(ENDPOINTS.USERS.CREATE, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(result.data),
    cache: 'no-store',
  });

  if (!res.ok) {
    let errMsg = 'Registration failed';
    try {
      const errBody = await res.json();
      errMsg = errBody.message ?? errMsg;
    } catch {}
    throw new Error(errMsg);
  }
}
export type RecoverPasswordState = {
  errors?: {
    email?: string;
    generic?: string;
  };
  success?: boolean;
  pending?: boolean;
};

export async function recoverPasswordAction(
  _: RecoverPasswordState,
  formData: FormData
): Promise<RecoverPasswordState> {
  const raw = Object.fromEntries(formData.entries()) as {
    email?: string;
    languageCode?: string;
  };

  const parsed = recoverPasswordSchema.safeParse(raw);
  if (!parsed.success) {
    return {
      success: false,
      errors: { email: parsed.error.flatten().fieldErrors.email?.[0] },
    };
  }

  const { email, languageCode } = parsed.data;

  const url = new URL(ENDPOINTS.AUTH.SEND_RECOVER_PASS, process.env.NEXTAUTH_URL);
  url.searchParams.set('language_code', languageCode);

  const res = await fetch(url.toString(), {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email }),
    cache: 'no-store',
  });

  if (!res.ok) {
    const body = await res.json().catch(() => null);
    return {
      success: false,
      errors: { generic: body?.message ?? 'Falha ao enviar e-mail' },
    };
  }

  return { success: true, errors: {} };
}

export async function loginAction(_prev: LoginState, formData: FormData): Promise<LoginState> {
  const t = await getTranslations();
  const email = formData.get('email')?.toString() ?? '';
  const password = formData.get('password')?.toString() ?? '';
  const parsed = loginFormApi.safeParse({ email, password });

  if (!parsed.success) {
    const { email: eErr, password: pErr } = parsed.error.flatten().fieldErrors;
    return {
      errors: {
        email: eErr?.[0],
        password: pErr?.[0],
      },
    };
  }

  const res = await fetch(ENDPOINTS.AUTH.LOGIN, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(parsed.data),
    cache: 'no-store',
  });

  if (res.status === 401) {
    return { errors: { password: t('errors.invalidCredentials') } };
  }

  if (!res.ok) {
    return { errors: { password: t('errors.unexpectedError') } };
  }

  return { errors: {}, success: true };
}

export async function resetPasswordAction(token: string, formData: FormData) {
  const values = Object.fromEntries(formData.entries()) as Record<string, string>;

  const result = formSchema.safeParse(values);

  if (!result.success) {
    const fieldErrors = (result.error as ZodError).flatten().fieldErrors;
    const referer = `/reset-password/${token}`;
    const url = new URL(referer, process.env.NEXTAUTH_URL);

    for (const [fieldName, msgs] of Object.entries(fieldErrors)) {
      if (msgs && msgs[0]) {
        url.searchParams.set(`error_${fieldName}`, msgs[0]);
      }
    }

    redirect(url.toString());
  }

  const res = await fetch(ENDPOINTS.AUTH.SEND_RECOVER_PASS, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      token,
      newPassword: result.data.newPassword,
    }),
    cache: 'no-store',
  });

  if (!res.ok) {
    const urlErr = new URL(`/reset-password/${token}`, process.env.NEXTAUTH_URL);
    urlErr.searchParams.set('error_generic', 'Falha ao redefinir senha. Tente novamente.');
    redirect(urlErr.toString());
  }

  redirect(`/sign-in?reset=success`);
}
