import { createTheme, PaletteMode } from '@mui/material/styles';
import { red } from '@mui/material/colors';
import type { OverridableStringUnion } from '@mui/types';
import type { ButtonPropsVariantOverrides } from '@mui/material/Button';

declare module '@mui/material/styles' {
  interface Palette {
    icon: {
      primary: string;
      editBg: string;
    };
  }
  interface PaletteOptions {
    icon?: {
      primary?: string;
      editBg?: string;
    };
  }
}

const lightMode: PaletteMode = 'light';
const palette = {
  mode: lightMode,
  primary: {
    main: '#3FBCD5',
    light: '#ECF0F3',
    dark: '#3A4556',
    contrastText: '#FFFFFF',
  },
  secondary: {
    light: '#BA68C8',
    main: '#9C27B0',
    dark: '#7B1FA2',
    contrastText: '#FFFFFF',
  },
  error: {
    light: '#EF5350',
    main: '#D32F2F',
    dark: '#C62828',
    contrastText: '#FFFFFF',
  },
  warning: {
    light: '#FF9800',
    main: '#EF6C00',
    dark: '#E65100',
    contrastText: '#FFFFFF',
  },
  success: {
    main: '#1E8573',
  },
  background: {
    default: '#F8F8F9',
    paper: '#FFFFFF',
  },
  text: {
    primary: '#49454F', // changed
    secondary: '#62767A', // changed
    disabled: 'rgba(0,0,0,0.38)',
  },
  // missing adding this to the palette typescript validation
  icon: {
    primary: '#0000008F',
  },
  info: {
    light: '#E5F6FD',
    main: '#014361',
    contrastText: '#FFFFFF',
  },
  divider: 'rgba(0,0,0,0.12)',
  action: {
    hover: '#ECF0F3',
    selected: 'rgba(84,95,113,0.08)',
    focus: 'rgba(84,95,113,0.12)',
    focusVisible: 'rgba(84,95,113,0.30)',
    disabled: 'rgba(0,0,0,0.38)',
    disabledBackground: 'rgba(0,0,0,0.12)',
  },
};

export const muiTheme = createTheme({
  palette,
  typography: {
    fontFamily: 'Barlow, sans-serif',
    h1: {
      fontSize: '4rem',
      textTransform: 'uppercase',
      fontWeight: 700,
      lineHeight: 1.167,
      letterSpacing: '-1.5px',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 700,
      lineHeight: 1.2,
      textTransform: 'uppercase',
      color: palette.primary.main,
    },
    h3: { fontSize: '2rem', fontWeight: 700, lineHeight: 1.2, color: palette.primary.main }, // same as h2 but no uppercase
    h4: { fontSize: '1.75rem', fontWeight: 400, lineHeight: 1.235, letterSpacing: '0.25px' },
    h5: { fontSize: '1.5rem', fontWeight: 500, lineHeight: 1.334 },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: 0,
      textTransform: 'uppercase',
    }, // same as regular text but uppercase
    alertTitle: {
      fontWeight: 500,
      fontSize: '1rem',
      lineHeight: '150%',
      letterSpacing: '0.15px',
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.75,
      letterSpacing: '0.15px',
      color: palette.text.primary,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.57,
      letterSpacing: '0.1px',
      color: palette.text.primary,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.15px',
      color: palette.text.primary,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.43,
      letterSpacing: '0.17px',
      color: palette.text.primary,
    },
    caption: { fontSize: '0.75rem', fontWeight: 400, lineHeight: 1.66, letterSpacing: '0.4px' },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 2.66,
      letterSpacing: '1px',
      textTransform: 'uppercase',
    },
    buttonMedium: {
      fontFamily: 'Barlow, sans-serif',
      fontWeight: 500,
      fontSize: '0.875rem',
      lineHeight: '24px',
      letterSpacing: '0.4px',
    },
  },

  shape: {
    borderRadius: 8,
    drawerWidth: 260,
    drawerMini: 64,
  },

  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          margin: 0,
          padding: 0,
          boxSizing: 'border-box',
          fontFamily: 'Montserrat, sans-serif',
          fontSize: '16px',
        },
        '*': {
          margin: 0,
          padding: 0,
          boxSizing: 'border-box',
        },
        ':root': {
          '--color-primary': '#3FBCD5',
          '--color-primary-rgb': '63, 188, 213',
          '--color-primary-light': '#ECF0F3',
          '--color-primary-light-rgb': '236, 240, 243',
          '--color-primary-dark': '#3A4556',
          '--color-primary-dark-rgb': '58, 69, 86',
          '--color-on-primary': '#FFFFFF',
          '--color-on-primary-rgb': '255, 255, 255',
          '--color-divider': '#e0e0e0',
          '--color-divider-rgb': '224, 224, 224',
          '--color-text-primary': '#49454F',
          '--color-text-primary-rgb': '73, 69, 79',
          '--color-text-secondary': '#62767A',
          '--color-text-secondary-rgb': '98, 118, 122',
          '--color-text-disabled': 'rgba(0,0,0,0.38)',
          '--color-info-light': '#E5F6FD',
          '--color-info-light-rgb': '229, 246, 253',
          '--color-info-main': '#014361',
          '--color-info-main-rgb': '1, 67, 97',
          '--color-info-contrast-text': '#FFFFFF',
          '--color-info-contrast-text-rgb': '255, 255, 255',
          '--color-action-hover': '#ECF0F3',
          '--color-action-hover-rgb': '236, 240, 243',
          '--color-action-selected': 'rgba(84,95,113,0.08)',
          '--color-action-focus': 'rgba(84,95,113,0.12)',
          '--color-action-focus-visible': 'rgba(84,95,113,0.30)',
          '--color-action-disabled': 'rgba(0,0,0,0.38)',
          '--color-action-disabled-background': 'rgba(0,0,0,0.12)',
          '--color-background-default': '#F8F8F9',
          '--color-background-default-rgb': '248, 248, 249',
        },
      },
    },

    MuiButton: {
      defaultProps: {
        disableElevation: true,
      },
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 600,
        },

        containedPrimary: ({ theme }) => ({
          color: theme.palette.primary.contrastText,
          backgroundColor: theme.palette.primary.main,
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: theme.palette.primary.dark,
          },
          '&.Mui-disabled': {
            backgroundColor: theme.palette.action.disabledBackground,
            color: theme.palette.action.disabled,
          },
        }),

        outlinedPrimary: ({ theme }) => ({
          color: theme.palette.primary.main,
          border: `1px solid ${theme.palette.primary.main}`,
          backgroundColor: 'white',
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
          '&.Mui-disabled': {
            borderColor: theme.palette.action.disabledBackground,
            color: theme.palette.action.disabled,
          },
        }),

        textPrimary: ({ theme }) => ({
          color: theme.palette.primary.main,
          padding: '8px 11px',
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
          '&.Mui-disabled': {
            color: theme.palette.action.disabled,
          },
        }),

        sizeLarge: {
          padding: '8px 22px',
          minWidth: 124,
          height: 42,
          fontSize: '1rem',
        },

        sizeMedium: {
          padding: '6px 16px',
          minWidth: 108,
          height: 40,
          fontSize: '0.875rem',
        },

        sizeSmall: {
          padding: '4px 10px',
          minWidth: 93,
          height: 36,
          fontSize: '0.8125rem',
        },
      },
      variants: [
        {
          props: {
            variant: 'destructive' as OverridableStringUnion<
              'text' | 'outlined' | 'contained',
              ButtonPropsVariantOverrides
            >,
          },
          style: {
            color: '#fff',
            backgroundColor: red.A400,
            '&:hover': {
              backgroundColor: red.A700,
            },
          },
        },
        {
          props: {
            variant: 'ghost' as OverridableStringUnion<
              'text' | 'outlined' | 'contained',
              ButtonPropsVariantOverrides
            >,
          },
          style: {
            backgroundColor: 'transparent',
            border: '1px solid rgba(0,0,0,0.12)',
            '&:hover': {
              backgroundColor: 'rgba(0,0,0,0.04)',
            },
          },
        },
      ],
    },

    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
        size: 'small',
      },
    },

    MuiDrawer: {
      styleOverrides: {
        paper: ({ theme }) => ({
          width: theme.shape.drawerWidth,
          backgroundColor: theme.palette.common.white,
          borderRight: `1px solid ${theme.palette.divider}`,
          boxShadow: 'none',
        }),
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.common.white,
          color: theme.palette.grey[800],
          boxShadow: 'none',
          borderBottom: `1px solid ${theme.palette.divider}`,
        }),
      },
    },
    MuiToolbar: {
      styleOverrides: {
        root: {
          minHeight: 56,
        },
      },
    },
    MuiListSubheader: {
      styleOverrides: {
        root: ({ theme }) => ({
          ...theme.typography.overline,
          color: theme.palette.grey[600],
          marginTop: theme.spacing(3),
          marginBottom: theme.spacing(1),
        }),
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 8,
          '&.Mui-selected': {
            backgroundColor: theme.palette.action.hover,
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          },
        }),
      },
    },
    MuiTable: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,
          boxShadow: theme.shadows[1],
        }),
      },
    },

    MuiTableHead: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.background.paper,
          '& .MuiTableCell-root': {
            ...theme.typography.subtitle2,
            color: theme.palette.text.primary,
            borderBottom: `2px solid ${theme.palette.text.primary}`,
            padding: theme.spacing(2),
            '&:last-of-type': { textAlign: 'right', width: 56 },
          },
        }),
      },
    },

    MuiTableBody: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.background.paper,
          '& .MuiTableRow-hover:hover': {
            backgroundColor: theme.palette.action.hover,
          },
          '& .MuiTableRow-root.Mui-selected': {
            backgroundColor: theme.palette.action.selected,
            '&:hover': { backgroundColor: theme.palette.action.selected },
          },
        }),
      },
    },

    MuiTableCell: {
      styleOverrides: {
        root: ({ theme }) => ({
          padding: theme.spacing(1.5, 2),
          borderBottom: `1px solid ${theme.palette.divider}`,
          '&:first-of-type': { paddingLeft: theme.spacing(2.5) },
          '&:last-of-type': { textAlign: 'right', width: 56 },
          '&.header-cell': {
            borderBottom: 'none',
            paddingTop: '30px',
            paddingBottom: '0px',
          },
          '&.filter-cell': {
            paddingTop: '0',
            paddingBottom: '12px',
            paddingLeft: '16px',
            paddingRight: '16px',
          },
        }),
      },
    },

    MuiTablePagination: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderTop: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.background.paper,
          '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
            ...theme.typography.caption,
            color: theme.palette.text.secondary,
          },
        }),
        actions: ({ theme }) => ({
          '.MuiIconButton-root': {
            padding: 8,
            borderRadius: 999,
            '&:hover': { backgroundColor: theme.palette.action.hover },
          },
        }),
      },
    },
    MuiCard: {
      defaultProps: {
        elevation: 1,
      },
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,

          backgroundColor: theme.palette.background.paper,

          boxShadow: theme.shadows[1],

          padding: theme.spacing(3),

          display: 'flex',
          flexDirection: 'column',
          gap: theme.spacing(5),
        }),
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: ({ theme, ownerState }) => ({
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: theme.shape.borderRadius,
          boxShadow: 'none',

          backgroundColor: theme.palette.grey[50],

          ...(ownerState && ownerState.expanded
            ? { backgroundColor: theme.palette.grey[100] }
            : {}),
          '&.Mui-expanded': { margin: 0 },
          '&:not(:last-of-type)': { borderBottom: 0 },
        }),
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: ({ theme }) => ({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: theme.spacing(1),
          padding: theme.spacing(2, 5),
          '&.Mui-expanded': {
            minHeight: 'auto',
          },
          '& .MuiAccordionSummary-content': {
            margin: 0,
          },
          '& .MuiAccordionSummary-expandIconWrapper': {
            marginRight: 0,
          },
        }),
        content: {
          margin: 0,
          '&.Mui-expanded': {
            margin: 0,
          },
        },
        expandIconWrapper: {
          marginRight: 0,
        },
      },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: ({ theme }) => ({
          padding: theme.spacing(1),
        }),
      },
    },
    MuiSelect: {
      defaultProps: {
        size: 'small',
        MenuProps: {
          PaperProps: { square: false },
          elevation: 8,
        },
      },
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.background.paper,
          borderRadius: theme.shape.borderRadius,
          height: 40,
          maxWidth: 280,
          width: '100%',
          '& .MuiSelect-select': {
            paddingLeft: theme.spacing(1.5),
            paddingRight: theme.spacing(1.5),
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(1),
          },
        }),
        icon: ({ theme }) => ({
          color: theme.palette.primary.main,
        }),
      },
    },

    MuiMenu: {
      styleOverrides: {
        paper: ({ theme }) => ({
          borderRadius: theme.shape.borderRadius,
          paddingLeft: 4,
          paddingRight: 4,
        }),
      },
    },

    MuiMenuList: {
      defaultProps: {
        dense: false,
        disablePadding: false,
      },
      styleOverrides: {
        root: {
          paddingTop: 0,
          paddingBottom: 0,
        },
      },
    },

    MuiMenuItem: {
      styleOverrides: {
        root: ({ theme }) => ({
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
            borderRadius: theme.shape.borderRadius,
          },

          '&:first-of-type:hover': {
            borderTopLeftRadius: theme.shape.borderRadius,
            borderTopRightRadius: theme.shape.borderRadius,
          },
          '&:last-of-type:hover': {
            borderBottomLeftRadius: theme.shape.borderRadius,
            borderBottomRightRadius: theme.shape.borderRadius,
          },
        }),
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: ({ theme }) => ({
          '& input[readonly]': {
            width: '100%',
            height: 55,
            padding: '12px 16px',
            border: `1px solid ${theme.palette.grey[300]}`,
            borderRadius: 3,
            backgroundColor: theme.palette.grey[50],
            fontSize: '1rem',
            fontWeight: 400,
            lineHeight: '150%',
            color: theme.palette.grey[600],
            cursor: 'default',
            caretColor: 'transparent',
            '&:focus': {
              outline: 'none',
            },
          },
        }),
      },
    },
    MuiAlert: {
      defaultProps: {
        variant: 'standard',
      },
      styleOverrides: {
        standardInfo: {
          backgroundColor: '#E5F6FD',
        },
        root: {
          padding: '6px 16px',
          minHeight: '48px',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
        },
        icon: {
          marginRight: '12px',
          fontSize: '20px',
        },
        message: {
          padding: 0,
          display: 'flex',
          alignItems: 'center',
        },
        action: {
          display: 'none',
        },
      },
    },
    MuiDialogTitle: {
      styleOverrides: {
        root: ({ theme }) => ({
          ...theme.typography.h6,
          color: theme.palette.text.primary,

          minHeight: '32px',
        }),
      },
    },
  },
});
