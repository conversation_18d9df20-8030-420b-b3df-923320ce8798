import '@mui/material/styles';
import '@mui/system';

declare module '@mui/material/styles' {
  interface Shape {
    drawerWidth: number;
    drawerMini: number;
    borderRadius: number;
  }

  interface ThemeOptions {
    shape?: {
      drawerWidth?: number;
      drawerMini?: number;
      borderRadius?: number;
    };
  }

  interface TypeAction {
    focusVisible?: string;
  }
  interface TypographyVariants {
    alertTitle: React.CSSProperties;
    buttonMedium: React.CSSProperties;
  }
  interface TypographyVariantsOptions {
    alertTitle?: React.CSSProperties;
    buttonMedium?: React.CSSProperties;
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    destructive: true;
    ghost: true;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    alertTitle: true;
    buttonMedium: true;
  }
}

declare module '@mui/system' {
  interface Shape {
    drawerWidth: number;
    drawerMini: number;
  }
}

declare module '@mui/material/OutlinedInput' {
  interface OutlinedInputPropsVariantOverrides {
    readonly: true;
  }
}

import '@mui/material/TextField';

declare module '@mui/material/TextField' {
  interface TextFieldPropsVariantOverrides {
    readonly: true;
  }
}
