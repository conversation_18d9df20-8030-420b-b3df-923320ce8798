:root {
  --dp-range-bg: #e0f7fa; /* definitive trail */
  --dp-selected-bg: var(--color-primary, #26c6da); /* fixed circles */
  --dp-preview-bg: rgba(var(--color-primary-rgb), 0.12); /* preview trail */
}

/* ========================================================
   Container and header
   ======================================================== */
.react-datepicker {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
  font-size: 1rem;
  background: #fff !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
  border: none !important;
  min-width: 320px !important;
  padding: 20px !important;
}

.react-datepicker__header {
  background: #fff !important;
  border-bottom: none !important;
  border-radius: 16px 16px 0 0 !important;
}

/* ========================================================
   Days (basic size + transitions)
   ======================================================== */
.react-datepicker__day-name,
.react-datepicker__day {
  width: 40px !important;
  height: 40px !important;
  line-height: 40px !important;
  margin: 0 !important;
  font-size: 1rem !important;
  font-weight: 400 !important;
}

.react-datepicker__day-name {
  color: #757575 !important;
  font-weight: 500 !important;
}

.react-datepicker__day {
  position: relative;
  z-index: 1;
  transition: background 0.2s;
}

/* ========================================================
   Isolated "selected" states
   ======================================================== */
.react-datepicker__day--selected:not(.react-datepicker__day--range-start):not(
    .react-datepicker__day--range-end
  ) {
  background: var(--dp-selected-bg) !important;
  color: #000 !important;
  border-radius: 50% !important;
}

/* ========================================================
   Definitive range (start / end + trail)
   ======================================================== */
.react-datepicker__day--in-range {
  background: var(--dp-range-bg) !important;
  color: var(--dp-selected-bg) !important;
  border-radius: 0 !important;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  position: relative;
  background: none !important; /* circle will come via ::after */
  color: #000 !important;
}

/* square that sticks to the circle */
.react-datepicker__day--range-start::before,
.react-datepicker__day--range-end::before {
  content: '';
  position: absolute;
  top: 0;
  height: 100%;
  width: 50%;
  background: var(--dp-range-bg);
  z-index: -2;
}

.react-datepicker__day--range-start::before {
  left: 50%;
}

.react-datepicker__day--range-end::before {
  left: 0;
}

/* circle above the square */
.react-datepicker__day--range-start::after,
.react-datepicker__day--range-end::after {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--dp-selected-bg);
  border-radius: 50%;
  z-index: -1;
}

/* ========================================================
   Preview (hover while choosing the second day)
   ======================================================== */
.react-datepicker__day--in-selecting-range {
  background: var(--dp-preview-bg) !important;
  color: var(--dp-selected-bg) !important;
  border-radius: 0 !important;
}

.react-datepicker__day--selecting-range-start,
.react-datepicker__day--selecting-range-end {
  position: relative;
  background: none !important;
  color: var(--color-background) !important;
}

/* preview square */
.react-datepicker__day--selecting-range-start::before,
.react-datepicker__day--selecting-range-end::before {
  content: '';
  position: absolute;
  top: 0;
  height: 100%;
  width: 50%;
  background: var(--dp-preview-bg);
  z-index: 0;
}

.react-datepicker__day--selecting-range-start::before {
  left: 50%;
}

.react-datepicker__day--selecting-range-end::before {
  left: 0;
}

/* preview circle */
.react-datepicker__day--selecting-range-start::after,
.react-datepicker__day--selecting-range-end::after {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--dp-preview-bg);
  border-radius: 50%;
  z-index: 1;
}

/* ========================================================
   Hover & keyboard
   ======================================================== */
.react-datepicker__day:hover,
.react-datepicker__day--keyboard-selected {
  background: var(--dp-selected-bg) !important;
  color: #000 !important;
  border-radius: 50% !important;
}

.react-datepicker__day--in-range:hover:not(.react-datepicker__day--range-start):not(
    .react-datepicker__day--range-end
  ) {
  border-radius: 0 !important;
}

/* ========================================================
   Others (arrows, custom header, accessibility)
   ======================================================== */
.react-datepicker__navigation {
  top: 18px !important;
}

.react-datepicker__navigation-icon::before {
  border-width: 2px 2px 0 0 !important;
  width: 8px !important;
  height: 8px !important;
}

.react-datepicker__triangle {
  display: none !important;
}

/* custom header (month/year + selects + buttons) */
.react-datepicker__custom-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
  padding: 0 8px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}

/* Style the select dropdowns */
.react-datepicker__custom-header select {
  font-family: inherit;
  font-size: 1rem;
  border: 1px solid var(--color-divider);
  background: var(--color-background);
  padding: 4px 32px 4px 12px;
  border-radius: 8px;
  outline: none;
  appearance: none;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s, border 0.2s;
  box-shadow: none;
  background-image: url("data:image/svg+xml;utf8,<svg fill='var(--color-divider)' height='20' viewBox='0 0 24 24' width='20' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 20px 20px;
  padding-right: 32px; /* space for the icon */
}

.react-datepicker__custom-header select:hover {
  background-color: var(--color-background);
  border: 1px solid var(--color-primary);
}

/* Hide the navigation buttons */
.react-datepicker__custom-header button {
  display: none;
}

/* week with trail crossing the line break */
.react-datepicker__week {
  position: relative;
  overflow: hidden;
}

.react-datepicker__week.has-in-range {
  background: var(--dp-range-bg) !important;
  border-radius: 20px;
  z-index: 0;
}
