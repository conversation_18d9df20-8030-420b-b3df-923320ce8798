import 'next-auth';
import 'next-auth/jwt';
import { DefaultSession } from 'next-auth';
import { Ability } from '@/lib/types/ability';

declare module 'next-auth' {
  interface Session extends DefaultSession {
    accessToken?: string;
    refreshToken?: string;
    ability: Ability;
    raw?: RawUser;
    user: {
      id: string;
      email?: string | null;
      roles?: string[];
      image?: string | null;
    } & DefaultSession['user'];
  }
  interface User extends AdapterUser {
    id: string;
    ability: Ability;
    roles: string[];
    accessToken?: string;
    refreshToken?: string;
    raw?: RawUser;
  }
}

declare module 'next-auth/jwt' {
  interface JWT extends DefaultJWT {
    id?: string;
    raw?: RawUser;
    roles?: string[];
    ability: Ability;
    accessToken?: string;
    refreshToken?: string;
  }
}

// ---------- TOKEN V1 ----------
type TokenProfile = { role: string; subrole?: string };
export type DecodedTokenV1 = {
  userId: string;
  name: string;
  email: string;
  profiles: TokenProfile[];
  functionalities: string[];
  iat: number;
  exp: number;
};

// ---------- TOKEN V2 ----------
export type PermObject = {
  code: string;
  permissions: { value: string; action: string }[];
};
export type DecodedTokenV2 = {
  userId: string;
  version: string;
  role: Array<string | PermObject>;
  type: string;
  iat: number;
  exp: number;
};
