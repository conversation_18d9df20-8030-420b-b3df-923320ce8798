import { FormTabs } from './forms';

export type CRMHistoryType = {
  category:
    | 'OPPORTUNITY'
    | 'INTERACTION_OPPORTUNITY'
    | 'INTERACTION_SYSTEM'
    | 'INTERACTION_EXTERNAL';
  id: string;
  description: string;
  type: string;
  state: string;
  madeBy: string;
  creationDate: Date;
  enrollment: string;
  extraData?: string;
};

export type ContactPersonType = {
  id: string;
  name?: string;
  email?: string;
  mobile?: string;
  phone?: string;
  nif?: string;
  socialSecurityNumber?: string;
  identificationType?: string;
  identificationValidity?: Date;
  identificationNumber?: string;
  identificationFile?: string;
  address?: string;
  postalCode?: string;
  locality?: string;
  district?: string;
  municipality?: string;
  parish?: string;
  country?: string;
  birthPlace?: string;
  profession?: string;
  employerEntity?: string;
  contractType?: string;
  birthDate?: Date;
  gender?: string;
  iban?: string;
  educationLevel?: string;
  notes?: string;
  rgpd?: Record<string, boolean>;
  relationships?: any;
  entities?: Array<{ id: string; name: string; description: string }>;
  attachments?: Record<string, unknown>;
  crmHistory?: Array<CRMHistoryType>;
  enrollments?: Record<string, unknown>;
  clientId?: string;
};

export const ContactUserSubTab = {
  ...FormTabs.DETAILS,
  ...FormTabs.RGPD,
  ...FormTabs.RELATIONSHIPS,
  ...FormTabs.ENTITIES,
  ...FormTabs.ATTACHMENTS,
  ...FormTabs.CRM_HISTORY,
  ...FormTabs.ENROLLMENTS,
  ...FormTabs.PAYMENTS,
};
export type ContactUserSubTabType = (typeof ContactUserSubTab)[keyof typeof ContactUserSubTab];

export type SelectOption = { value: string; label: string };
export type SelectOptions = {
  district?: SelectOption[];
  municipality?: SelectOption[];
  parish?: SelectOption[];
  country?: SelectOption[];
  contractType?: SelectOption[];
  gender?: SelectOption[];
  educationLevel?: SelectOption[];
  relationshipTypes?: SelectOption[];
  users?: SelectOption[];
  fileTypes?: SelectOption[];
  employer?: SelectOption[];
};
