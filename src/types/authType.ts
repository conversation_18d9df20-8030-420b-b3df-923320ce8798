import { ApiCreateUserData, LoginFormData } from '../schemas/authSchema';
import FunctionalityEnum from './functionalityEnum';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RecoverPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  newPassword: string;
  otp: string;
}

export interface ResetPasswordRequestToApi {
  email: string | null;
  newPassword: string;
  otp: string;
}

export interface LoginResponse {
  partialToken: string;
  accessToken: string;
  refreshToken: string;
  functionalities: FunctionalityEnum[];
}

export interface AuthResponse {
  token: string;
  user: {
    id: string;
    email: string;
  };
}

interface User {
  email: string;
  nif: number;
}

export interface AuthPayload {
  userId: string;
  user: User;
  role: string[];
  language: string;
  type: string;
  iat: number;
  exp: number;
}

export interface AccountActivateRequest {
  email: string;
  otp: string;
  password: string;
  phone?: string;
}

export interface AuthContextType {
  user: AuthPayload | null;
  partialToken: string | null;
  handleLogin: (credentials: LoginFormData) => Promise<void>;
  handleOtp: (opt: string) => Promise<void>;
  logout: () => void;
  hasAccess: (functionality: FunctionalityEnum) => boolean;
  handleAccountActivate: (data: AccountActivateRequest) => Promise<boolean>;
  handleResetPassword: (data: RecoverPasswordRequest) => void;
  handleRedefinePassword: (data: ResetPasswordRequest) => Promise<boolean>;
  handleCreateEntity: (data: ApiCreateUserData) => Promise<boolean>;
}
