import { BoolType, VIEW } from '@/constants/enums';

export type Tab = {
  id: string;
  // href: string;
  title?: string;
  isOpen?: boolean;
  dependsOn?: { id: string; canClose: boolean }[];
  dependentBy?: { id: string }[];
  hasChanges?: boolean;
  view?: VIEW;
  subTab?: string;
  // need to add here modal behavior also
};

export type TabStore = {
  openTabs: Tab[];
  formContext: Record<string, any>;
  formGetValues: { id?: string; getValues?: () => void };
  setFormGetValues: (args: { id?: string; getValues?: () => void; clear?: boolean }) => void;
  updateSubTab: (args: { subTab: string }) => void;
  openTab: () => Tab | undefined;
  openSubTab: () => string | undefined;
  addTab: (args: {
    tab: Tab;
    changeOpenTab?: boolean;
    isReplace?: boolean;
    addDependencyRelation?: boolean;
  }) => void;
  removeTab: (args: { tab: Tab }) => boolean;
  saveContext: () => void;
  changeOpenTab: (args: {
    tab: Tab;
    changeOpenTab?: boolean;
    isReplace?: boolean;
    shouldAddDependencyRelation?: boolean;
  }) => void;
  checkIfTabExists: (args: {
    tab: Tab;
    changeOpenTab?: boolean;
    isReplace?: boolean;
    shouldAddDependencyRelation?: boolean;
  }) => boolean;
  maxOpenTabs: number;
  isLimitReached: () => boolean;
  checkIfTabCanBeClosed: (args: { tab: Tab }) => boolean;
  setDependentByTabsCanClose: (args: { tab: Tab }) => void;
  addDependencyRelation: (args: { dependentTab: Tab; dependedTab: Tab }) => {
    updatedDependentTab: Tab;
    updatedDependedTab: Tab;
  };
  constructTab: (args: {
    // href?: string;
    isStatic: BoolType;
    id: string;
    title?: string;
    view?: VIEW;
  }) => Tab | undefined;
  constructDynamicTab: (args: {
    // href: string;
    id: string;
    title?: string;
    view?: VIEW;
  }) => Tab | undefined;
  constructStaticTab: (args: { id: string; view?: VIEW }) => Tab | undefined;
  setFormContext: (args: { id?: string; formValues?: any; clear?: boolean }) => void;
  updateHasChanges: (args: { id: string; hasChanges: boolean }) => void;
  updateTabView: (args: { view: VIEW; id?: string }) => void;
};
