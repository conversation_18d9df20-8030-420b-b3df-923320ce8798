{"tabs": {"new": {"profiles": "New profile", "users": "New user", "leads": "New lead", "opportunities": "New opportunity", "entities": "New entity"}}, "profiles": {"main": {"title": "Profiles", "subtitle": "Manage different user profiles and set their access levels.", "label": "New profile", "action": "New profile"}, "record": {"title": "Profile", "subtitle": "Configure this profile's permissions and access levels on the platform.", "new": "Create profile", "subtitleNew": "Create a new profile and configure its permissions and access levels."}}, "crm": {"leads": {"main": {"title": "Leads", "subtitle": "Manage and track all received information requests.", "action": "Import Leads"}, "record": {"title": "Lead details", "label": "View contact record", "action": "Manage Lead", "secondaryAction": "View contact record"}}, "opportunities": {"main": {"title": "Opportunities", "subtitle": "Manage and track all ongoing opportunities.", "action": "New opportunity"}, "record": {"title": "Opportunity record id", "new": "New opportunity"}, "new": {"title": "New opportunity record", "subtitle": "Fill in the details below to successfully close the lead and create a new opportunity.", "successMessage": "New opportunity created successfully!"}, "title": "Opportunities", "subtitle": "Manage and track all ongoing opportunities.", "label": "New opportunity", "opportunity": {"title": "Opportunity details"}, "actions": {"view": "View Details", "delete": "Delete Opportunity"}, "edit": {"successMessage": "Opportunity updated successfully!"}, "dialog": {"confirmDeleteTitle": "Delete Opportunity", "confirmDeleteSubtitle": "Are you sure you want to delete this opportunity?", "deleteDescription": "This action is permanent and cannot be undone.", "delete": "Delete", "cancel": "Cancel", "columns": {"description": "Description", "type": "Type", "state": "State", "scheduledDate": "Scheduled date", "doneDate": "Done date", "doneBy": "Done by", "enrollment": "Enrollment"}}}}, "contacts": {"people": {"main": {"title": "People", "subtitle": "Manage and track all people to keep the platform always updated and organized.", "action": "New person"}, "record": {"title": "Contact record id", "new": "New person"}}, "entities": {"main": {"title": "Entities", "subtitle": "Manage and keep updated the contacts of entities associated with the platform", "action": "New entity", "delete": {"title": "Delete entity", "action": "Delete", "subTitleQuestion": "Are you sure you want to delete this entity?", "subTitle": "This action is irreversible. The entity will be removed from the SA database, along with all associated data.", "success": "Entity #{id} deleted successfully!"}}, "record": {"title": "Entity record id", "new": "New entity"}}}, "users": {"main": {"title": "Users", "subtitle": "Manage and track all users to keep the platform always updated and organized.", "action": "New user"}, "record": {"title": "Name XXXX user", "new": "New user", "subtitle": "Update the user's information and define the locations where they will operate."}}, "teams": {"main": {"title": "Teams", "subtitle": "Manage the different active teams and assign users to each one.", "action": "New team"}, "newTeam": {"title": "New team"}, "editTeam": {"title": "Edit team"}, "deleteTeam": {"title": "Delete team", "subtitle": "Are you sure you want to delete this team?", "description": "This action is irreversible. The team will be removed from the SA database, along with all associated data."}, "buttons": {"cancel": "Cancel", "delete": "Delete", "create": "Create", "save": "Save"}, "table": {"actions": {"edit": "Edit team", "delete": "Delete team"}, "columns": {"name": "Name", "pole": "Poles", "assignedUsers": "Assigned users"}}, "form": {"name": {"label": "Name", "placeholder": "Enter department..."}, "pole": {"label": "Poles", "placeholder": "Select location"}, "users": {"label": "Users", "available": "Available", "assigned": "Assigned", "searchPlaceholder": "Search users...", "emptyMessage": "Team without assigned users."}}}}