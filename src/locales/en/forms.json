{"subtab": {"details": "Details", "rgpd": "GDPR", "relationships": "Relationships", "entities": "Entities", "attachments": "Attachments", "crmHistory": "CRM History", "enrollments": "Enrollments", "detailsEnrollment": "Enrollment Details", "payments": "Billing", "basicInformation": "Basic Information", "localization": "Location", "atribution": "Assignment", "course": "Course", "message": "Message", "interaction": "Interactions", "invoicing": "Billing", "groups": "Groups", "protocols": "Protocols", "basicInfo": "Basic Information", "location": "Location", "assignment": "Assignment", "userInfo": "User Information", "profileDataTitle": "Profile data", "labelTextArea": "Description", "accordionTitle": "Permissions", "assignedHubs": "Assigned Hubs"}, "placeholder": "Insert", "id": {"label": "ID"}, "entities": {"label": "Entities", "placeholder": "Select entities...", "empty": "There are no entities associated with this contact.", "add": "Add entity", "type": {"label": "Entity type", "placeholder": "Select type"}}, "description": {"label": "Description", "placeholder": "Enter description"}, "name": {"label": "Name", "placeholder": "Enter name"}, "email": {"label": "Email", "placeholder": "Enter email"}, "mobile": {"label": "Mobile", "placeholder": "Enter mobile"}, "contact": {"label": "Contact", "placeholder": "Enter contact", "add": "Add contact", "edit": "Edit contact"}, "phone": {"label": "Phone", "placeholder": "Enter phone"}, "nif": {"label": "NIF", "placeholder": "Enter NIF"}, "center": {"label": "<PERSON><PERSON>", "placeholder": "Select hub..."}, "relationships": {"empty": "There are no relationships associated with this contact.", "add": "Add relationship", "label": "Contact Name", "placeholder": "Search contact...", "type": {"label": "Relationship", "placeholder": "Select relationship"}}, "observations": {"label": "Observations", "placeholder": "Enter observations..."}, "attachments": {"empty": "There are no files associated with this contact.", "label": "Add file", "subLabel": "Imported files", "modal": {"label": "Attach file", "fileNamePlaceholder": "Enter name", "filePlaceholder": "File", "fileTypePlaceholder": "Select type", "descriptionPlaceholder": "Enter description", "tagsPlaceholder": "Enter tags", "confirmLabel": "Attach", "cancelLabel": "Cancel"}, "add": "Add attachment", "attach": "Attach", "noAttachments": "No attachments", "success": "Attachment added successfully!", "fields": {"file": {"placeholder": "File name", "buttonLabel": "File"}, "type": {"label": "Type", "placeholder": "Enter type", "options": {}}, "labels": {"label": "Tags"}, "startDate": {"label": "Start Date", "placeholder": "Select Date"}, "endDate": {"label": "End Date", "placeholder": "Select Date"}}}, "interaction": {"add": "Add interaction", "hubs": {"notAssigned": "No hub assigned", "assigned": "<PERSON><PERSON> assigned", "assign": "Assign hub"}}, "contacts": {"entities": {"title": "New entity record"}}, "assignmentDate": {"label": "Assignment Date"}, "creationDate": {"label": "Creation Date"}, "assignedCampus": {"label": "Assigned Hub"}, "campus": {"label": "<PERSON><PERSON>"}, "preferredSchedule": {"label": "Preferred Schedule"}, "message": {"label": "Message"}, "upload": {"label": "Upload"}, "fileName": {"label": "File name"}, "assignedHubs": {"section": "Assigned hubs", "notAssigned": "No hub assigned", "assigned": "<PERSON><PERSON> assigned", "assign": "Assign hub"}, "label": {"cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete"}, "opportunityTypes": {"registration": "Registration"}, "interactionTypes": {"call": "Phone Call", "mail": "Mail", "sms": "SMS"}, "schedule": {"label": "Schedule", "placeholder": "Select schedule", "options": {"morning": "Morning Shift", "afternoon": "Afternoon Shift", "night": "Evening Shift", "none": "N/A", "saturday": "Saturday"}}, "baseEducation": {"label": "Base Education", "options": {"level2": "Level 2", "level3": "Level 3", "level2And3": "Levels 2 and 3"}}, "baseType9": {"label": "Base education type (9th)", "placeholder": "Select modality", "options": {"ufcd25": "UFCD 25 hours", "ufcd50": "UFCD 50 hours", "A": "Type A (level 2)", "B": "Type B (level 2)", "C": "Type C (level 2)"}}, "baseType12": {"label": "Base education type (12th)", "placeholder": "Select modality", "options": {"A": "Type A (level 3)", "B": "Type B (level 3)", "C": "Type C (level 3)", "portaria": "Ordinance 357/2007", "ufcd25": "UFCD 25 hours", "ufcd50": "UFCD 50 hours"}}, "trainee": {"label": "Trainee", "placeholder": "Select trainee"}, "traineeType": {"label": "Trainee type", "placeholder": "Select type", "options": {"customer": "Individual", "business": "Company"}}, "socialSecurityNumber": {"label": "Social Security Number", "placeholder": "Enter number"}, "identificationType": {"label": "ID Document Type", "placeholder": "Select document"}, "identificationValidity": {"label": "ID Document Validity", "placeholder": "MM/DD/YYYY"}, "identificationNumber": {"label": "ID Document Number", "placeholder": "Enter number"}, "identificationFile": {"label": "ID Document File", "placeholder": "Enter file"}, "address": {"label": "Address", "placeholder": "Enter address"}, "postalCode": {"label": "Postal Code", "placeholder": "Enter postal code"}, "locality": {"label": "Locality", "placeholder": "Enter locality"}, "district": {"label": "District", "placeholder": "Enter district"}, "municipality": {"label": "Municipality", "placeholder": "Enter municipality"}, "parish": {"label": "Parish", "placeholder": "Enter parish"}, "country": {"label": "Country", "placeholder": "Enter country"}, "birthPlace": {"label": "Birthplace", "placeholder": "Enter birthplace"}, "profession": {"label": "Profession", "placeholder": "Enter profession"}, "employerEntity": {"label": "Employer Entity", "placeholder": "Search entity"}, "contractType": {"label": "Contract Type", "placeholder": "Select type"}, "birthDate": {"label": "Birth Date", "placeholder": "MM/DD/YYYY"}, "gender": {"label": "Gender", "placeholder": "Select gender"}, "educationLevel": {"label": "Education Level", "placeholder": "Select education level"}, "iban": {"label": "IBAN", "placeholder": "Enter IBAN"}, "notes": {"label": "Notes", "placeholder": "Enter notes"}, "website": {"label": "Website", "placeholder": "Enter website url..."}, "type": {"label": "Type", "placeholder": "Select type"}, "state": {"label": "State", "placeholder": "Enter state...", "options": {"success": "Success", "insuccess": "Failure"}}, "scheduledDate": {"label": "Scheduled Date"}, "doneDate": {"label": "Completion Date"}, "doneBy": {"label": "Completed by"}, "createdAt": {"label": "Creation Date"}, "text": {"label": "Text", "placeholder": "Enter text..."}, "opportunityState": {"label": "Opportunity State", "placeholder": "Select new state"}, "closingResult": {"label": "Closing Result", "placeholder": "Select result"}, "closingDate": {"label": "Closing Date"}, "closingDescription": {"label": "Closing Description"}, "entity": {"label": "Entity", "placeholder": "Search entity..."}, "assignedTo": {"label": "Assigned to", "placeholder": "Search user..."}, "stateDate": {"label": "State Date"}, "closeDate": {"label": "Closing Date"}, "resultState": {"label": "Closing Result"}, "resultReason": {"label": "Closing Reason", "placeholder": "Enter reason..."}, "resultDescription": {"label": "Closing Description", "placeholder": "Enter description"}, "createdBy": {"label": "Created by"}, "sourceLead": {"label": "Source lead"}, "course": {"label": "Course", "placeholder": "Enter course..."}, "modality": {"label": "Training modality", "placeholder": "Select modality", "options": {"inPerson": "In-person", "bLearning": "B-Learning", "eLearning": "E-Learning"}}, "cliente_formacao": {"label": "Training client"}, "username": {"label": "Username", "placeholder": "Enter username..."}, "profile": {"label": "Profile", "admin": "Administrator", "supervisor": "Sales Supervisor", "placeholder": "Select profile"}, "status": {"label": "Status", "placeholder": "Select status", "active": "Active", "inactive": "Inactive"}, "language": {"label": "Language", "pt": "Portuguese", "en": "English", "placeholder": "Select language"}, "title": "Close lead unsuccessfully ({id})", "inputLabel": "Indicate the reason for closing and describe the motive", "selectReason": "Select the reason", "textFieldPlaceholder": "Enter a description", "cliente_faturacao": {"label": "Billing client"}, "entidade_empregadora": {"label": "Employer entity"}, "parceria": {"label": "Partnership"}, "capital_social": {"label": "Share Capital", "placeholder": "Enter share capital..."}, "autorizar_dados_pessoais": {"label": "Do you authorize the use of my personal data, contained in this form, under the terms of Law No. 67/98, of October 26, for the purpose of computer processing of processes and homologation/certification, statistical calculation and monitoring of the training carried out by the certifying entity, namely the Directorate-General for Employment (DGERT)?"}, "autorizar_processamento_dados": {"label": "Do you authorize the Training Entity to carry out the computer processing of the data and to keep them for the period in which the process is open for administrative, evaluation and audit purposes? During this period, the data subject reserves the right to access and rectify them."}, "autorizar_fotocopia": {"label": "Do you authorize the data contained in this form to be used by the Training Entity for sending commercial and/or promotional content?"}, "autorizar_conteudos_promocionais": {"label": "Do you authorize the data contained in this form to be used by the Training Entity for sending commercial and/or promotional content?"}, "autorizar_comunicacoes_institucionais": {"label": "Do you authorize the data contained in this form to be used by the Training Entity for sending institutional and informative communications?"}, "praticas": {"label": "Internships"}, "estagios": {"label": "Internships"}, "maintenanceContract": {"label": "Maintenance Contract"}, "protocolTypes": {"label": "Protocol Types", "options": {"Práticas": "Internships", "Estágios": "Internships"}}, "addressesType": {"label": "Type", "placeholder": "Enter type", "options": {}}, "addresses": {"label": "Addresses", "add": "Add Address", "noAddresses": "No addresses", "success": "Address added successfully!"}, "rgpd": {"consent_1": "Do you authorize the use of my personal data, contained in this form, under the terms of Law No. 67/98, of October 26, for the purpose of computer processing of processes and homologation/certification, statistical calculation and monitoring of the training carried out by the certifying entity, namely the Directorate-General for Employment (DGERT)?", "consent_2": "Do you authorize the Training Entity to carry out the computer processing of the data and to keep them for the period in which the process is open for administrative, evaluation and audit purposes? During this period, the data subject reserves the right to access and rectify them.", "consent_3": "Do you authorize the Training Entity to photocopy your identification document for filing in the Technical-Pedagogical Dossier?", "consent_4": "Do you authorize the data contained in this form to be used by the Training Entity for sending commercial and/or promotional content?", "consent_5": "Do you authorize the data contained in this form to be used by the Training Entity for sending institutional and informative communications?"}, "closingReason": {"label": "Closing Reason", "placeholder": "Select reason"}, "statusDate": {"label": "Status Date", "placeholder": "Select date"}, "closingStatus": {"label": "Closing Status", "placeholder": "Select status"}}