{"columns": {"type": "Type", "description": "Description", "startDate": "Start Date", "endDate": "End Date", "file": "File", "locality": "Locality", "postalCode": "Postal Code", "parish": "Parish", "municipality": "Municipality", "district": "District", "id": "ID", "status": "Status", "name": "Name", "email": "Email", "contact": "Contact", "nif": "NIF", "assignedTo": "Assigned to", "assignmentDate": "Assignment Date", "creationDate": "Creation Date", "createdAt": "Created At", "course": "Course", "assignedCampus": "Assigned Hub", "campus": "<PERSON><PERSON>", "educationLevel": "Education Level", "preferredSchedule": "Preferred Schedule", "message": "Message", "payment": "Payment", "state": "State", "scheduledDate": "Scheduled Date", "doneDate": "Completion Date", "doneBy": "Completed by", "enrollment": "Enrollment", "username": "Username", "profile": "Profile", "country": "Country", "clientId": "Client ID"}, "statusOptions": {"new": "New", "assigned": "Assigned", "inQualification": "In Qualification", "closedSuccess": "Closed with Success", "closedFail": "Closed without Success", "inValidation": "In Validation", "cancelled": "Cancelled"}, "profession": "Profession"}