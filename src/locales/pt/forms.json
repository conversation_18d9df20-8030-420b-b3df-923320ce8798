<<<<<<< HEAD
  {
    "subtab": {
      "details": "Detalhes",
      "rgpd": "RGPD",
      "relationships": "Relações",
      "entities": "Entidades",
      "attachments": "Anexos",
      "crmHistory": "CRM-Histórico",
      "enrollments": "Inscrições",
      "detailsEnrollment": "Detalhes matrícula",
      "payments": "Faturação",
      "basicInformation": "Informação Básica",
      "localization": "Localização",
      "atribution": "Atribuição",
      "course": "Curso",
      "message": "Mensagem",
      "interaction": "Interações",
      "invoicing": "Faturação",
      "groups": "Grupos",
      "protocols": "Protocolos",
      "basicInfo": "Informações básicas",
      "location": "Localização",
      "assignment": "Atribuição",
      "userInfo": "Informação do utilizador",
      "profileDataTitle": "Dados do perfil",
      "labelTextArea": "Descrição",
      "accordionTitle": "Permissões",
      "assignedHubs": "Polos atribuídos",
      "secondary": {
        "contract": "Contratualização Geral da Matrícula",
        "paymentEnrollment": "Pagamento Efetuado no ato da Inscrição"
      }
    },
    "placeholder": "Inserir",
    "id": {
      "label": "ID"
    },
    "entities": {
      "label": "Entidades",
      "placeholder": "Seleciona entidades...",
      "empty": "Não existem entidades associadas a este contacto.",
      "add": "Adicionar entidade",
      "type": {
        "label": "Tipo de entidade",
        "placeholder": "Seleciona tipo"
      }
    },
    "description": {
      "label": "Descrição",
      "placeholder": "Insere descrição"
    },
    "name": {
      "label": "Nome",
      "placeholder": "Insere nome"
    },
    "email": {
      "label": "Email",
      "placeholder": "Insere email"
    },
    "mobile": {
      "label": "Telemóvel",
      "placeholder": "Insere telemóvel"
    },
    "contact": {
      "label": "Contacto",
      "placeholder": "Insere contacto"
    },
    "phone": {
      "label": "Telefone",
      "placeholder": "Insere telefone"
    },
    "nif": {
      "label": "NIF",
      "placeholder": "Insere NIF"
    },
    "center": {
      "label": "Polo",
      "placeholder": "Seleciona polo..."
    },
    "relationships": {
      "empty": "Não existem relações associadas a este contacto.",
      "add": "Adicionar relação",
      "label": "Nome Contacto",
      "placeholder": "Pesquisar contacto...",
      "type": {
        "label": "Relação",
        "placeholder": "Seleciona relação"
      }
    },
    "observations": {
      "label": "Observações",
      "placeholder": "Insere observações..."
    },
    "attachments": {
      "empty": "Não existem ficheiros associados a este contacto.",
      "label": "Adicionar ficheiro",
      "subLabel": "Ficheiros importados",
      "modal": {
        "label": "Anexar ficheiro",
        "fileNamePlaceholder": "Insere nome",
        "filePlaceholder": "Ficheiro",
        "fileTypePlaceholder": "Seleciona tipo",
        "descriptionPlaceholder": "Insere descrição",
        "tagsPlaceholder": "Insere etiquetas",
        "confirmLabel": "Anexar",
        "cancelLabel": "Cancelar"
=======
{
  "subtab": {
    "details": "Detalhes",
    "rgpd": "RGPD",
    "relationships": "Relações",
    "entities": "Entidades",
    "attachments": "Anexos",
    "crmHistory": "CRM-Histórico",
    "enrollments": "Inscrições",
    "detailsEnrollment": "Detalhes matrícula",
    "payments": "Faturação",
    "basicInformation": "Informação Básica",
    "localization": "Localização",
    "atribution": "Atribuição",
    "course": "Curso",
    "message": "Mensagem",
    "interaction": "Interações",
    "invoicing": "Faturação",
    "groups": "Grupos",
    "protocols": "Protocolos",
    "basicInfo": "Informações básicas",
    "location": "Localização",
    "assignment": "Atribuição",
    "userInfo": "Informação do utilizador",
    "profileDataTitle": "Informação do perfil",
    "labelTextArea": "Nome do perfil",
    "accordionTitle": "Permissões e níveis de acesso",
    "assignedHubs": "Polos atribuídos",
    "secondary": {
      "contract": "Contratualização Geral da Matrícula",
      "paymentEnrollment": "Pagamento Efetuado no ato da Inscrição"
    }
  },
  "placeholder": "Inserir",
  "id": {
    "label": "ID"
  },
  "entities": {
    "label": "Entidades",
    "placeholder": "Seleciona entidades..."
  },
  "description": {
    "label": "Descrição",
    "placeholder": "Insere descrição"
  },
  "name": {
    "label": "Nome",
    "placeholder": "Insere nome"
  },
  "email": {
    "label": "Email",
    "placeholder": "Insere email"
  },
  "mobile": {
    "label": "Telemóvel",
    "placeholder": "Insere telemóvel"
  },
  "contact": {
    "label": "Contacto",
    "placeholder": "Insere contacto",
    "add": "Adicionar contacto",
    "edit": "Editar contacto"
  },
  "phone": {
    "label": "Telefone",
    "placeholder": "Insere telefone"
  },
  "nif": {
    "label": "NIF",
    "placeholder": "Insere NIF"
  },
  "center": {
    "label": "Polo",
    "placeholder": "Seleciona polo..."
  },
  "relationships": {
    "empty": "Não existem relações associadas a este contacto.",
    "add": "Adicionar relação",
    "label": "Nome Contacto",
    "placeholder": "Pesquisar contacto...",
    "type": {
      "label": "Relação",
      "placeholder": "Seleciona relação"
    }
  },
  "observations": {
    "label": "Observações",
    "placeholder": "Insere observações..."
  },
  "attachments": {
    "empty": "Não existem ficheiros associados a este contacto.",
    "label": "Adicionar ficheiro",
    "subLabel": "Ficheiros importados",
    "modal": {
      "label": "Anexar ficheiro",
      "fileNamePlaceholder": "Insere nome",
      "filePlaceholder": "Ficheiro",
      "fileTypePlaceholder": "Seleciona tipo",
      "descriptionPlaceholder": "Insere descrição",
      "tagsPlaceholder": "Insere etiquetas",
      "confirmLabel": "Anexar",
      "cancelLabel": "Cancelar"
    },
    "add": "Adicionar anexo",
    "attach": "Anexar",
    "noAttachments": "Sem anexos",
    "success": "Anexo adicionado com sucesso!",
    "fields": {
      "file": {
        "placeholder": "Nome ficheiro",
        "buttonLabel": "Ficheiro"
>>>>>>> 4826263 (feat(ui): connect opportunities to backend, list users in contact field)
      },
      "type": {
        "label": "Tipo",
        "placeholder": "Insere tipo",
        "options": {}
      },
      "labels": {
        "label": "Etiquetas"
      },
      "startDate": {
        "label": "Data Início",
        "placeholder": "Seleciona Data"
      },
      "endDate": {
        "label": "Data Fim",
        "placeholder": "Seleciona Data"
      }
<<<<<<< HEAD
    },
    "interaction": {
      "add": "Adicionar interação",
      "hubs": {
        "notAssigned": "Nenhum polo atribuído",
        "assigned": "Polo atribuído",
        "assign": "Atribuir polo"
      }
    },
    "contacts": {
      "entities": {
        "title": "Nova ficha de entidades"
      }
    },
    "assignmentDate": {
      "label": "Data de atribuição"
    },
    "creationDate": {
      "label": "Data de criação"
    },
    "assignedCampus": {
      "label": "Pólo atribuído"
    },
    "campus": {
      "label": "Pólo"
    },
    "preferredSchedule": {
      "label": "Horário Preferencial"
    },
    "message": {
      "label": "Mensagem"
    },
    "upload": {
      "label": "Carregar"
    },
    "fileName": {
      "label": "Nome do ficheiro"
    },
    "assignedHubs": {
      "section": "Polos atribuídos",
      "notAssigned": "Nenhum polo atribuído",
      "assigned": "Polo atribuído",
      "assign": "Atribuir polo"
    },
    "label": {
      "cancel": "Cancelar",
      "save": "Guardar",
      "edit": "Editar",
      "delete": "Eliminar"
    },
    "opportunityTypes": {
      "registration": "Registo"
    },
    "interactionTypes": {
      "call": "Chamada Telefónica",
      "mail": "Carta",
      "sms": "SMS"
    },
    "schedule": {
      "label": "Horário",
      "placeholder": "Seleciona horário",
      "options": {
        "morning": "Laboral Manhã",
        "afternoon": "Laboral Tarde",
        "night": "Pós-Laboral",
        "none": "N/A",
        "saturday": "Sábado"
      }
    },
    "enrollmentYear": {
      "label": "Ano de Matrícula",
      "placeholder": "Seleciona ano"
    },
    "enrollmentNumber": {
      "label": "Nº de Matrícula",
      "placeholder": "Insere número"
    },
    "baseEducation": {
      "label": "Formação base",
      "placeholder": "Seleciona formação base",
      "options": {
        "level2": "Nível 2",
        "level3": "Nível 3",
        "level2And3": "Níveis 2 e 3"
      }
    },
    "baseType9": {
      "label": "Tipo formação base (9º)",
      "placeholder": "Seleciona modalidade",
      "options": {
        "ufcd25": "UFCD 25 horas",
        "ufcd50": "UFCD 50 horas",
        "A": "Tipo A (nível 2)",
        "B": "Tipo B (nível 2)",
        "C": "Tipo C (nível 2)"
      }
    },
    "baseType12": {
      "label": "Tipo formação base (12º)",
      "placeholder": "Seleciona modalidade",
      "options": {
        "A": "Tipo A (nível 3)",
        "B": "Tipo B (nível 3)",
        "C": "Tipo C (nível 3)",
        "portaria": "Portaria 357/2007",
        "ufcd25": "UFCD 25 horas",
        "ufcd50": "UFCD 50 horas"
      }
    },
    "trainee": {
      "label": "Formando",
      "placeholder": "Seleciona formando"
    },
    "traineeType": {
      "label": "Tipo formando",
      "placeholder": "Selecione tipo",
      "options": {
        "customer": "Particular",
        "business": "Empresa"
      }
    },
    "socialSecurityNumber": {
      "label": "Nº Segurança Social",
      "placeholder": "Insere número"
    },
    "identificationType": {
      "label": "Tipo Doc. Identificação",
      "placeholder": "Seleciona documento"
    },
    "identificationValidity": {
      "label": "Validade Doc. Identificação",
      "placeholder": "MM/DD/YYYY"
    },
    "identificationNumber": {
      "label": "N° Doc. de Identificação",
      "placeholder": "Insere número"
    },
    "identificationFile": {
      "label": "Arquivo Doc. Identificação",
      "placeholder": "Insere arquivo"
    },
    "address": {
      "label": "Morada",
      "placeholder": "Insere a morada"
    },
    "postalCode": {
      "label": "Código Postal",
      "placeholder": "Insere código postal"
    },
    "locality": {
      "label": "Localidade",
      "placeholder": "Insere localidade"
    },
    "district": {
      "label": "Distrito",
      "placeholder": "Insere distrito"
    },
    "municipality": {
      "label": "Concelho",
      "placeholder": "Insere concelho"
    },
    "parish": {
      "label": "Freguesia",
      "placeholder": "Insere freguesia"
    },
    "country": {
      "label": "País",
      "placeholder": "Insere país"
    },
    "birthPlace": {
      "label": "Naturalidade",
      "placeholder": "Insere naturalidade"
    },
    "profession": {
      "label": "Profissão",
      "placeholder": "Insere profissão"
    },
    "employerEntity": {
      "label": "Entidade Empregadora",
      "placeholder": "Pesquisa entidade"
    },
    "contractType": {
      "label": "Tipo de Contrato",
      "placeholder": "Seleciona tipo"
    },
    "birthDate": {
      "label": "Data de Nascimento",
      "placeholder": "MM/DD/YYYY"
    },
    "gender": {
      "label": "Género",
      "placeholder": "Seleciona género"
    },
    "educationLevel": {
      "label": "Escolaridade",
      "placeholder": "Seleciona escolaridade"
    },
    "iban": {
      "label": "IBAN",
      "placeholder": "Insere IBAN"
    },
    "notes": {
      "label": "Notas",
      "placeholder": "Insere observações"
    },
    "website": {
      "label": "Site",
      "placeholder": "Insere url site..."
    },
    "type": {
      "label": "Tipo",
      "placeholder": "Seleciona tipo"
    },
    "state": {
      "label": "Estado",
      "placeholder": "Insere estado...",
      "options": {
        "success": "Sucesso",
        "insuccess": "Insucesso"
      }
    },
    "scheduledDate": {
      "label": "Data agendada"
    },
    "doneDate": {
      "label": "Data efetuada"
    },
    "doneBy": {
      "label": "Efetuada por"
    },
    "createdAt": {
      "label": "Data criação"
    },
    "text": {
      "label": "Texto",
      "placeholder": "Insere texto..."
    },
    "opportunityState": {
      "label": "Estado da opportunidade",
      "placeholder": "Seleciona novo estado"
    },
    "closingResult": {
      "label": "Resultado Fecho",
      "placeholder": "seleciona resultado"
    },
    "closingDate": {
      "label": "Data Fecho"
    },
    "closingDescription": {
      "label": "Descrição Fecho"
    },
    "entity": {
      "label": "Entidade",
      "placeholder": "Pesquisa entidade..."
    },
    "assignedTo": {
      "label": "Atribuido a"
    },
    "stateDate": {
      "label": "Data Estado"
    },
    "closeDate": {
      "label": "Data Fecho"
    },
    "resultState": {
      "label": "Resultado Fecho"
    },
    "resultReason": {
      "label": "Razão Fecho",
      "placeholder": "Insere razão..."
    },
    "resultDescription": {
      "label": "Descrição fecho",
      "placeholder": "Insere Descrição"
    },
    "createdBy": {
      "label": "Criado por"
    },
    "sourceLead": {
      "label": "Lead origem"
    },
    "course": {
      "label": "Curso",
      "placeholder": "Insere curso..."
    },
    "modality": {
      "label": "Modalidade formação",
      "placeholder": "Seleciona modalidade",
      "options": {
        "inPerson": "Presencial",
        "bLearning": "B-Learning",
        "eLearning": "E-Learning"
      }
    },
    "cliente_formacao": {
      "label": "Cliente formação"
    },
    "username": {
      "label": "Username",
      "placeholder": "Insere o username..."
    },
    "profile": {
      "label": "Perfil",
      "admin": "Administrador",
      "supervisor": "Supervisor Comercial",
      "placeholder": "Seleciona perfil"
    },
    "status": {
      "label": "Estado",
      "active": "Ativo",
      "inactive": "Inativo"
    },
    "language": {
      "label": "Idioma",
      "pt": "Português",
      "en": "Inglês",
      "placeholder": "Seleciona língua"
    },
    "title": "Fechar lead sem sucesso ({id})",
    "inputLabel": "Indica a razão do fecho e descreve o motivo",
    "selectReason": "Seleciona a razão",
    "textFieldPlaceholder": "Insere uma descrição",
    "cliente_faturacao": {
      "label": "Cliente faturação"
    },
    "entidade_empregadora": {
      "label": "Entidade empregadora"
    },
    "parceria": {
      "label": "Parceria"
    },
    "capital_social": {
      "label": "Capital Social",
      "placeholder": "Inserir capital social..."
    },
    "autorizar_dados_pessoais": {
      "label": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei n° 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?"
    },
    "autorizar_processamento_dados": {
      "label": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo periodo em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este periodo reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos."
    },
    "autorizar_fotocopia": {
      "label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"
    },
    "autorizar_conteudos_promocionais": {
      "label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"
    },
    "autorizar_comunicacoes_institucionais": {
      "label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"
    },
    "praticas": {
      "label": "Práticas"
    },
    "estagios": {
      "label": "Estágios"
    },
    "maintenanceContract": {
      "label": "Contrato de Manutenção"
    },
    "protocolTypes": {
      "label": "Tipos de protocolo",
      "options": {
        "Práticas": "Práticas",
        "Estágios": "Estágios"
      }
    },
    "addressesType": {
      "label": "Tipo",
      "placeholder": "Insere tipo",
      "options": {}
    },
    "addresses": {
      "label": "Moradas",
      "add": "Adicionar Morada",
      "noAddresses": "Sem endereços",
      "success": "Endereço adicionado com sucesso!"
    },
    "courseValue": {
      "label": "Valor do curso",
      "placeholder": "Insere valor..."
    },
    "monthlyCount": {
      "label": "Número mensalidades",
      "placeholder": "Insere número mensalidades..."
    },
    "hasInsurance": {
      "label": "Tem seguro próprio",
      "placeholder": "Seleciona opção",
      "options": {
        "yes": "Sim",
        "no": "Não"
      }
    },
    "insuranceValue": {
      "label": "Valor do seguro",
      "placeholder": "Oferta do seguro"
    },
    "enrollmentValue": {
      "label": "Valor da inscrição",
      "placeholder": "Insere valor inscrição"
    },
    "differentInvoiceContact": {
      "label": "Dados de faturação diferentes dos dados do formando"
    },
    "recurringPaymentTypes": {
      "label": "Forma de pagamento recorrente",
      "placeholder": "Seleciona modalidade",
      "options": {
        "transfer": "Transferência",
        "reference": "Referência de multibanco",
        "presential": "Pagamento Presencial"
      }
    },
    "paymentTypes": {
      "label": "Forma de pagamento",
      "options": {
        "transfer": "Transferência",
        "card": "Multibanco",
        "cash": "Numerário",
        "check": "Cheque",
        "reference": "Referência de multibanco"
      }
    },
    "monthlyPaymentValue": {
      "label": "Valor pago de mensalidade",
      "placeholder": "Insere valor..."
    },
    "totalPaid": {
      "label": "Total pago no ato de inscrição",
      "placeholder": "Insere total..."
    },
    "paymentType": {
      "label": "Tipo de pagamento",
      "placeholder": "Seleciona opções"
    },
    "checkNumber": {
      "label": "Cheque número",
      "placeholder": "Insere número..."
    },
    "bank": {
      "label": "Banco",
      "placeholder": "Insere banco..."
    },
    "agency": {
      "label": "Agência",
      "placeholder": "Insere agência..."
    },
    "rgpd": {
      "consent_1": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei nº 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?",
      "consent_2": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo período em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este período reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos.",
      "consent_3": "Autoriza a Entidade Formadora, a fotocopiar o seu documento de identificação para fins de arquivo em Dossier Técnico-Pedagógico?",
      "consent_4": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?",
      "consent_5": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"
=======
    }
  },
  "interaction": {
    "add": "Adicionar interação",
    "hubs": {
      "notAssigned": "Nenhum polo atribuído",
      "assigned": "Polo atribuído",
      "assign": "Atribuir polo"
>>>>>>> 4826263 (feat(ui): connect opportunities to backend, list users in contact field)
    }
  },
  "contacts": {
    "entities": {
      "title": "Nova ficha de entidades"
    }
  },

  "assignmentDate": {
    "label": "Data de atribuição"
  },
  "creationDate": {
    "label": "Data de criação"
  },
  "assignedCampus": {
    "label": "Pólo atribuído"
  },
  "campus": {
    "label": "Pólo"
  },
  "preferredSchedule": {
    "label": "Horário Preferencial"
  },
  "message": {
    "label": "Mensagem"
  },
  "upload": {
    "label": "Carregar"
  },
  "fileName": {
    "label": "Nome do ficheiro"
  },
  "assignedHubs": {
    "section": "Polos atribuídos",
    "notAssigned": "Nenhum polo atribuído",
    "assigned": "Polo atribuído",
    "assign": "Atribuir polo"
  },
  "label": {
    "cancel": "Cancelar",
    "save": "Guardar",
    "edit": "Editar",
    "delete": "Eliminar"
  },
  "opportunityTypes": {
    "registration": "Registo"
  },
  "interactionTypes": {
    "call": "Chamada Telefónica",
    "mail": "Carta",
    "sms": "SMS"
  },
  "schedule": {
    "label": "Horário",
    "placeholder": "Seleciona horário",
    "options": {
      "morning": "Laboral Manhã",
      "afternoon": "Laboral Tarde",
      "night": "Pós-Laboral",
      "none": "N/A",
      "saturday": "Sábado"
    }
  },
  "baseEducation": {
    "label": "Formação base",
    "placeholder": "Seleciona formação base",
    "options": {
      "level2": "Nível 2",
      "level3": "Nível 3",
      "level2And3": "Níveis 2 e 3"
    }
  },
  "baseType9": {
    "label": "Tipo formação base (9º)",
    "placeholder": "Seleciona modalidade",
    "options": {
      "ufcd25": "UFCD 25 horas",
      "ufcd50": "UFCD 50 horas",
      "A": "Tipo A (nível 2)",
      "B": "Tipo B (nível 2)",
      "C": "Tipo C (nível 2)"
    }
  },
  "baseType12": {
    "label": "Tipo formação base (12º)",
    "placeholder": "Seleciona modalidade",
    "options": {
      "A": "Tipo A (nível 3)",
      "B": "Tipo B (nível 3)",
      "C": "Tipo C (nível 3)",
      "portaria": "Portaria 357/2007",
      "ufcd25": "UFCD 25 horas",
      "ufcd50": "UFCD 50 horas"
    }
  },
  "trainee": {
    "label": "Formando",
    "placeholder": "Seleciona formando"
  },
  "traineeType": {
    "label": "Tipo formando",
    "placeholder": "Selecione tipo",
    "options": {
      "customer": "Particular",
      "business": "Empresa"
    }
  },
  "socialSecurityNumber": {
    "label": "Nº Segurança Social",
    "placeholder": "Insere número"
  },
  "identificationType": {
    "label": "Tipo Doc. Identificação",
    "placeholder": "Seleciona documento"
  },
  "identificationValidity": {
    "label": "Validade Doc. Identificação",
    "placeholder": "MM/DD/YYYY"
  },
  "identificationNumber": {
    "label": "N° Doc. de Identificação",
    "placeholder": "Insere número"
  },
  "identificationFile": {
    "label": "Arquivo Doc. Identificação",
    "placeholder": "Insere arquivo"
  },
  "address": {
    "label": "Morada",
    "placeholder": "Insere a morada"
  },
  "postalCode": {
    "label": "Código Postal",
    "placeholder": "Insere código postal"
  },
  "locality": {
    "label": "Localidade",
    "placeholder": "Insere localidade"
  },
  "district": {
    "label": "Distrito",
    "placeholder": "Insere distrito"
  },
  "municipality": {
    "label": "Concelho",
    "placeholder": "Insere concelho"
  },
  "parish": {
    "label": "Freguesia",
    "placeholder": "Insere freguesia"
  },
  "country": {
    "label": "País",
    "placeholder": "Insere país"
  },
  "birthPlace": {
    "label": "Naturalidade",
    "placeholder": "Insere naturalidade"
  },
  "profession": {
    "label": "Profissão",
    "placeholder": "Insere profissão"
  },
  "employerEntity": {
    "label": "Entidade Empregadora",
    "placeholder": "Pesquisa entidade"
  },
  "contractType": {
    "label": "Tipo de Contrato",
    "placeholder": "Seleciona tipo"
  },
  "birthDate": {
    "label": "Data de Nascimento",
    "placeholder": "MM/DD/YYYY"
  },
  "gender": {
    "label": "Género",
    "placeholder": "Seleciona género"
  },
  "educationLevel": {
    "label": "Escolaridade",
    "placeholder": "Seleciona escolaridade"
  },
  "iban": {
    "label": "IBAN",
    "placeholder": "Insere IBAN"
  },
  "notes": {
    "label": "Notas",
    "placeholder": "Insere observações"
  },
  "website": {
    "label": "Site",
    "placeholder": "Insere url site..."
  },
  "type": {
    "label": "Tipo",
    "placeholder": "Seleciona tipo"
  },
  "state": {
    "label": "Estado",
    "placeholder": "Insere estado...",
    "options": {
      "success": "Sucesso",
      "insuccess": "Insucesso"
    }
  },
  "scheduledDate": {
    "label": "Data agendada"
  },
  "doneDate": {
    "label": "Data efetuada"
  },
  "doneBy": {
    "label": "Efetuada por"
  },
  "createdAt": {
    "label": "Data criação"
  },
  "text": {
    "label": "Texto",
    "placeholder": "Insere texto..."
  },
  "opportunityState": {
    "label": "Estado da opportunidade",
    "placeholder": "Seleciona novo estado"
  },
  "closingResult": {
    "label": "Resultado Fecho",
    "placeholder": "seleciona resultado"
  },
  "closingDate": {
    "label": "Data Fecho"
  },
  "closingDescription": {
    "label": "Descrição Fecho"
  },
  "entity": {
    "label": "Entidade",
    "placeholder": "Pesquisa entidade..."
  },
  "assignedTo": {
    "label": "Atribuido a",
    "placeholder": "Pesquisa utilizador..."
  },
  "stateDate": {
    "label": "Data Estado"
  },
  "closeDate": {
    "label": "Data Fecho"
  },
  "resultState": {
    "label": "Resultado Fecho"
  },
  "resultReason": {
    "label": "Razão Fecho",
    "placeholder": "Insere razão..."
  },
  "resultDescription": {
    "label": "Descrição fecho",
    "placeholder": "Insere Descrição"
  },
  "createdBy": {
    "label": "Criado por"
  },
  "sourceLead": {
    "label": "Lead origem"
  },
  "course": {
    "label": "Curso",
    "placeholder": "Insere curso..."
  },
  "modality": {
    "label": "Modalidade formação",
    "placeholder": "Seleciona modalidade",
    "options": {
      "inPerson": "Presencial",
      "bLearning": "B-Learning",
      "eLearning": "E-Learning"
    }
  },
  "cliente_formacao": {
    "label": "Cliente formação"
  },
  "username": {
    "label": "Username",
    "placeholder": "Insere o username..."
  },
  "profile": {
    "label": "Perfil",
    "admin": "Administrador",
    "supervisor": "Supervisor Comercial",
    "placeholder": "Seleciona perfil"
  },
  "status": {
    "label": "Estado",
    "placeholder": "Seleciona estado",
    "active": "Ativo",
    "inactive": "Inativo"
  },
  "language": {
    "label": "Idioma",
    "pt": "Português",
    "en": "Inglês",
    "placeholder": "Seleciona língua"
  },
  "title": "Fechar lead sem sucesso ({id})",
  "inputLabel": "Indica a razão do fecho e descreve o motivo",
  "selectReason": "Seleciona a razão",
  "textFieldPlaceholder": "Insere uma descrição",
  "cliente_faturacao": {
    "label": "Cliente faturação"
  },
  "entidade_empregadora": {
    "label": "Entidade empregadora"
  },
  "parceria": {
    "label": "Parceria"
  },
  "capital_social": {
    "label": "Capital Social",
    "placeholder": "Inserir capital social..."
  },
  "autorizar_dados_pessoais": {
    "label": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei n° 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?"
  },
  "autorizar_processamento_dados": {
    "label": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo periodo em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este periodo reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos."
  },
  "autorizar_fotocopia": {
    "label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"
  },
  "autorizar_conteudos_promocionais": {
    "label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"
  },
  "autorizar_comunicacoes_institucionais": {
    "label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"
  },
  "praticas": {
    "label": "Práticas"
  },
  "estagios": {
    "label": "Estágios"
  },
  "maintenanceContract": {
    "label": "Contrato de Manutenção"
  },
  "protocolTypes": {
    "label": "Tipos de protocolo",
    "options": {
      "Práticas": "Práticas",
      "Estágios": "Estágios"
    }
  },
  "addressesType": {
    "label": "Tipo",
    "placeholder": "Insere tipo",
    "options": {}
  },
  "addresses": {
    "label": "Moradas",
    "add": "Adicionar Morada",
    "noAddresses": "Sem endereços",
    "success": "Endereço adicionado com sucesso!"
  },
  "courseValue": {
    "label": "Valor do curso",
    "placeholder": "Insere valor..."
  },
  "monthlyCount": {
    "label": "Número mensalidades",
    "placeholder": "Insere número mensalidades..."
  },
  "hasInsurance": {
    "label": "Tem seguro próprio",
    "placeholder": "Seleciona opção",
    "options": {
      "yes": "Sim",
      "no": "Não"
    }
  },
  "insuranceValue": {
    "label": "Valor do seguro",
    "placeholder": "Oferta do seguro"
  },
  "enrollmentValue": {
    "label": "Valor da inscrição",
    "placeholder": "Insere valor inscrição"
  },
  "differentInvoiceContact": {
    "label": "Dados de faturação diferentes dos dados do formando"
  },
  "recurringPaymentTypes": {
    "label": "Forma de pagamento recorrente",
    "placeholder": "Seleciona modalidade",
    "options": {
      "transfer": "Transferência",
      "reference": "Referência de multibanco",
      "presential": "Pagamento Presencial"
    }
  },
  "paymentTypes": {
    "label": "Forma de pagamento",
    "options": {
      "transfer": "Transferência",
      "card": "Multibanco",
      "cash": "Numerário",
      "check": "Cheque",
      "reference": "Referência de multibanco"
    }
  },
  "monthlyPaymentValue": {
    "label": "Valor pago de mensalidade",
    "placeholder": "Insere valor..."
  },
  "totalPaid": {
    "label": "Total pago no ato de inscrição",
    "placeholder": "Insere total..."
  },
  "paymentType": {
    "label": "Tipo de pagamento",
    "placeholder": "Seleciona opções"
  },
  "checkNumber": {
    "label": "Cheque número",
    "placeholder": "Insere número..."
  },
  "bank": {
    "label": "Banco",
    "placeholder": "Insere banco..."
  },
  "agency": {
    "label": "Agência",
    "placeholder": "Insere agência..."
  },
  "rgpd": {
    "consent_1": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei nº 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?",
    "consent_2": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo período em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este período reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos.",
    "consent_3": "Autoriza a Entidade Formadora, a fotocopiar o seu documento de identificação para fins de arquivo em Dossier Técnico-Pedagógico?",
    "consent_4": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?",
    "consent_5": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"
  },
  "closingReason": {
    "label": "Razão Fecho",
    "placeholder": "seleciona razão"
  },
  "closingStatus": {
    "label": "Estado Fecho",
    "placeholder": "seleciona estado"
  },
  "statusDate": {
    "label": "Data do estado",
    "placeholder": "Seleciona data"
  }
}
