{"columns": {"type": "Tipo", "description": "Descrição", "startDate": "Data de início", "endDate": "Data de fim", "file": "<PERSON><PERSON><PERSON>", "locality": "Localidade", "postalCode": "Código Postal", "parish": "Freguesia", "municipality": "<PERSON><PERSON><PERSON>", "district": "Distrito", "id": "ID", "status": "Estado", "name": "Nome", "email": "Email", "contact": "Contacto", "nif": "NIF", "assignedTo": "Atribuído a", "assignmentDate": "Data de atribuição", "creationDate": "Data de criação", "createdAt": "Data criação", "course": "Curso", "assignedCampus": "<PERSON><PERSON><PERSON>", "campus": "<PERSON><PERSON><PERSON>", "educationLevel": "Nível de Escolaridade", "preferredSchedule": "<PERSON><PERSON><PERSON><PERSON>", "message": "Mensagem", "payment": "Pagamento", "state": "Estado", "scheduledDate": "Data agendada", "doneDate": "Data efetuada", "doneBy": "Efetuada por", "enrollment": "<PERSON><PERSON><PERSON><PERSON>", "username": "Utilizador", "profile": "Perfil", "country": "<PERSON><PERSON>", "clientId": "ID cliente", "ref": "Ref.", "center": "Polo", "modality": "Modal. formação", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "expectedStart": "Prev. in<PERSON><PERSON>", "daysSinceOldestEnrollment": "Dias desde matrícula mais antiga", "numTrainees": "Nº formandos", "fundraisingDeadline": "Data limite angariação", "createdBy": "<PERSON><PERSON><PERSON> por", "category": "Categoria", "madeBy": "<PERSON><PERSON> por"}, "statusOptions": {"new": "New", "assigned": "Assigned", "inQualification": "In Qualification", "closedSuccess": "Closed with Success", "closedFail": "Closed without Success", "inValidation": "In Validation", "cancelled": "Cancelled"}, "profession": "Profissão", "categories": {"OPPORTUNITY": "Oportunidade", "INTERACTION_OPPORTUNITY": "Interação oportunidade", "INTERACTION_SYSTEM": "Interação sistema", "INTERACTION_EXTERNAL": "Interação externa"}}