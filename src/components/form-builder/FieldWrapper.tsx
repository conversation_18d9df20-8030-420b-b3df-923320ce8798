'use client';

import { Box, Grid, Skeleton } from '@mui/material';

import { FieldConfig, useFormBuilderContext } from './types/formBuilder';
import { CustomLabel } from './FormField';
import { PropsWithChildren } from 'react';

export type FieldWrapperProps = Pick<
  FieldConfig,
  'label' | 'required' | 'fullWidth' | 'loading' | 'colSpan'
>;

const FieldWrapper = ({
  label,
  required,
  loading,
  fullWidth,
  colSpan,
  children,
}: PropsWithChildren<FieldWrapperProps>) => {
  const { columns } = useFormBuilderContext();

  // Distribuição automática de colunas
  const gridSize = fullWidth ? 12 : colSpan ?? Math.round(12 / columns);

  return (
    <Grid size={gridSize}>
      {loading ? (
        <>
          <CustomLabel label={label || ''} required={required} />
          <Skeleton
            variant="rectangular"
            width="100%"
            height={'40px'}
            sx={{
              borderRadius: '8px',
            }}
          />
          {/* helper text height */}
          <Box sx={{ minHeight: '24px' }} />
        </>
      ) : (
        children
      )}
    </Grid>
  );
};

export default FieldWrapper;