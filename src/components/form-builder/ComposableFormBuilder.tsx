'use client';

import { FC, ReactNode, useMemo } from 'react';
import { Grid, Typography } from '@mui/material';
import {
  FieldConfig,
  FormBuilderContext,
  FormBuilderContextType,
  FormBuilderProps,
} from './types/formBuilder';
import { FormProvider, UseFormReturn } from 'react-hook-form';
import FieldWrapper from './FieldWrapper';
import { useTranslations } from 'next-intl';
import FormField from './FormField';

type ComposableFormBuilderProps = Omit<FormBuilderProps, 'fields'> & {
  methods: UseFormReturn<any>;
  children: ReactNode;
};

export type ComposableFormFieldProps = Omit<FieldConfig, 'subFields'> & { children?: ReactNode };

const RenderRecursiveFields = ({ fields = [] }: { fields?: FieldConfig[] }) =>
  fields.map(({ subFields, ...field }) => (
    <ComposableForm.Field key={field.name} {...field}>
      <RenderRecursiveFields fields={subFields} />
    </ComposableForm.Field>
  ));

export const Layout: FC<Omit<ComposableFormBuilderProps, 'methods'>> = ({
  label,
  columns = 1,
  children,
}) => {
  const value = useMemo<FormBuilderContextType>(() => ({ columns }), [columns]);
  const t = useTranslations();

  return (
    <FormBuilderContext.Provider value={value}>
      <Grid width={'100%'} container spacing={3} columnSpacing={3} rowSpacing={1}>
        {label && (
          <Typography
            variant="alertTitle"
            component="label"
            sx={{ width: '100%', display: 'block' }}
          >
            {t(label)}
          </Typography>
        )}

        {children}
      </Grid>
    </FormBuilderContext.Provider>
  );
};

const Provider: FC<ComposableFormBuilderProps> = ({ methods, children, ...rest }) => (
  <FormProvider {...methods}>
    <Layout {...rest}>{children}</Layout>
  </FormProvider>
);

const Field: FC<ComposableFormFieldProps> = ({ children, ...field }) => (
  <FieldWrapper
    label={field.label}
    required={field.required}
    colSpan={field.colSpan}
    fullWidth={field.fullWidth}
    loading={field.loading}
  >
    <FormField field={field}>{children}</FormField>
  </FieldWrapper>
);

export const ComposableForm = {
  Provider,
  Field,
  Internal: {
    Layout,
    RenderRecursiveFields,
  },
};
