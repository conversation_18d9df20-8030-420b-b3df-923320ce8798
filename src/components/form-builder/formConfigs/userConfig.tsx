import { FieldConfig } from '../types/formBuilder';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import { NameField } from '../presets/nameField';
import { EmailField } from '../presets/emailField';
import { ComposableForm } from '../ComposableFormBuilder';
import { PhoneField } from '../presets/phoneField';
import { NifField } from '../presets/nifField';
import React from 'react';
import { useTranslations } from 'next-intl';
import { Translation } from '@/types/layout';

type RgpdFieldsArgs = {
  readonly?: boolean;
  t: Translation;
};
type ContactUserDetailsFieldsProps = {
  readonly?: boolean;
  selectOptions?: Record<string, any>;
};

export const rgpdFields = ({ readonly, t }: RgpdFieldsArgs): FieldConfig[] => [
  {
    name: 'rgpd.consent_1',
    label: t('rgpd.consent_1'),
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_2',
    label: t('rgpd.consent_2'),
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_3',
    label: t('rgpd.consent_3'),
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_4',
    label: t('rgpd.consent_4'),
    type: 'checkbox',
    readonly,
  },
  {
    name: 'rgpd.consent_5',
    label: t('rgpd.consent_5'),
    type: 'checkbox',
    readonly,
  },
];

export const ContactUserDetailsFields: React.FC<ContactUserDetailsFieldsProps> = ({
  readonly = false,
  selectOptions = {},
}) => {
  const t = useTranslations('forms');
  return (
    <ComposableForm.Internal.Layout columns={2}>
      <NameField required readonly={readonly} />
      <EmailField readonly={readonly} />
      <ComposableForm.Field
        name="mobile"
        label={t('mobile.label')}
        placeholder={t('mobile.placeholder')}
        type="phonenumber"
        readonly={readonly}
      />
      <PhoneField readonly={readonly} />
      <NifField readonly={readonly} />
      <ComposableForm.Field
        name="socialSecurityNumber"
        label={t('socialSecurityNumber.label')}
        placeholder={t('socialSecurityNumber.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="identificationType"
        label={t('identificationType.label')}
        placeholder={t('identificationType.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="identificationValidity"
        label={t('identificationValidity.label')}
        placeholder={t('identificationValidity.placeholder')}
        type="date"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="identificationNumber"
        label={t('identificationNumber.label')}
        placeholder={t('identificationNumber.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="identificationFile"
        label={t('identificationFile.label')}
        placeholder={t('identificationFile.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="address"
        label={t('address.label')}
        placeholder={t('address.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="postalCode"
        label={t('postalCode.label')}
        placeholder={t('postalCode.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="locality"
        label={t('locality.label')}
        placeholder={t('locality.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="district"
        label={t('district.label')}
        placeholder={t('district.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.district}
      />
      <ComposableForm.Field
        name="municipality"
        label={t('municipality.label')}
        placeholder={t('municipality.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.municipality}
      />
      <ComposableForm.Field
        name="parish"
        label={t('parish.label')}
        placeholder={t('parish.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.parish}
      />
      <ComposableForm.Field
        name="country"
        label={t('country.label')}
        placeholder={t('country.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.country}
      />
      <ComposableForm.Field
        name="birthPlace"
        label={t('birthPlace.label')}
        placeholder={t('birthPlace.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="profession"
        label={t('profession.label')}
        placeholder={t('profession.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="employerEntity"
        type="select"
        options={selectOptions?.employer ?? []}
        resultsOnlyWithSearch={true}
        readonly={readonly}
        placeholder={t('employerEntity.placeholder')}
        label={t('employerEntity.label')}
        actionPrimary={() => console.log('Edit employer entity')}
        actionPrimaryIcon={EditIcon}
        actionPrimaryLabel={'employerEntity.edit'} //aria label only
        actionPrimaryDisabled={true}
        actionSecondary={() => console.log('Add employer entity')}
        actionSecondaryIcon={AddCircleIcon}
        actionSecondaryLabel={'employerEntity.add'} // aria label only
      />
      <ComposableForm.Field
        name="contractType"
        label={t('contractType.label')}
        placeholder={t('contractType.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.contractType}
      />
      <ComposableForm.Field
        name="birthDate"
        label={t('birthDate.label')}
        placeholder={t('birthDate.placeholder')}
        type="date"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="gender"
        label={t('gender.label')}
        placeholder={t('gender.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.gender}
      />
      <ComposableForm.Field
        name="educationLevel"
        label={t('educationLevel.label')}
        placeholder={t('educationLevel.placeholder')}
        type="select"
        readonly={readonly}
        options={selectOptions?.educationLevel}
      />
      <ComposableForm.Field
        name="iban"
        label={t('iban.label')}
        placeholder={t('iban.placeholder')}
        type="text"
        readonly={readonly}
      />
      <ComposableForm.Field
        name="notes"
        label={t('notes.label')}
        placeholder={t('notes.placeholder')}
        type="text"
        colSpan={12}
        readonly={readonly}
        multiline={true}
      />
    </ComposableForm.Internal.Layout>
  );
};
type SequenceFieldsProps = {
  formOptions: any;
  selectOptions: any;
  readonly: boolean;
  setFormOptions: React.Dispatch<React.SetStateAction<any>>;
  fieldKey: string;
};
export const SequenceFields: React.FC<SequenceFieldsProps> = ({
  formOptions,
  selectOptions,
  readonly,
  setFormOptions,
  fieldKey,
}) => {
  const t = useTranslations('forms');
  return (
    <ComposableForm.Internal.Layout columns={2}>
      <ComposableForm.Field colSpan={12} name={fieldKey} type="multiFieldLine">
        {formOptions[`${fieldKey}Types`].map((key: string) => (
          <React.Fragment key={key}>
            <ComposableForm.Field
              key={key}
              name={`${fieldKey}.${key}.user`}
              label={t(`${fieldKey}.label`)}
              placeholder={t(`${fieldKey}.placeholder`)}
              options={selectOptions?.users ?? []}
              type="select"
              resultsOnlyWithSearch={true}
              colSpan={6}
              readonly={readonly}
            />
            <ComposableForm.Field
              key={`${key}-type`}
              name={`${fieldKey}.${key}.relationshipType`}
              label={t(`${fieldKey}.type.label`)}
              placeholder={t(`${fieldKey}.type.placeholder`)}
              type="select"
              options={selectOptions?.[`${fieldKey}Types`] ?? []}
              colSpan={6}
              readonly={readonly}
              actionPrimary={() =>
                setFormOptions((prev: any) => ({
                  ...prev,
                  [`${fieldKey}Types`]: prev[`${fieldKey}Types`].filter(
                    (type: string) => type !== key
                  ),
                }))
              }
              actionPrimaryIcon={DeleteIcon}
              actionPrimaryLabel={`forms.${fieldKey}.delete`}
            />
          </React.Fragment>
        ))}
      </ComposableForm.Field>
      <ComposableForm.Field
        name={`${fieldKey}.addAction`}
        actionLabel={t(`${fieldKey}.add`)}
        type="iconButtonText"
        colSpan={12}
        disabled={false}
        action={() =>
          setFormOptions((prev: any) => ({
            ...prev,
            [`${fieldKey}Types`]: [...prev[`${fieldKey}Types`], `new-${Date.now()}`],
          }))
        }
        icon={<AddIcon />}
        readonly={readonly}
      />
    </ComposableForm.Internal.Layout>
  );
};
type AttachmentsFieldsProps = {
  readonly: boolean;
  formOptions: any;
  setFormOptions: React.Dispatch<React.SetStateAction<any>>;
  selectOptions: any;
  methods: any;
};

export const AttachmentsFields: React.FC<AttachmentsFieldsProps> = ({
  readonly,
  formOptions,
  setFormOptions,
  selectOptions,
  methods,
}) => {
  const t = useTranslations('forms');
  return (
    <>
      <ComposableForm.Field name="attachments.empty1" type="empty" colSpan={3} />
      <ComposableForm.Field
        name="attachments"
        label={t('attachments.label')}
        type="fileUploadModal"
        confirmIcon={<AttachFileIcon />}
        confirmLabel={t('attachments.modal.confirmLabel')}
        cancelLabel={t('attachments.modal.cancelLabel')}
        readonly={readonly}
        files={formOptions?.files ?? []}
        modalLabel={t('attachments.modal.label')}
        createAction={() => {
          const id = `new-${Date.now()}`;
          setFormOptions((prev: any) => ({
            ...prev,
            files: [...(prev.files ?? []), { id, published: false }],
          }));
          return id;
        }}
        cancelAction={() => {
          let id;
          setFormOptions((prev: any) => {
            const unpublished = prev.files?.find((file: any) => !file.published);
            if (unpublished) {
              id = unpublished.id;
            }
            return {
              ...prev,
              files: (prev.files ?? []).filter((file: any) => file.published),
            };
          });
          // we could use usefieldarray, but objects are easier to keep track
          methods.unregister(`attachments.${id}`);
        }}
        submitAction={({ id }: { id: string }) => {
          setFormOptions((prev: any) => ({
            ...prev,
            files: (prev.files ?? []).map((file: any) =>
              file.id === id ? { ...file, published: true } : file
            ),
          }));
        }}
        colSpan={6}
        subLabel={t('attachments.subLabel')}
        options={selectOptions?.fileTypes ?? []}
        modalNode={(id: string) => (
          <ComposableForm.Internal.RenderRecursiveFields
            fields={[
              {
                name: `${id}.fileName`,
                type: 'text',
                placeholder: t('attachments.modal.fileNamePlaceholder'),
                readonly,
                colSpan: 9,
              },
              {
                name: `${id}.file`,
                type: 'fileUpload',
                placeholder: t('attachments.modal.filePlaceholder'),
                readonly,
                colSpan: 3,
              },
              {
                name: `${id}.fileType`,
                type: 'select',
                options: selectOptions?.fileTypes ?? [],
                placeholder: t('attachments.modal.fileTypePlaceholder'),
                readonly,
                colSpan: 12,
              },
              {
                name: `${id}.description`,
                type: 'text',
                placeholder: t('attachments.modal.descriptionPlaceholder'),
                multiline: true,
                colSpan: 12,
                readonly,
              },
              {
                name: `${id}.tags`,
                type: 'text',
                placeholder: t('attachments.modal.tagsPlaceholder'),
                colSpan: 12,
                readonly,
              },
            ]}
          />
        )}
      >
        {formOptions?.files
          ?.filter((file: { published: boolean }) => file.published)
          ?.map(({ id }: { id: string }) => (
            <ComposableForm.Field
              key={id}
              name={`attachments.${id}.fileGroup`}
              id={id}
              parent={'attachments'}
              label={t('attachments.type')}
              type="fileGroup"
              deleteAction={() => {
                setFormOptions((prev: any) => ({
                  ...prev,
                  files: (prev.files ?? []).filter((file: any) => file.id !== id),
                }));
                methods.unregister(`attachments.${id}`);
              }}
              downloadAction={() => {
                // No operation for now
              }}
            />
          ))}
      </ComposableForm.Field>
      <ComposableForm.Field name="attachments.empty2" type="empty" colSpan={3} />
    </>
  );
};
