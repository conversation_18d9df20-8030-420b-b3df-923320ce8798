'use client';

import { FC } from 'react';
import { FormBuilderProps } from './types/formBuilder';
import { UseFormReturn } from 'react-hook-form';
import { ComposableForm } from './ComposableFormBuilder';

/**
 * @deprecated use ComposableForm.Provider
 */
const FormBuilder: FC<FormBuilderProps & { methods: UseFormReturn<any> }> = ({
  methods,
  columns,
  fields,
}) => {
  return (
    <ComposableForm.Provider methods={methods} columns={columns}>
      <ComposableForm.Internal.RenderRecursiveFields fields={fields} />
    </ComposableForm.Provider>
  );
};

export default FormBuilder;
