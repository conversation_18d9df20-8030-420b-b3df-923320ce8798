'use client';

import { Translation } from '@/types/layout';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';
import { useTranslations } from 'next-intl';

const fieldConfig = (t: Translation): FieldConfig => ({
  name: 'description',
  label: t('description.label'),
  placeholder: t('description.placeholder'),
  type: 'text',
});

export const DescriptionField = (props: Partial<ComposableFormFieldProps>) => {
  const t = useTranslations('forms');
  return <ComposableForm.Field {...fieldConfig(t)} {...props} />;
};
