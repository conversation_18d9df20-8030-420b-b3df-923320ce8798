'use client';

import { Translation } from '@/types/layout';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';
import { useTranslations } from 'next-intl';

const fieldConfig = (t: Translation): FieldConfig => ({
  name: 'email',
  label: t('forms.email.label'),
  placeholder: t('forms.email.placeholder'),
  type: 'text',
});

export const EmailField = (props: Partial<ComposableFormFieldProps>) => {
  const t = useTranslations();
  return <ComposableForm.Field {...fieldConfig(t)} {...props} />;
};
