'use client';

import { useTranslations } from 'next-intl';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';
import { Translation } from '@/types/layout';

const fieldConfig = (t: Translation) =>
  ({
    name: 'name',
    label: t('forms.name.label'),
    placeholder: t('forms.name.placeholder'),
    type: 'text',
  }) as FieldConfig;

export const NameField = (props: Partial<ComposableFormFieldProps>) => {
  const t = useTranslations();
  return <ComposableForm.Field {...fieldConfig(t)} {...props} />;
};
