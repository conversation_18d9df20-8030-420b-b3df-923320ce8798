import { useMemo } from 'react';
import { useContactsQuery } from '../../../lib/queries/contacts';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldOption } from '../types/formBuilder';
import { Search } from '@mui/icons-material';
import { useTranslations } from 'next-intl';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';

export function ProfileSelectField(props: Omit<ComposableFormFieldProps, 'options' | 'type'>) {
  const { contacts, loading } = useContactsQuery();
  const t = useTranslations('forms');

  const options = useMemo(
    () =>
      contacts.map<FieldOption>((c) => ({
        label: c.name ?? '-',
        value: c.id,
      })),
    [contacts]
  );

  return (
    <ComposableForm.Field
      icon={<Search />}
      type={'select'}
      label={t('trainee.label')}
      placeholder={t('trainee.placeholder')}
      loading={loading}
      options={options}
      actionPrimary={() => console.log('Edit employer entity')}
      actionPrimaryIcon={EditIcon}
      actionPrimaryLabel={'employerEntity.edit'} //aria label only
      actionPrimaryDisabled={true}
      actionSecondary={() => console.log('Add employer entity')}
      actionSecondaryIcon={AddCircleIcon}
      actionSecondaryLabel={'employerEntity.add'} // aria label only
      {...props}
    />
  );
}