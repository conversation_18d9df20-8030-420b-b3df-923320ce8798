import { useMemo } from 'react';
import { useCentersQuery } from '../../../lib/queries/centers';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { mapLabel } from '../../../utils';
import { useTranslations } from 'next-intl';

type FieldProps = Omit<ComposableFormFieldProps, 'type'>;

export const CentersSelectField = (props: FieldProps) => {
  const { centers, loading } = useCentersQuery();
  const t = useTranslations('forms');

  const options = useMemo(() => mapLabel(centers), [centers]);

  return (
    <ComposableForm.Field
      label={t('center.label')}
      placeholder={t('center.placeholder')}
      type={'select'}
      options={options}
      loading={loading}
      {...props}
    />
  );
};