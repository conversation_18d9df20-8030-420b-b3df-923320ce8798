'use client';

import { Translation } from '@/types/layout';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';
import { useTranslations } from 'next-intl';

const fieldConfig = (t: Translation): FieldConfig => ({
  name: 'phone',
  label: t('forms.phone.label'),
  placeholder: t('forms.phone.placeholder'),
  type: 'phonenumber',
});

export const PhoneField = (props: Partial<ComposableFormFieldProps>) => {
  const t = useTranslations();
  return <ComposableForm.Field {...fieldConfig(t)} {...props} />;
};
