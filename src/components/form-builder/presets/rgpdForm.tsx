import { rgpdFields } from '../formConfigs/userConfig';
import { ComposableForm } from '../ComposableFormBuilder';
import { useTranslations } from 'next-intl';

type GDPRConsent = `consent_${1 | 2 | 3 | 4 | 5}`;

export function RGPDForm({
  requiredConsents = [],
  readonly,
}: {
  requiredConsents?: GDPRConsent[];
  readonly?: boolean;
}) {
  const t = useTranslations('forms');
  return (
    <>
      {rgpdFields({ readonly, t }).map((f) => (
        <ComposableForm.Field
          key={f.name}
          required={requiredConsents.some((c) => c === `rgpd.${f.name}`)}
          {...f}
        />
      ))}
    </>
  );
}