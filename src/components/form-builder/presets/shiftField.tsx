import { useTranslations } from 'next-intl';
import { useOptionsIntl } from '../../../hooks/useFieldOptionsIntl';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';

const useShiftOptions = () =>
  useOptionsIntl(['morning', 'afternoon', 'night', 'none', 'saturday'], 'forms.schedule.options');

export const ShiftField = ({
  name = 'shift',
  label = 'schedule.label',
  placeholder = 'schedule.placeholder',
  type = 'select',
  ...rest
}: Omit<Partial<ComposableFormFieldProps>, 'options'>) => {
  const options = useShiftOptions();
  const t = useTranslations('forms');

  return (
    <ComposableForm.Field
      name={name}
      label={t(label)}
      placeholder={t(placeholder)}
      type={type}
      options={options}
      {...rest}
    />
  );
};
