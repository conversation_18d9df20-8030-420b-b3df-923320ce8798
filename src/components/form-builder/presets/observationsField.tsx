'use client';

import { useTranslations } from 'next-intl';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';
import { Translation } from '@/types/layout';

const fieldConfig = (t: Translation): FieldConfig => ({
  name: 'observations',
  label: t('observations.label'),
  placeholder: t('observations.placeholder'),
  type: 'text',
  multiline: true,
  colSpan: 12,
});

export const ObservationsField = (props: Partial<ComposableFormFieldProps>) => {
  const t = useTranslations('forms');
  return <ComposableForm.Field {...fieldConfig(t)} {...props} />;
};
