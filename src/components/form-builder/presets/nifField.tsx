'use client';

import { Translation } from '@/types/layout';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';
import { useTranslations } from 'next-intl';

const fieldConfig = (t: Translation): FieldConfig => ({
  name: 'nif',
  label: t('forms.nif.label'),
  placeholder: t('forms.nif.placeholder'),
  type: 'text',
});

export const NifField = (props: Partial<ComposableFormFieldProps>) => {
  const t = useTranslations();
  return <ComposableForm.Field {...fieldConfig(t)} {...props} />;
};
