'use client';

import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';

const fieldConfig: FieldConfig = {
  name: 'postalCode',
  label: 'forms.postalCode.label',
  placeholder: 'forms.postalCode.placeholder',
  type: 'text',
};

export const PostalCodeField = (props: Partial<ComposableFormFieldProps>) => (
  <ComposableForm.Field {...fieldConfig} {...props} />
);
