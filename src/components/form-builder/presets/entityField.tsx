import { Search } from '@mui/icons-material';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { useEntitiesQuery } from '../../../lib/queries/entities';
import { useMemo } from 'react';
import { FieldOption } from '../types/formBuilder';
import { useTranslations } from 'next-intl';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import { useTabsMethods } from '../../../hooks/useTabs';
import { useFormContext, useWatch } from 'react-hook-form';

export const EntitySelectField = (props: Omit<ComposableFormFieldProps, 'type' | 'options'>) => {
  const { entities, loading } = useEntitiesQuery();
  const t = useTranslations('forms');

  const options = useMemo(
    () =>
      entities.map<FieldOption>((e) => ({
        label: e.name || ' - ',
        value: e.id || ' - ',
      })),
    [entities]
  );

  const { control } = useFormContext();

  const [entity] = useWatch({
    control,
    name: [props.name],
  });

  const selectedEntity = entities.find((e) => e.id === entity);

  const { handleChangeTab } = useTabsMethods();

  return (
    <ComposableForm.Field
      icon={<Search />}
      label={t('entities.label')}
      placeholder={t('entities.placeholder')}
      options={options}
      loading={loading}
      type={'select'}
      actionPrimary={() =>
        handleChangeTab({
          id: `/dashboard/contacts/entities/${selectedEntity?.id}`,
          title: 'Entity Details',
        })
      }
      actionPrimaryIcon={EditIcon}
      actionPrimaryLabel={'employerEntity.edit'} //aria label only
      actionPrimaryDisabled={!selectedEntity}
      actionSecondary={() =>
        handleChangeTab({ id: '/dashboard/contacts/entities/new', title: 'New Entity' })
      }
      actionSecondaryIcon={AddCircleIcon}
      actionSecondaryLabel={'employerEntity.add'} // aria label only
      {...props}
    />
  );
};
