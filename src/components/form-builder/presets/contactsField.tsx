import { useTranslations } from 'next-intl';
import { useContactsQuery } from '../../../lib/queries/contacts';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';
import { Search } from '@mui/icons-material';
import { useMemo } from 'react';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import { useTabsMethods } from '../../../hooks/useTabs';
import { useFormContext, useWatch } from 'react-hook-form';

type FieldProps = Omit<ComposableFormFieldProps, 'type' | 'loading' | 'options'>;

export function ContactsField(props: FieldProps) {
  const t = useTranslations('forms');
  const { contacts, loading } = useContactsQuery();

  const contactOptions = useMemo(
    () =>
      contacts.map((c) => ({
        label: c.name || '',
        value: c.id,
      })),
    [contacts]
  );

  const { handleChangeTab } = useTabsMethods();

  const { control } = useFormContext();

  const [contact] = useWatch({
    control,
    name: [props.name],
  });

  const selectedContact = contacts.find((c) => c.id === contact);

  return (
    <>
      <ComposableForm.Field
        label={t('contact.label')}
        placeholder={t('contact.placeholder')}
        icon={<Search />}
        loading={loading}
        disableClearable
        options={contactOptions}
        required
        type="select"
        actionPrimary={() =>
          handleChangeTab({
            id: `/dashboard/contacts/people/${selectedContact?.id}`,
            title: 'Contact Details',
          })
        }
        actionPrimaryIcon={EditIcon}
        actionPrimaryLabel={t('contact.edit')}
        actionPrimaryDisabled={!selectedContact}
        actionSecondary={() =>
          handleChangeTab({ id: '/dashboard/contacts/people/new', title: 'New Contact' })
        }
        actionSecondaryIcon={AddCircleIcon}
        actionSecondaryLabel={t('contact.add')}
        {...props}
      />
    </>
  );
}
