'use client';

import { MailOutline } from '@mui/icons-material';
import { ComposableForm } from '../ComposableFormBuilder';
import { useFormContext, useWatch } from 'react-hook-form';
import { useOptionsIntl } from '../../../hooks/useFieldOptionsIntl';
import { useEffect } from 'react';
import { useTranslations } from 'next-intl';

type FieldNames = Partial<{
  baseEducation: string;
  baseType9: string;
  baseType12: string;
}>;

const useBaseEducationOptions = () =>
  useOptionsIntl(['level2', 'level3', 'level2And3'], 'forms.baseEducation.options');

const useBaseType9Options = () =>
  useOptionsIntl(['ufcd25', 'ufcd50', 'A', 'B', 'C'], 'forms.baseType9.options');

const useBaseType12OptionsLevel2And3 = () =>
  useOptionsIntl(['A', 'B', 'C', 'portaria', 'ufcd25', 'ufcd50'], 'forms.baseType12.options');

const useBaseType12OptionsLevel3 = () => useOptionsIntl(['A'], 'forms.baseType12.options');

export function BaseLearningFields({
  readonly = false,
  fieldNames = {},
}: {
  readonly?: boolean;
  fieldNames?: FieldNames;
}) {
  const {
    baseEducation = 'baseEducation',
    baseType9 = 'baseType9',
    baseType12 = 'baseType12',
  } = fieldNames;
  const t = useTranslations('forms');

  const { control, setValue } = useFormContext();

  const [baseEducationValue, baseType12Value] = useWatch({
    control,
    name: [baseEducation, baseType12],
  });

  useEffect(() => {
    switch (baseEducationValue) {
      case null:
      case undefined:
        setValue(baseType9, null);
        setValue(baseType12, null);
        break;
      case 'level2':
        setValue(baseType12, null);
        break;
      case 'level3':
        setValue(baseType9, null);
        break;
      case 'level2And3':
        if (baseType12Value !== 'A') {
          setValue(baseType12, null);
        }
        break;
    }
  }, [baseEducationValue]);

  const baseEducationOptions = useBaseEducationOptions();

  const baseType9Options = useBaseType9Options();

  const baseType12OptionsLevel3 = useBaseType12OptionsLevel3();
  const baseType12OptionsLevel2And3 = useBaseType12OptionsLevel2And3();

  return (
    <>
      <ComposableForm.Field
        name={baseEducation}
        label={t('baseEducation.label')}
        placeholder={t('baseEducation.placeholder')}
        type="select"
        icon={<MailOutline />}
        options={baseEducationOptions}
        disableClearable={false}
        readonly={readonly}
      />
      <ComposableForm.Field
        name={baseType9}
        label={t('baseType9.label')}
        placeholder={t('baseType9.placeholder')}
        options={baseType9Options}
        readonly={!baseEducationValue || baseEducationValue === 'level3' || readonly}
        type="select"
      />
      <ComposableForm.Field
        name={baseType12}
        label={t('baseType12.label')}
        placeholder={t('baseType12.placeholder')}
        readonly={!baseEducationValue || baseEducationValue === 'level2' || readonly}
        options={
          baseEducationValue === 'level3'
            ? baseType12OptionsLevel3
            : baseEducationValue === 'level2And3'
              ? baseType12OptionsLevel2And3
              : []
        }
        type="select"
      />
    </>
  );
}