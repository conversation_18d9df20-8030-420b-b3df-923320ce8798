import { useTranslations } from 'next-intl';
import { useOptionsIntl } from '../../../hooks/useFieldOptionsIntl';
import { ComposableForm, ComposableFormFieldProps } from '../ComposableFormBuilder';

const useOptions = () => useOptionsIntl(['customer', 'business'], 'forms.traineeType.options');

export function TraineeTypeField(
  props: Partial<Omit<ComposableFormFieldProps, 'options' | 'type'>>
) {
  const options = useOptions();
  const t = useTranslations('forms');

  return (
    <ComposableForm.Field
      name={'traineeType'}
      label={t('traineeType.label')}
      placeholder={t('traineeType.placeholder')}
      type={'select'}
      options={options}
      {...props}
    />
  );
}