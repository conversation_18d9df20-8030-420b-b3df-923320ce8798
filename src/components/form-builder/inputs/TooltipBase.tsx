'use client';

import { Tooltip, Box } from '@mui/material';

interface BaseTooltipProps {
  label: string;
  tooltip?: string;
  children?: React.ReactElement;
}

const BaseTooltip: React.FC<BaseTooltipProps & { shouldTranslate?: boolean }> = ({
  label,
  tooltip,
  children,
}) => {
  const title = tooltip ?? label;

  return (
    <Tooltip followCursor title={title}>
      {children ?? (
        <Box
          component="p"
          sx={{
            display: 'block',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '100%',
          }}
        >
          {label}
        </Box>
      )}
    </Tooltip>
  );
};

export default BaseTooltip;