import React from 'react';
import { Controller, FieldValues, Path, useFormContext } from 'react-hook-form';
import { Box, Button, Typography } from '@mui/material';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';

interface FileUploadFieldProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  label?: string;
  disabled?: boolean;
  placeholder?: string;
}

const FileUploadField = <TFormValues extends FieldValues>({
  name,
  label,
  disabled,
  placeholder,
}: FileUploadFieldProps<TFormValues>) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState }) => (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            width: '100%',
          }}
        >
          {label && (
            <Typography sx={{ fontWeight: 500, mb: 1, color: '#1976d2' }}>{label}</Typography>
          )}
          <Button
            variant="contained"
            component="label"
            startIcon={<InsertDriveFileIcon sx={{ color: '#1976d2' }} />}
            sx={{
              width: '100%',
              bgcolor: '#e3f2fd',
              color: '#1976d2',
              '&:hover': { bgcolor: '#bbdefb' },
              mb: 1,
            }}
            disabled={disabled}
          >
            {/* this sucks for truncate, find better way */}
            {
              <Typography
                variant="body2"
                sx={{
                  color: '#1976d2',
                  maxWidth: 240,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value?.name?.length > 7
                  ? value.name.slice(0, 7) + '...'
                  : value?.name ?? placeholder}
              </Typography>
            }
            <input
              type="file"
              hidden
              onChange={(e) => {
                const file = e.target.files?.[0];
                onChange(file || null);
              }}
              disabled={disabled}
            />
          </Button>
          {fieldState.error && (
            <Typography variant="caption" color="error">
              {fieldState.error.message}
            </Typography>
          )}
        </Box>
      )}
    />
  );
};

export default FileUploadField;
