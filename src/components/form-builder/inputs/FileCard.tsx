import React from 'react';
import { <PERSON>, Card, CardContent, Typography, IconButton, Stack } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import DeleteIcon from '@mui/icons-material/Delete';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { FieldConfig } from '../types/formBuilder';

interface CardFieldProps {
  field: FieldConfig;
  methods: { getValues: (path: string) => any; unregister: (path: string) => void };
}

function formatFileSize(size: number | undefined): string {
  if (!size && size !== 0) return '';
  if (size < 1024) return `${size} bytes`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} kb`;
  return `${(size / (1024 * 1024)).toFixed(1)} mb`;
}

const FileCard: React.FC<CardFieldProps> = ({ field, methods }) => {
  const data = methods.getValues(`${field.parent}.${field.id}`) ?? {};
  console.log('FileCard data:', data, field);
  const fileName = data.fileName ?? 'document_file_name.pdf';
  const fileSize = formatFileSize(data.file?.size);

  return (
    <Card variant="outlined" sx={{ borderRadius: 2, mb: 2, boxShadow: 0 }}>
      <CardContent sx={{ p: 2 }}>
        <Stack spacing={1}>
          {/* File name, size */}
          <Box>
            <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
              {fileName}
            </Typography>
            {fileSize && (
              <Typography variant="caption" color="text.secondary">
                {fileSize}
              </Typography>
            )}
          </Box>
          {/* Content Row */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
            {/* File Icon */}
            <InsertDriveFileIcon sx={{ color: '#33c0ee', mt: 0.5 }} />
            {/* Info */}
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2">
                <b>Tipo:</b> <span style={{ color: '#8c8fa6' }}>{data?.fileType?.label}</span>
              </Typography>
              <Typography variant="body2">
                <b>Descrição:</b>{' '}
                <span style={{ color: '#8c8fa6' }}>{data.description || 'Texto'}</span>
              </Typography>
              <Typography variant="body2">
                <b>Etiquetas:</b> <span style={{ color: '#8c8fa6' }}>{data.tags || 'Texto'}</span>
              </Typography>
            </Box>
            {/* Actions */}
            <Stack spacing={0.5} direction="column" alignItems="center">
              <IconButton color="primary" size="small" onClick={() => field.downloadAction?.()}>
                <DownloadIcon sx={{ color: '#33c0ee' }} />
              </IconButton>
              {!field.readonly && (
                <IconButton color="error" size="small" onClick={() => field.deleteAction?.()}>
                  <DeleteIcon sx={{ color: '#ee3333' }} />
                </IconButton>
              )}
            </Stack>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default FileCard;
