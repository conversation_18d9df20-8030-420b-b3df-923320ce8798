import FormBuilderDialog from '@/components/ui/dialog/FormBuilderDialog';
import { Box, Button, IconButton, Typography } from '@mui/material';
import { ReactNode, useState } from 'react';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { ComposableForm } from '../ComposableFormBuilder';
import { FieldConfig } from '../types/formBuilder';

interface FileUploadModalProps {
  field: FieldConfig;
  labelComponent: (label?: string) => React.ReactNode;
  children?: ReactNode;
}
const FileUploadModal = ({ field, children, labelComponent }: FileUploadModalProps) => {
  const [open, setOpen] = useState<{ open: true; id: string } | { open: false; id: undefined }>({
    open: false,
    id: undefined,
  });

  const fileFields = (
    <>
      {labelComponent(field.subLabel)}
      <ComposableForm.Internal.Layout key={field.name}>{children}</ComposableForm.Internal.Layout>
    </>
  );

  if (field.readonly) {
    return fileFields;
  }

  const id = `${field.name}.attachments.${open.id}`;

  return (
    <>
      {open?.id && (
        <FormBuilderDialog
          cancelLabel={field.cancelLabel ?? 'Cancelar'}
          confirmLabel={field.confirmLabel ?? 'Submeter'}
          confirmIcon={field.confirmIcon ?? undefined}
          open={open.open}
          onCancel={() => {
            field.cancelAction?.();
            setOpen({ open: false, id: undefined });
          }}
          onConfirm={() => {
            field.submitAction?.({ id: open.id });
            setOpen({ open: false, id: undefined });
          }}
          title={field.modalLabel ?? ''}
        >
          <ComposableForm.Internal.Layout>{field.modalNode?.(id)}</ComposableForm.Internal.Layout>
        </FormBuilderDialog>
      )}
      {labelComponent()}
      <Box
        sx={{
          border: '1.5px dashed #e4e4e4',
          borderRadius: '12px',
          padding: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          minHeight: 200,
          justifyContent: 'center',
          bgcolor: 'background.paper',
          cursor: 'pointer',
          transition: 'border-color 0.2s',
          '&:hover': {
            borderColor: 'primary.main',
          },
        }}
        onClick={() => {
          if (!field.createAction) return;

          const id = field.createAction();
          setOpen({ open: true, id });
        }}
      >
        <IconButton
          disabled
          sx={{
            bgcolor: '#F4F4F4',
            mb: 1,
            '& .MuiSvgIcon-root': { fontSize: 40, color: '#757575' },
            pointerEvents: 'none',
          }}
        >
          <CloudUploadIcon />
        </IconButton>
        <Button
          variant="text"
          sx={{
            color: '#00b3e6',
            fontWeight: 500,
            textTransform: 'none',
            mb: 0.5,
            fontSize: 16,
          }}
        >
          Adicionar ficheiro
        </Button>
        <Typography color="text.secondary" fontSize={14} textAlign="center">
          CSV, PDF, PNG ou JPG (max. 25MB)
        </Typography>
      </Box>
      {fileFields}
    </>
  );
};

export default FileUploadModal;
