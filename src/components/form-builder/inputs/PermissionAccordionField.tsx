'use client';

import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Checkbox,
  Stack,
  Typography,
} from '@mui/material';
import { ExpandMore } from '@mui/icons-material';
import { Controller, useFormContext } from 'react-hook-form';
import PermissionTable, {
  PermissionRowDef,
} from '@/app/[locale]/(protected)/dashboard/profiles/components/PermissionTable';

interface Feature {
  key: string;
  label: string;
  rows: PermissionRowDef[];
}

interface Group {
  key: string;
  label: string;
  description: string;
  features: Feature[];
}

interface Section {
  key: string;
  label: string;
  groups: Group[];
}

interface PermissionAccordionFieldProps {
  permissionSections: Section[];
  disabled?: boolean;
}

const PermissionAccordionField: React.FC<PermissionAccordionFieldProps> = ({
  permissionSections,
  disabled,
}) => {
  const { control, setValue, getValues } = useFormContext();

  return (
    <>
      {permissionSections.map((section) => (
        <Box key={section.key} mb={3}>
          <Stack gap={4}>
            <Typography variant="h5" mb={1}>
              {section.label}
            </Typography>

            {section.groups.map((group) => (
              <Accordion key={group.key} disabled={disabled}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Controller
                    name={group.key}
                    control={control}
                    defaultValue={false}
                    render={({ field }) => {
                      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                        const checked = e.target.checked;
                        field.onChange(checked);

                        group.features.forEach((f) =>
                          setValue(f.key as string, checked, { shouldDirty: true })
                        );
                      };

                      return (
                        <Checkbox
                          {...field}
                          checked={!!field.value}
                          onChange={handleChange}
                          onClick={(e) => e.stopPropagation()}
                          onFocus={(e) => e.stopPropagation()}
                        />
                      );
                    }}
                  />

                  <Stack direction="column" alignItems="flex-start" gap={0.5}>
                    <Typography variant="h6">{group.label}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {group.description}
                    </Typography>
                  </Stack>
                </AccordionSummary>

                <Stack gap={4}>
                  <AccordionDetails>
                    {group.features.map((feature) => (
                      <Accordion
                        key={feature.key}
                        disableGutters
                        sx={(t) => ({
                          backgroundColor: t.palette.background.paper,
                          mb: 2,
                        })}
                      >
                        <AccordionSummary expandIcon={<ExpandMore />}>
                          <Stack direction="row" alignItems="center">
                            <Controller
                              name={feature.key}
                              control={control}
                              defaultValue={false}
                              render={({ field }) => {
                                const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
                                  const checked = e.target.checked;
                                  field.onChange(checked);

                                  const featureKeys = group.features.map((f) => f.key) as string[];
                                  const featureValues = featureKeys.map((k) => getValues(k));

                                  const allChecked = featureValues.every(Boolean);
                                  setValue(group.key as string, allChecked, { shouldDirty: true });
                                };

                                return (
                                  <Checkbox
                                    {...field}
                                    checked={!!field.value}
                                    onChange={handleChange}
                                    onClick={(e) => e.stopPropagation()}
                                    onFocus={(e) => e.stopPropagation()}
                                  />
                                );
                              }}
                            />
                            <Typography variant="body1">{feature.label}</Typography>
                          </Stack>
                        </AccordionSummary>

                        <AccordionDetails>
                          <PermissionTable rows={feature.rows} />
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </AccordionDetails>
                </Stack>
              </Accordion>
            ))}
          </Stack>
        </Box>
      ))}
    </>
  );
};

export default PermissionAccordionField;
