'use client';

import { ReactNode, useRef } from 'react';
import { Controller, FieldValues, Path, useFormContext } from 'react-hook-form';
import { Autocomplete, TextField, Box, Typography, Checkbox, ListItem } from '@mui/material';
import { FieldOption } from '@/components/form-builder/types/formBuilder';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { uniq } from 'lodash';

export interface SelectFieldProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  icon?: ReactNode;
  options: FieldOption[];
  required?: boolean;
  placeholder?: string;
  labelComponent?: React.ReactNode;
  error?: { message: string };
  multiple?: boolean;
  disableClearable?: boolean;
  disabled?: boolean;
  iconPrimary?: React.ReactNode; // Optional primary
  iconSecondary?: React.ReactNode; // Optional secondary
  resultsOnlyWithSearch?: boolean; // If true, the select will show results after searching
}

const SelectField = <TFormValues extends FieldValues = FieldValues>({
  name,
  icon,
  required,
  options = [],
  disableClearable,
  labelComponent,
  resultsOnlyWithSearch = false,
  disabled,
  placeholder,
  multiple = false,
  iconPrimary = undefined,
  iconSecondary = undefined,
}: SelectFieldProps<TFormValues>) => {
  const { control } = useFormContext();

  const wrapperRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {labelComponent}
      <Box
        display="flex"
        alignItems="center"
        width="100%"
        sx={{
          gap: 1,
          flexWrap: 'nowrap',
        }}
        ref={wrapperRef}
      >
        <Box flex="1 1 0" minWidth={0}>
          <Controller
            name={name}
            control={control}
            rules={{
              required,
            }}
            render={({ field: { onChange, ...restField }, fieldState }) => {
              // Handle value for single/multiple
              const formattedValue: any[] | any = multiple
                ? restField.value ?? []
                : restField.value ?? null;

              const values = Array.isArray(formattedValue)
                ? formattedValue
                : formattedValue
                ? [formattedValue]
                : [];

              const selectedOptions = options.filter((o) => values.some((v) => v == o.value));

              return (
                <Autocomplete
                  {...restField}
                  multiple={multiple}
                  options={options}
                  value={multiple ? selectedOptions : selectedOptions[0] || null}
                  isOptionEqualToValue={(option, value) => {
                    return !!value && option.value === value.value;
                  }}
                  onChange={(_, options) => {
                    if (multiple) {
                      onChange(uniq((options as FieldOption[]).map((o) => o.value)));
                    } else {
                      onChange((options as FieldOption)?.value || null);
                    }
                  }}
                  disabled={disabled}
                  popupIcon={<ArrowDropDownIcon />}
                  disableClearable={disableClearable}
                  disableCloseOnSelect={multiple}
                  filterOptions={(opts, state) => {
                    if (resultsOnlyWithSearch) {
                      if (selectedOptions.length) {
                        return selectedOptions;
                      }

                      if (state.inputValue) {
                        // Show filtered options only when searching
                        return opts.filter((o) =>
                          o.label.toLowerCase().includes(state.inputValue.toLowerCase())
                        );
                      }
                      // Otherwise, show nothing
                      return [];
                    }
                    // Default filtering
                    return opts;
                  }}
                  renderOption={(props, option, _, { value: selectedValue }) => {
                    return (
                      <ListItem {...props} key={props.key} alignItems={'center'}>
                        <>
                          {multiple ? (
                            <Checkbox
                              checked={
                                Array.isArray(selectedValue)
                                  ? selectedValue.some((v) => v.value === option.value)
                                  : !!selectedValue && selectedValue.value === option.value
                              }
                            />
                          ) : undefined}
                          <Typography>{option.label}</Typography>
                        </>
                      </ListItem>
                    );
                  }}
                  renderValue={() => {
                    return selectedOptions.length ? (
                      <Typography
                        display={'flex'}
                        flexDirection={'row'}
                        flexWrap={'nowrap'}
                        whiteSpace={'nowrap'}
                        overflow={'hidden'}
                        sx={{
                          pl: 1,
                          color: disabled ? 'text.disabled' : 'inherit',
                        }}
                      >
                        {selectedOptions.map((o) => o.label).join(', ')}
                      </Typography>
                    ) : null;
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      error={!!fieldState.error}
                      sx={{
                        flexFlow: 'row nowrap',
                        flexDirection: 'column',
                        overflow: 'hidden',
                      }}
                      placeholder={!values.length || multiple ? placeholder : undefined}
                      disabled={disabled}
                      helperText={fieldState.error?.message ?? '\u00A0'}
                      slotProps={{
                        input: {
                          ...params.InputProps,
                          startAdornment: (
                            <>
                              {icon}
                              {params.InputProps.startAdornment}
                            </>
                          ),
                          inputProps: params.inputProps,
                        },
                      }}
                    />
                  )}
                />
              );
            }}
          />
        </Box>
        {(iconPrimary || iconSecondary) && (
          <Box sx={{ marginTop: '-24px' }}>
            {iconPrimary}
            {iconSecondary}
          </Box>
        )}
      </Box>
    </>
  );
};
export default SelectField;
