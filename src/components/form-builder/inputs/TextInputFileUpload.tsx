import { Box, Button, ButtonOwnProps, TextField, Typography } from '@mui/material';
import FieldWrapper, { FieldWrapperProps } from '../FieldWrapper';
import { ReactNode } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { AttachFile } from '@mui/icons-material';

type TextInputFileUploadProps = {
  placeholder?: string;
  name: string;
  buttonContent?: ReactNode;
  buttonColor?: ButtonOwnProps['color'];
  disabled?: boolean;
};

const DefaultButtonContent = () => {
  const t = useTranslations();

  return (
    <>
      <AttachFile />
      {t('forms.upload')}
    </>
  );
};

export function TextInputFileUploadField({
  placeholder,
  name,
  buttonContent,
  buttonColor,
  disabled,
  ...fieldWrapperProps
}: TextInputFileUploadProps & FieldWrapperProps) {
  const methods = useFormContext();

  return (
    <FieldWrapper {...fieldWrapperProps}>
      <Controller
        name={name}
        control={methods.control}
        rules={{
          required: fieldWrapperProps.required,
        }}
        render={({ field, fieldState }) => (
          <Box sx={{ display: 'flex', flexWrap: 'nowrap', gap: 1 }} width={'100%'}>
            <TextField
              disabled
              placeholder={placeholder}
              required={fieldWrapperProps.required}
              fullWidth
              error={!!fieldState.error}
              key={field.value?.name}
              defaultValue={field.value?.name}
              helperText={fieldState?.error?.message ?? '\u00A0'}
            />
            <Button
              variant={'contained'}
              component="label"
              color={buttonColor}
              disabled={disabled}
              sx={{
                display: 'flex',
                gap: 0.5,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <input
                type="file"
                hidden
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  field.onChange(file || null);
                }}
                disabled={disabled}
              />
              {buttonContent ?? <DefaultButtonContent />}
            </Button>
            {fieldState.error && (
              <Typography variant="caption" color="error">
                {fieldState.error.message}
              </Typography>
            )}
          </Box>
        )}
      />
    </FieldWrapper>
  );
}
