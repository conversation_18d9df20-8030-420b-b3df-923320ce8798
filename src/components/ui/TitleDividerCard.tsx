'use client';

import { Card, CardContent, Divider, Typography } from '@mui/material';
import { ReactNode } from 'react';

export interface TitleDividerCardProps {
  title: string;
  children: ReactNode;
}

export default function TitleDividerCard({ title, children }: TitleDividerCardProps) {
  return (
    <Card>
      <CardContent sx={{ py: 2 }}>
        <Typography variant="overline" color="primary.main">
          {title.toUpperCase()}
        </Typography>

        <Divider sx={{ mt: 1, mb: 2, borderColor: 'primary.main' }} />

        {children}
      </CardContent>
    </Card>
  );
}
