'use client';

import React from 'react';
import { Typography, TextField, TextFieldProps, Box } from '@mui/material';

export interface LabeledTextareaProps extends Omit<TextFieldProps, 'label'> {
  label: string;
  rows?: number;
  width?: string | number;
}

export default function LabeledTextarea({
  label,
  rows = 4,
  helperText,
  width = '100%',
  ...textfieldProps
}: LabeledTextareaProps) {
  return (
    <Box width={width}>
      <Typography variant="alertTitle" component="label" sx={{ display: 'block', mb: 1 }}>
        {label}
      </Typography>

      <TextField multiline rows={rows} fullWidth helperText={helperText} {...textfieldProps} />
    </Box>
  );
}
