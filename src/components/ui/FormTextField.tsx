import React from 'react';
import { TextField, TextFieldProps } from '@mui/material';
import { FieldError } from 'react-hook-form';

export interface FormTextFieldProps extends Omit<TextFieldProps, 'error' | 'helperText' | 'name'> {
  /**
   * Register name passed from react-hook-form (e.g. {...register('email')}).
   * This prop is only used to forward rest props; it does not render by itself.
   */
  name: string;
  /**
   * Field error from react-hook-form, coming from Zod resolver.
   */
  error?: FieldError;
  /**
   * Default helper text when there's no error.
   */
  helperText?: string;
}

export function FormTextField({ error, helperText, ...props }: FormTextFieldProps) {
  return <TextField {...props} error={!!error} helperText={error ? error.message : helperText} />;
}

// Usage example in a form:
//
// import { useForm } from 'react-hook-form';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { loginSchema, type LoginFormData } from '@/schemas/authSchema';
// import { FormTextField } from '@/components/ui/FormTextField';
//
// const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>({
//   resolver: zodResolver(loginSchema),
// });
//
// <FormTextField
//   label="Email"
//   type="email"
//   fullWidth
//   {...register('email')}
//   error={errors.email}
// />
