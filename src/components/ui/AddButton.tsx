import { AddCircle } from '@mui/icons-material';
import { Button, SxProps, useTheme } from '@mui/material';
import { ReactNode } from 'react';

export function AddButton({
  sx = {},
  onClick,
  children,
  disabled = false,
}: {
  sx?: SxProps;
  disabled?: boolean;
  children?: ReactNode;
  onClick: () => any;
}) {
  const theme = useTheme();

  return (
    <Button
      onClick={onClick}
      startIcon={<AddCircle />}
      sx={{
        alignItems: 'flex-start',
        fontSize: '0,9375rem',
        color: disabled ? theme.palette.text.disabled : theme.palette.primary.main,
        ...sx,
      }}
    >
      {children}
    </Button>
  );
}
