'use client';

import { Edit, Save } from '@mui/icons-material';
import type { FooterAction } from '@/components/ui/FormActions';
import { VIEW } from '@/constants/enums';
import { useTranslations } from 'next-intl';
import { useTabsMethods } from '@/hooks/useTabs';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import FormActions from '@/components/ui/FormActions';
import { run } from '../../../utils';

type DefaultFooterActionsProps = {
  view: VIEW;
  shouldCloseTab?: boolean;
  saveDisabled?: boolean;
  extraActions?: FooterAction[];
};
export function DefaultFooterActions({
  view,
  extraActions = [],
  shouldCloseTab,
}: DefaultFooterActionsProps) {
  const { openTab, handleCloseTab, handleUpdateTabView } = useTabsMethods();
  const t = useTranslations();
  const closeTab = shouldCloseTab ?? view === VIEW.CREATE;
  const { formState } = useFormContext();
  const disabled =
    /*!formState.isDirty || formState.disabled || !!formState.errors.length || */ !formState.isValid;

  const actions = useMemo<FooterAction[]>(
    () => [
      ...run<FooterAction[]>(() => {
        if (view === VIEW.VIEW) {
          return [
            {
              icon: <Edit />,
              label: t('forms.label.edit'),
              color: 'primary',
              onClick: () => handleUpdateTabView({ view: VIEW.EDIT }),
              type: 'button',
            },
          ];
        }
        // menu dummy data below
        return [
          {
            label: t('forms.label.cancel'),
            variant: 'outlined',
            type: 'reset',
            onClick: () => {
              if (closeTab) {
                handleCloseTab({ id: openTab()?.id ?? '' });
              } else {
                handleUpdateTabView({ view: VIEW.VIEW });
              }
            },
          },
          {
            icon: <Save />,
            label: t('forms.label.save'),
            color: 'primary',
            type: 'submit',
            disabled: disabled,
          },
        ];
      }),
      ...extraActions,
    ],
    [view, closeTab, disabled, openTab, t, extraActions]
  );

  return <FormActions actions={actions} />;
}
