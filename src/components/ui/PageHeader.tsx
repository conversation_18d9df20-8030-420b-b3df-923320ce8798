'use client';

import { usePathname, useParams } from 'next/navigation';
import { PAGE_HEADERS } from '@/config/pageHeaders';
import { Typography, Button, Box, Stack } from '@mui/material';
import Link from 'next/link';
import { matchPath } from '@/utils';
import { useTranslations } from 'next-intl';
import usePageHeaderStore from '@/store/PageHeader';
import Dropdown from './dropdown/Dropdown';
import { useTabsMethods } from '@/hooks/useTabs';

function resolvePathVars(path: string, vars: Record<string, string | number>) {
  return Object.entries(vars).reduce(
    (acc, [key, val]) => acc.replace(new RegExp(`:${key}\\b`, 'g'), String(val)),
    path
  );
}

export type MenuHandlerMap = Record<string, () => void>;

const stripTrailingSlash = (s: string) => (s.endsWith('/') && s !== '/' ? s.slice(0, -1) : s);

export default function PageHeader() {
  const t = useTranslations('dashboard');
  const tMenu = useTranslations('menu');
  const raw = usePathname() || '';
  const params = useParams<Record<string, string>>();
  const clean = stripTrailingSlash(raw.replace(/^\/[a-z]{2}(?:-[A-Z]{2})?/, ''));
  const { handleChangeTab } = useTabsMethods();

  const headers = PAGE_HEADERS(t, tMenu);
  const found =
    headers.find(([pattern]) => !pattern.includes(':') && stripTrailingSlash(pattern) === clean) ??
    headers.find(
      ([pattern]) => pattern.includes(':') && matchPath(stripTrailingSlash(pattern), clean)
    );

  const handlers = usePageHeaderStore((s) => s.handlers);

  const config = found?.[1];
  if (!config) return null;

  return (
    <Box display="flex" alignItems="center" justifyContent="space-between" mb={3} gap={2}>
      <Box>
        <Typography variant="h5">{config.title}</Typography>
        {config.subtitle && (
          <Typography variant="body1" color="text.secondary">
            {config.subtitle}
          </Typography>
        )}
      </Box>
      <Stack direction="row" spacing={2} sx={{ flexShrink: 0 }}>
        {config?.secondaryAction?.href && (
          <Button
            component={Link}
            variant="outlined"
            href={resolvePathVars(config.secondaryAction.href, params)}
            startIcon={config.secondaryAction.icon?.()}
          >
            {config.secondaryAction.label}
          </Button>
        )}
        {config.action &&
          Object.keys(config.action).length > 0 &&
          (config?.action?.menuOptions ? (
            <>
              <Dropdown label={config.action.label} yDirection="down" xDirection="left">
                {config?.action?.menuOptions(handlers)}
              </Dropdown>
              {/*               <SplitMenuButton
                label={t(config.action.label)}
                items={config.action.menu.map(resolveItem)}
              />
            */}
            </>
          ) : config?.action?.href ? (
            <Button
              component="button"
              onClick={() => handleChangeTab({ id: config.action!.href })}
              variant="contained"
              startIcon={config.action.icon?.()}
            >
              {config.action.label}
            </Button>
          ) : config?.action?.onClick ? (
            <Button
              component="button"
              onClick={() => {
                // Check if it's a handler function or a direct function
                if (typeof config.action!.onClick === 'function') {
                  config.action!.onClick();
                } else if (handlers[config.action!.onClick as string]) {
                  handlers[config.action!.onClick as string]();
                }
              }}
              variant="contained"
              startIcon={config.action.icon?.()}
            >
              {config.action.label}
            </Button>
          ) : null)}
      </Stack>
      {config.actions && <Box>{config.actions}</Box>}
    </Box>
  );
}
