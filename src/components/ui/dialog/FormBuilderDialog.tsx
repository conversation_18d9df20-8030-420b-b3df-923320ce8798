import {
  Box,
  Breakpoint,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import { identity } from 'lodash';
import { ReactNode } from 'react';

interface FormBuilderDialogProps {
  children: ReactNode;
  open: boolean;
  onCancel: () => void;
  onConfirm?: () => void;
  title: string;
  maxWidth?: Breakpoint | false;
  cancelLabel?: string;
  confirmLabel?: string;
  confirmIcon?: ReactNode;
  confirmColor?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  showActions?: boolean;
  confirmDisabled?: boolean;
}
export default function FormBuilderDialog({
  open,
  onCancel,
  onConfirm = identity,
  children,
  title,
  maxWidth,
  showActions = true,
  cancelLabel,
  confirmIcon,
  confirmLabel,
  confirmColor = 'primary',
  confirmDisabled = false,
}: FormBuilderDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="formBuilder-dialog-title"
      aria-describedby="formBuilder-dialog-description"
      fullWidth
      maxWidth={maxWidth}
    >
      <DialogTitle id="formBuilder-dialog-title">{title}</DialogTitle>
      <DialogContent dividers>{children}</DialogContent>

      <DialogActions sx={{ px: 3, p: 2 }}>
        {showActions && (
          <>
            <Button onClick={onCancel} variant="outlined">
              {cancelLabel}
            </Button>

            <Button
              startIcon={confirmIcon}
              onClick={onConfirm}
              variant="contained"
              color={confirmColor}
              disabled={confirmDisabled}
              autoFocus
            >
              {confirmLabel}
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
}

export function LoadingDialog({
  title = '',
  maxWidth,
  onCancel = identity,
}: {
  title?: string;
  maxWidth?: Breakpoint | false;
  onCancel?: () => any;
}) {
  return (
    <FormBuilderDialog
      title={title}
      maxWidth={maxWidth}
      open
      showActions={false}
      onCancel={onCancel}
      onConfirm={identity}
    >
      <Box
        width={'100%'}
        display={'flex'}
        justifyContent={'center'}
        alignItems={'center'}
        overflow={'hidden'}
      >
        <CircularProgress size={100} color="inherit" />
      </Box>
    </FormBuilderDialog>
  );
}
