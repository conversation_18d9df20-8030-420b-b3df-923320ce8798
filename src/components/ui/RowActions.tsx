'use client';

import React from 'react';
import { IconButton, type SxProps } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Dropdown, { ColorType, DropdownOptions } from './dropdown/Dropdown';

export type ActionItem = {
  label: string;
  disabled?: boolean;
  icon?: React.ElementType;
  onClick: () => void;
  color?: ColorType;
  iconColor?: ColorType;
  sx?: SxProps;
};

interface RowActionsProps {
  actions?: ActionItem[];
  componentActions?: React.ReactNode;
  lineId: string;
}

const CustomMenuOpener = ({ id, onClick }: { id: string; onClick: () => void }) => {
  return (
    <IconButton id={id} onClick={onClick} size="small">
      <MoreVertIcon />
    </IconButton>
  );
};

//passing actions with a default structure will spawn actions, passing componentActions will spawn a custom component, easier for submenus and to write, both options valid
export default function RowActions({ actions = [], componentActions, lineId }: RowActionsProps) {
  return (
    <Dropdown
      dropdownId={`table-menu-dropdown-${lineId}`}
      containerId={`table-menu-container-${lineId}`}
      key={`table-menu-actions-${lineId}`}
      CustomButton={CustomMenuOpener}
      usePortal
    >
      {/* right now its only capable of spawning 1 level with this method, but its a easy fix to spawn multiple levels, needs a recursive spawn method  */}
      {actions?.map((action, idx) => (
        <DropdownOptions.Option
          key={action.label + idx}
          label={action.label}
          icon={action.icon}
          iconColor={action?.iconColor}
          color={action?.color}
          onClick={action.onClick}
        />
      ))}
      {componentActions}
    </Dropdown>
  );
}
