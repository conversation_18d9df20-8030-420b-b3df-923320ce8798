// src/components/ui/CmImage.tsx
import Image, { ImageProps } from 'next/image';

/**
 * CmImage always:
 * - fills the viewport width (100vw), so the browser can pick the right srcset
 * - is marked `priority` by default
 */
export function CmImage(props: Omit<ImageProps, 'sizes' | 'priority'>) {
  return (
    <Image
      {...props}
      alt={props.alt || ''}
      sizes="100vw"
      priority
      style={{ width: '100%', height: 'auto' }}
    />
  );
}
