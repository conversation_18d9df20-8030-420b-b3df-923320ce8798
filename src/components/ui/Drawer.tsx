'use client';

import { <PERSON>er, Toolbar, IconButton, Divider, Box, SxProps, Theme, useTheme } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { ReactNode } from 'react';

export type PersistentDrawerProps = {
  open: boolean;
  onToggle: () => void;
  width?: number;
  children: ReactNode;
  sx?: SxProps<Theme>;
};

export default function PersistentDrawer({ open, onToggle, children }: PersistentDrawerProps) {
  const theme = useTheme();

  const width = open ? theme.shape.drawerWidth : theme.shape.drawerMini;
  return (
    <Drawer
      variant="permanent"
      anchor="left"
      open={open}
      sx={{
        width,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width,
          overflowX: 'hidden',
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: open
              ? theme.transitions.duration.enteringScreen
              : theme.transitions.duration.leavingScreen,
          }),
        },
      }}
    >
      <Toolbar
        sx={{
          justifyContent: open ? 'flex-end' : 'center',
          px: 1,
        }}
      >
        <IconButton onClick={onToggle} size="small">
          {open ? <ChevronLeft /> : <ChevronRight />}
        </IconButton>
      </Toolbar>

      <Divider />

      <Box sx={{ flexGrow: 1 }}>{children}</Box>
    </Drawer>
  );
}