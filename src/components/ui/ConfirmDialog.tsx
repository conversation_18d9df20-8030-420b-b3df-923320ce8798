'use client';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Typography,
  Breakpoint,
} from '@mui/material';
import { ReactNode } from 'react';

export type ConfirmDialogProps = {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title: string;
  subtitle?: string;
  description: ReactNode;
  cancelLabel?: string;
  confirmLabel?: string;
  maxWidth?: Breakpoint | false;
  confirmIcon?: ReactNode;
  confirmColor?: 'primary' | 'error' | 'secondary' | 'success' | 'warning' | 'info';
};

export default function ConfirmDialog({
  open,
  onCancel,
  onConfirm,
  title,
  description,
  subtitle,
  maxWidth,
  cancelLabel = 'Cancelar',
  confirmLabel = 'Confirmar',
  confirmColor = 'primary',
  confirmIcon = null,
}: ConfirmDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      aria-labelledby="confirm-dialog-title"
      aria-describedby="confirm-dialog-description"
      maxWidth={maxWidth}
    >
      <DialogTitle variant="h5" id="confirm-dialog-title">
        {title}
      </DialogTitle>

      <DialogContent dividers>
        {subtitle && <Typography variant="subtitle2">{subtitle}</Typography>}
        <DialogContentText variant="body2">{description}</DialogContentText>
      </DialogContent>

      <DialogActions sx={{ px: 3, p: 2 }}>
        <Button onClick={onCancel} variant="outlined">
          {cancelLabel}
        </Button>

        <Button
          startIcon={confirmIcon}
          onClick={onConfirm}
          variant="contained"
          color={confirmColor}
          autoFocus
        >
          {confirmLabel}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
