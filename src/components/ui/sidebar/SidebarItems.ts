import BarChartIcon from '@mui/icons-material/BarChart';
import ContactMailIcon from '@mui/icons-material/ContactMail';
import GroupsIcon from '@mui/icons-material/Groups';
import SettingsIcon from '@mui/icons-material/Settings';
import PeopleIcon from '@mui/icons-material/People';
import InboxIcon from '@mui/icons-material/Inbox';

type NavItem = { label: string; href: string; icon?: typeof InboxIcon };
type Section = {
  label: string;
  icon: typeof InboxIcon;
  href?: string;
  items?: NavItem[];
};

type Group = { title: string; sections: Section[] };

export const GROUPS: Group[] = [
  {
    title: 'MÓDULOS',
    sections: [
      {
        label: 'CRM',
        icon: BarChartIcon,
        items: [
          { label: 'Leads', href: '/dashboard/crm/leads' },
          { label: 'Oportunidades', href: '/dashboard/crm/opportunities' },
        ],
      },
      {
        label: 'GesMat',
        icon: InboxIcon,
        items: [{ href: '/dashboard/gesmat/enrollments/new', label: 'Matrículas' }],
      },
      { label: 'GesPed', icon: InboxIcon, href: '/dashboard/gesped' },
    ],
  },
  {
    title: 'GESTÃO',
    sections: [
      { label: 'Equipas', icon: GroupsIcon, href: '/dashboard/teams' },
      { label: 'Perfis', icon: ContactMailIcon, href: '/dashboard/profiles' },
      { label: 'Utilizadores', icon: PeopleIcon, href: '/dashboard/users' },
      {
        label: 'Contactos',
        icon: ContactMailIcon,
        items: [
          { label: 'Pessoas', href: '/dashboard/contacts/people' },
          { label: 'Entidades', href: '/dashboard/contacts/entities' },
        ],
      },
    ],
  },
  {
    title: 'CONFIGURAÇÕES',
    sections: [
      {
        label: 'Definições',
        icon: SettingsIcon,
        items: [{ label: 'Geral', href: '/dashboard/definitions' }],
      },
      { label: 'Documentos', icon: InboxIcon, href: '/dashboard/docs' },
      { label: 'Tarefas', icon: InboxIcon, href: '/dashboard/tasks' },
      { label: 'Agenda', icon: InboxIcon, href: '/dashboard/calendar' },
    ],
  },
];

export const HREF_TAB: {
  [id: string]: { id: string; title: string };
} = {
  '/dashboard/crm/leads': {
    id: '/dashboard/crm/leads',
    //id: 'static_a9B2fL8xQz',
    title: 'Leads',
  },
  '/dashboard/crm/opportunities': {
    id: '/dashboard/crm/opportunities',
    //id: 'static_T5n7wC0yVh',
    title: 'Oportunidades',
  },
  '/dashboard/gesmat': {
    id: '/dashboard/gesmat',
    //id: 'static_pZ6sJ3mR2k',
    title: 'GesMat',
  },
  '/dashboard/gesped': {
    id: '/dashboard/gesped',
    //id: 'static_fQ8dW1uL7b',
    title: 'GesPed',
  },
  '/dashboard/teams': {
    id: '/dashboard/teams',
    //id: 'static_vX4tK9eM5r',
    title: 'Equipas',
  },
  '/dashboard/profiles': {
    id: '/dashboard/profiles',
    //id: 'static_sL2yG8cP3q',
    title: 'Perfis',
  },
  '/dashboard/users': {
    id: '/dashboard/users',
    //id: 'static_H6zN1jF0vS',
    title: 'Utilizadores',
  },
  '/dashboard/contacts/people': {
    id: '/dashboard/contacts/people',
    //id: 'static_mB3aT7kU2x',
    title: 'Pessoas',
  },
  '/dashboard/contacts/entities': {
    id: '/dashboard/contacts/entities',
    //id: 'static_rW9pE4dY1o',
    title: 'Entidades',
  },
  '/dashboard/definitions': {
    id: '/dashboard/definitions',
    //id: 'static_cJ5vS6lQ8n',
    title: 'Geral',
  },
  '/dashboard/docs': {
    id: '/dashboard/docs',
    //id: 'static_xF2uP9zR7w',
    title: 'Documentos',
  },
  '/dashboard/tasks': {
    id: '/dashboard/tasks',
    //id: 'static_D8bL3qC5yK',
    title: 'Tarefas',
  },
  '/dashboard/calendar': {
    id: '/dashboard/calendar',
    //id: 'static_tV7nM1jX4h',
    title: 'Agenda',
  },
};
