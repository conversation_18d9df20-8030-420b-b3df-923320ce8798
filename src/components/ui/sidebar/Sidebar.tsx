'use client';

import {
  <PERSON>,
  <PERSON>Subheader,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Typography,
  Tooltip,
  Menu,
  MenuItem,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { GROUPS } from './SidebarItems';
import { BOOL } from '@/constants/enums';
import { useTabsMethods } from '@/hooks/useTabs';

export default function SidebarNav({ mini = false }: { mini?: boolean }) {
  const pathname = usePathname();
  const cleanPath = pathname.replace(/^\/[a-z]{2}(?:-[A-Z]{2})?/, '');
  const [openSection, setOpenSection] = useState<string | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [submenuItems, setSubmenuItems] = useState<
    (typeof GROUPS)[number]['sections'][number]['items'] | null
  >(null);
  const { handleChangeTab } = useTabsMethods();

  const toggle = (key: string) => setOpenSection(openSection === key ? null : key);

  return (
    <>
      <List
        component="nav"
        sx={(t) => ({
          px: 1,
          width: mini ? t.shape.drawerMini : t.shape.drawerWidth,
        })}
      >
        {GROUPS.map((group) => (
          <div key={group.title}>
            {!mini && (
              <ListSubheader
                disableSticky
                sx={(t) => ({
                  mt: t.spacing(3),
                  mb: t.spacing(1),
                  px: 2,
                })}
              >
                <Typography variant="overline" color="text.secondary">
                  {group.title}
                </Typography>
              </ListSubheader>
            )}

            {group.sections.map((sec) => {
              const parentActive =
                (sec.href && cleanPath.startsWith(sec.href)) ||
                (sec.items && sec.items.some((it) => cleanPath.startsWith(it.href)));
              if (mini) {
                /* Se a seção tem sub-itens, abre um menu. Senão navega direto. */
                if (sec.items) {
                  return (
                    <Tooltip key={sec.label} title={sec.label} placement="right">
                      <ListItemButton
                        selected={parentActive}
                        onClick={(e) => {
                          setMenuAnchorEl(e.currentTarget);
                          setSubmenuItems(sec.items!);
                        }}
                        sx={{ justifyContent: 'center', py: 1.5 }}
                      >
                        <sec.icon fontSize="small" />
                      </ListItemButton>
                    </Tooltip>
                  );
                }

                const goto = sec.href ?? '#';
                return (
                  <Tooltip key={sec.label} title={sec.label} placement="right">
                    <ListItemButton
                      selected={parentActive}
                      onClick={() => handleChangeTab({ id: goto, isStatic: BOOL.TRUE })}
                      sx={{ justifyContent: 'center', py: 1.5 }}
                    >
                      <sec.icon fontSize="small" />
                    </ListItemButton>
                  </Tooltip>
                );
              }

              if (sec.items) {
                return (
                  <div key={sec.label}>
                    <ListItemButton
                      selected={parentActive}
                      onClick={() => toggle(sec.label)}
                      sx={(t) => ({
                        mx: 2,
                        minHeight: 48,
                        '&.Mui-selected': {
                          backgroundColor: t.palette.action.selected,
                        },
                      })}
                    >
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <sec.icon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={sec.label} />
                      <ExpandMoreIcon
                        sx={{
                          transform: openSection === sec.label ? 'rotate(180deg)' : 'rotate(0deg)',
                          transition: '0.2s',
                        }}
                      />
                    </ListItemButton>

                    <Collapse in={openSection === sec.label} timeout="auto" unmountOnExit>
                      {sec.items?.map((item) => {
                        const active = cleanPath === item.href;
                        return (
                          <ListItemButton
                            key={item.href}
                            selected={active}
                            onClick={() => handleChangeTab({ id: item.href, isStatic: BOOL.TRUE })}
                            sx={(t) => ({
                              ml: 6,
                              mr: 2,
                              my: 1,
                              '&.Mui-selected, &:hover': {
                                backgroundColor: t.palette.action.hover,
                              },
                            })}
                          >
                            <ListItemText primary={item.label} />
                          </ListItemButton>
                        );
                      })}
                    </Collapse>
                  </div>
                );
              }
              return (
                <ListItemButton
                  key={sec.label}
                  selected={parentActive}
                  onClick={() => handleChangeTab({ id: sec.href!, isStatic: BOOL.TRUE })}
                  sx={(t) => ({
                    mx: 2,
                    minHeight: 48,
                    '&.Mui-selected': { backgroundColor: t.palette.action.selected },
                  })}
                >
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <sec.icon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={sec.label} />
                </ListItemButton>
              );
            })}
          </div>
        ))}
      </List>
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        anchorOrigin={{ horizontal: 'right', vertical: 'top' }}
        transformOrigin={{ horizontal: 'left', vertical: 'top' }}
        slotProps={{ paper: { sx: (theme) => ({ px: 1, ml: theme.spacing(1) }) } }}
      >
        {submenuItems?.map((item) => (
          <MenuItem
            key={item.href}
            selected={cleanPath === item.href}
            onClick={() => {
              handleChangeTab({ id: item.href, isStatic: BOOL.TRUE });
              setMenuAnchorEl(null);
            }}
            sx={{ minWidth: 200 }}
          >
            {item.label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
}