'use client';

import { Chip } from '@mui/material';
import { styled } from '@mui/material/styles';

export type Status =
  | 'new'
  | 'assigned'
  | 'Fechada c/ sucesso'
  | 'Fechada s/ sucesso'
  | 'cancelled'
  | 'Em qualificação'
  | 'Em validação'
  | 'P2+3B'
  | 'P1'
  | 'F. Up – Quente'
  | 'F. Up – Morno'
  | 'F. Up – Frio'
  | 'active'
  | 'inactive';

interface StatusConfig {
  label: string;
  bg: string;
  color: string;
}

const STATUS_MAP: Record<Status, StatusConfig> = {
  // Lead statuses
  new: { label: 'Nova', bg: '#E5E1FA', color: '#5E3DC7' },
  assigned: { label: 'Atribuída', bg: '#D9FBFA', color: '#2C9EA8' },
  'Fechada c/ sucesso': { label: 'Fechada c/ sucesso', bg: '#DDF9E7', color: '#218D46' },
  'Fechada s/ sucesso': { label: '<PERSON>chada s/ sucesso', bg: '#FDE5E5', color: '#D32F2F' },
  cancelled: { label: 'Anulada', bg: '#F0F0F0', color: '#6E6E6E' },
  'Em qualificação': { label: 'Em qualificação', bg: '#FDE8F0', color: '#C2185B' },
  'Em validação': { label: 'Em validação', bg: '#E3F3FF', color: '#1976D2' },

  // Opportunity statuses
  'P2+3B': { label: 'P2 + 3B', bg: '#E0BBE4', color: '#4A148C' },
  P1: { label: 'P1', bg: '#A5F3FC', color: '#0C4A6E' },
  'F. Up – Quente': { label: 'F. Up – Quente', bg: '#E5E5E5', color: '#374151' },
  'F. Up – Morno': { label: 'F. Up – Morno', bg: '#FBCFE8', color: '#831843' },
  'F. Up – Frio': { label: 'F. Up – Frio', bg: '#BFDBFE', color: '#1E3A8A' },

  // User statuses
  active: { label: 'Ativo', bg: '#E8F5E9', color: '#1B5E20' },
  inactive: { label: 'Inativo', bg: '#FEEBEE', color: '#B71C1C' },
};

const StyledChip = styled(Chip)<{ status: Status }>(({ status }) => {
  const cfg = STATUS_MAP[status];
  return {
    backgroundColor: cfg?.bg,
    color: cfg?.color,
    fontWeight: 500,
    textTransform: 'none',
    height: 24,
    fontSize: '0.75rem',
    borderRadius: 12,
  };
});

export interface StatusChipProps {
  status: Status;
}

export default function StatusChip({ status }: StatusChipProps) {
  const cfg = STATUS_MAP[status];

  return status ? <StyledChip status={status} label={cfg?.label || status} /> : ' - ';
}
