import DatePicker from 'react-datepicker';
import { DatePickerInput } from './DatePickerInput';
import { useMemo } from 'react';

type MuiStyledDatePickerProps = {
  value: Date | null | [Date | null, Date | null];
  onChange: (date: Date | null | [Date | null, Date | null]) => void;
  selectsRange?: boolean;
  placeholder?: string;
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
};

export function MuiStyledDatePicker({
  value,
  onChange,
  selectsRange = false,
  placeholder = 'Selecione a data',
  minDate,
  maxDate,
  disabled,
}: MuiStyledDatePickerProps) {
  // For range, value should be [start, end], for single, just Date|null
  const startDate = selectsRange && Array.isArray(value) ? value[0] : (value as Date | null);
  const endDate = selectsRange && Array.isArray(value) ? value[1] : null;

  // Memoize years for performance
  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 30 }, (_, i) => currentYear - 15 + i);
  }, []);

  const months = useMemo(
    () => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    []
  );

  return (
    // @ts-expect-error: react-datepicker types are not fully compatible with our usage, but works at runtime
    <DatePicker
      {...(selectsRange ? { selectsRange: true } : {})}
      selected={selectsRange ? undefined : startDate}
      startDate={startDate}
      endDate={endDate}
      onChange={onChange}
      dateFormat="dd/MM/yyyy"
      isClearable
      minDate={minDate}
      maxDate={maxDate}
      disabled={disabled}
      placeholderText={placeholder}
      customInput={<DatePickerInput placeholder={placeholder} />}
      renderCustomHeader={({
        date,
        changeYear,
        changeMonth,
        decreaseMonth,
        increaseMonth,
        prevMonthButtonDisabled,
        nextMonthButtonDisabled,
      }) => (
        <div className="react-datepicker__custom-header">
          <button onClick={decreaseMonth} disabled={prevMonthButtonDisabled}>
            {'<'}
          </button>
          <select
            value={date.getFullYear()}
            onChange={({ target: { value } }) => changeYear(Number(value))}
          >
            {years.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          <select
            value={date.getMonth()}
            onChange={({ target: { value } }) => changeMonth(Number(value))}
          >
            {months.map((month, idx) => (
              <option key={month} value={idx}>
                {month}
              </option>
            ))}
          </select>
          <button onClick={increaseMonth} disabled={nextMonthButtonDisabled}>
            {'>'}
          </button>
        </div>
      )}
    />
  );
}
