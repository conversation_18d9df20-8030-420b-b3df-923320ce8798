'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  IconButton,
  Menu,
  MenuItem,
  TablePagination,
  TableFooter,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  ContentCopy as ContentCopyIcon,
  Delete as DeleteIcon,
  RemoveRedEyeRounded as VisibilityIcon,
} from '@mui/icons-material';
import { useState } from 'react';

type Action = {
  label: string;
  icon: 'edit' | 'copy' | 'delete' | 'view';
  onClick: (id: string) => void;
  color?: 'error';
};

export type ColumnDef<T> = {
  key: keyof T;
  label: string;
  align?: 'left' | 'right' | 'center';
  width?: number;
  render?: (value: any, row: T) => React.ReactNode;
};

type TableComponentProps<T extends { id: string }> = {
  rows: T[];
  columns?: readonly ColumnDef<T>[];
  actions?: Action[];
  pagination?: boolean;
  rowsPerPageOptionsLabel?: string;
  labelRowsPerPage?: string;
};

export default function TableGeneric<T extends { id: string }>({
  rows,
  columns,
  actions,
  pagination = false,
  rowsPerPageOptionsLabel = 'default',
  labelRowsPerPage = 'Linhas por página:',
}: TableComponentProps<T>) {
  const rowsPerPageOptions = { default: [10, 25, 50] };
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [menuRowId, setMenuRowId] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(
    rowsPerPageOptions[rowsPerPageOptionsLabel as keyof typeof rowsPerPageOptions][0]
  );

  const openMenu = Boolean(anchorEl);

  const handleMenu = (e: React.MouseEvent<HTMLElement>, id: string) => {
    setAnchorEl(e.currentTarget);
    setMenuRowId(id);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setMenuRowId(null);
  };

  const getIcon = (icon: string, color?: string) => {
    switch (icon) {
      case 'edit':
        return <EditIcon fontSize="small" sx={{ mr: 1 }} />;
      case 'copy':
        return <ContentCopyIcon fontSize="small" sx={{ mr: 1 }} />;
      case 'delete':
        return (
          <DeleteIcon
            fontSize="small"
            sx={{ mr: 1 }}
            color={color === 'error' ? 'error' : undefined}
          />
        );
      case 'view':
        return <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />;
      default:
        return null;
    }
  };

  const paginatedRows = pagination
    ? rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : rows;

  return (
    <>
      <Table size="medium">
        <TableHead>
          <TableRow>
            {(columns ?? []).map((col) => (
              <TableCell key={String(col.key)} align={col.align ?? 'left'} width={col.width}>
                {col.label}
              </TableCell>
            ))}
            {actions && actions.length > 0 && <TableCell align="right" width={56} />}
          </TableRow>
        </TableHead>
        <TableBody>
          {paginatedRows.map((row) => (
            <TableRow key={row.id} hover>
              {(columns ?? []).map((col) => {
                const value = row[col.key];
                if (col.render) {
                  return (
                    <TableCell key={String(col.key)} align={col.align ?? 'left'}>
                      {col.render(value, row)}
                    </TableCell>
                  );
                }
                return (
                  <TableCell key={String(col.key)} align={col.align ?? 'left'}>
                    {String(value)}
                  </TableCell>
                );
              })}
              {actions && actions.length > 0 && (
                <TableCell align="right">
                  <IconButton size="small" onClick={(e) => handleMenu(e, row.id)}>
                    <MoreVertIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
        {pagination && (
          <TableFooter>
            <TableRow>
              <TablePagination
                count={rows.length}
                page={page}
                onPageChange={(_, p) => setPage(p)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => {
                  setRowsPerPage(+e.target.value);
                  setPage(0);
                }}
                rowsPerPageOptions={
                  rowsPerPageOptions[rowsPerPageOptionsLabel as keyof typeof rowsPerPageOptions]
                }
                labelRowsPerPage={labelRowsPerPage}
              />
            </TableRow>
          </TableFooter>
        )}
      </Table>
      {actions && actions.length > 0 && (
        <Menu anchorEl={anchorEl} open={openMenu} onClose={handleCloseMenu}>
          {actions.map((action) => (
            <MenuItem
              key={action.label}
              onClick={() => {
                if (menuRowId) action.onClick(menuRowId);
                handleCloseMenu();
              }}
              sx={action.color ? { color: `${action.color}.main` } : undefined}
            >
              {getIcon(action.icon, action.color)} {action.label}
            </MenuItem>
          ))}
        </Menu>
      )}
    </>
  );
}
