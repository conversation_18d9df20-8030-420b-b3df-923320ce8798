'use client';

import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ArrowDropDown from '@mui/icons-material/ArrowDropDown';
import { Box, Button, ButtonGroup, Fade } from '@mui/material';
import { muiTheme } from '@/styles/theme';
import { createPortal } from 'react-dom';

// TODO
// allow option to menu to work independly, deciding its own direction
// atm only working properly for footer

export type ColorType =
  | 'primary'
  | 'secondary'
  | 'error'
  | 'info'
  | 'success'
  | 'warning'
  | 'defaultIcon'
  | 'text';

const getColor = (color?: ColorType) => {
  switch (color) {
    case 'primary':
      return muiTheme.palette.primary.main;
    case 'success':
      return muiTheme.palette.success.main;
    case 'error':
      return muiTheme.palette.error.main;
    case 'defaultIcon':
      return muiTheme.palette.icon.primary;
    default:
      return muiTheme.palette.text.primary;
  }
};

type DropdownProps = {
  readonly children: React.ReactNode;
  readonly label?: string;
  readonly dropdownId?: string;
  readonly containerId?: string;
  readonly yDirection?: 'up' | 'down';
  readonly xDirection?: 'left' | 'right';
  readonly usePortal?: boolean;
  readonly CustomButton?: React.ComponentType<{ id: string; onClick: () => void }>;
};

// with usePortal, dont use x and y direction, it will calculate its own position based on the boundingRectId and the space on the page
export default function Dropdown({
  children,
  label = 'Options',
  dropdownId = 'split-menu-dropdown',
  containerId = 'split-menu-container',
  yDirection = 'down',
  xDirection = 'right',
  CustomButton = undefined,
  usePortal,
}: DropdownProps) {
  const dropdownRef = useRef<{
    toggle: () => void;
    toggleOpen: () => void;
    close: () => void;
    open: () => void;
  }>(null);

  const handleButtonClick = () => {
    dropdownRef.current?.toggleOpen();
  };

  return (
    <Box sx={{ display: 'inline-block', position: 'relative' }}>
      {CustomButton ? (
        <CustomButton id={containerId} onClick={handleButtonClick} />
      ) : (
        <ButtonGroup
          id={containerId}
          onClick={handleButtonClick}
          variant="contained"
          disableElevation
        >
          <Button>{label}</Button>
          <Button color="primary">
            <ArrowDropDown fontSize="small" />
          </Button>
        </ButtonGroup>
      )}
      <DropdownOptionsWrapper
        ref={dropdownRef}
        id={dropdownId}
        boundingRectId={containerId}
        yDirection={yDirection}
        xDirection={xDirection}
        usePortal={usePortal}
      >
        {children}
      </DropdownOptionsWrapper>
    </Box>
  );
}

type DropdownOptionsProps = {
  children: React.ReactNode;
  id?: string;
  boundingRectId: string;
  open: boolean;
  onClose?: () => void;
  yDirection?: 'up' | 'down';
  xDirection?: 'left' | 'right';
  width?: number;
  usePortal?: boolean;
};

/*missing autocalculate place to open
 * yDirection is the direction for the menu to open relative
 * xdirection is the direction for the menu to open relative to the anchor element
 * boundingrectid is the id assigned to the element that will be used to calculate the position of the menu, html element simple calculation
 * rest is selfexplanatory
 * */
export const DropdownOptions = ({
  children,
  id = undefined,
  boundingRectId,
  open,
  onClose,
  yDirection = 'down',
  xDirection = 'right',
  width,
  usePortal = false,
}: DropdownOptionsProps) => {
  const [menuPosition, setMenuPosition] = useState<{
    top: number | undefined;
    left: number | undefined;
  } | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  // needs to be outside to calculate positioning when viewport changes
  const viewportWidth = typeof window !== 'undefined' ? window.innerWidth : 0;
  const viewportHeight = typeof window !== 'undefined' ? window.innerHeight : 0;
  useEffect(() => {
    if (!open) return;
    function handleClick(event: MouseEvent) {
      const menuEl = menuRef.current;
      const anchorEl = document.getElementById(boundingRectId);
      const target = event.target;
      if (
        menuEl &&
        anchorEl &&
        target instanceof Node &&
        !menuEl.contains(target) &&
        !anchorEl.contains(target)
      ) {
        onClose?.();
      }
    }
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open, boundingRectId, onClose]);

  // disable scroll funcitonality when menu is open
  useEffect(() => {
    function preventScroll(e: WheelEvent) {
      if (open) {
        e.preventDefault();
      }
    }
    if (open) {
      window.addEventListener('wheel', preventScroll, { passive: false });
    }
    return () => {
      window.removeEventListener('wheel', preventScroll);
    };
  }, [open]);
  // uses portal to calculate its own positioning, works great for tables
  // maybe in the future use this logic for all menus, but for now, only for portals
  useLayoutEffect(() => {
    if (!usePortal || !open) return;
    console.log('usePortal is true, calculating position');
    const anchor = document.getElementById(boundingRectId);
    const menuEl = menuRef.current;
    const anchorRect = anchor?.getBoundingClientRect();
    const menuRect = menuEl?.getBoundingClientRect();

    if (anchorRect && menuRect) {
      // this one scrolleft is untested bc no page has side scroll atm, but should work
      // minus 40 is safe margin to avoid going out of viewport, can be adjusted
      const left =
        viewportWidth - 40 < anchorRect.left + menuRect.width
          ? anchorRect.right - menuRect.width
          : anchorRect.left;
      const scrollTop = window.scrollY;
      const top =
        viewportHeight - 40 < anchorRect.bottom + menuRect.height
          ? anchorRect.top - menuRect.height + scrollTop
          : anchorRect.bottom + scrollTop;
      setMenuPosition({ left, top });
    }
  }, [open, boundingRectId, viewportHeight, viewportWidth]);

  // doesnt use portal, manually set positioning, works great on footer and static buttons
  useLayoutEffect(() => {
    if (usePortal) return;
    const anchor = document.getElementById(boundingRectId);
    const menuEl = menuRef.current;
    const anchorRect = anchor?.getBoundingClientRect();
    const menuRect = menuEl?.getBoundingClientRect();
    if (!open || !boundingRectId || menuPosition?.top || menuPosition?.left) {
      // setMenuPosition(null);
      return;
    }
    if (anchorRect && menuRect) {
      console.log('entering here but fucking up location');
      const { left: leftA, right: rightA, bottom: bottomA, top: topA } = anchorRect;
      const { left: leftM, right: rightM, bottom: bottomM, top: topM } = menuRect;
      let top;
      let left;
      if (yDirection === 'up') top = bottomA - bottomM - 6;
      if (yDirection === 'down') top = topM - topA;
      if (xDirection === 'left') left = rightA - rightM - 2;
      if (xDirection === 'right') left = leftA - leftM + 2;
      setMenuPosition({ top, left });
    }
  }, [open, boundingRectId, yDirection, xDirection]);

  return (
    <Fade in={open} timeout={320} unmountOnExit>
      <Box
        ref={menuRef}
        id={id}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          p: '8px 0',
          gap: '4px',
          borderRadius: '8px',
          width: width ?? 'fit-content',
          bgcolor: 'white',
          boxShadow: '0 2px 8px rgba(0,0,0,0.35)',
          position: 'absolute',
          zIndex: 20,
          ...(menuPosition
            ? {
                top: menuPosition.top,
                left: menuPosition.left,
              }
            : {}),
        }}
      >
        {children}
      </Box>
    </Fade>
  );
};

type DropdownOptionsWrapperProps = {
  children: React.ReactNode;
  id?: string;
  boundingRectId: string;
  yDirection?: 'up' | 'down';
  xDirection?: 'left' | 'right';
  usePortal?: boolean;
};

const DropdownOptionsWrapper = forwardRef<
  {
    toggle: () => void;
    toggleOpen: () => void;
    close: () => void;
    open: () => void;
  },
  DropdownOptionsWrapperProps
>(function DropdownOptionsWrapper({ children, id, boundingRectId, usePortal, ...rest }, ref) {
  const [open, setOpen] = useState(false);

  // Expose toggle method via ref
  useImperativeHandle(ref, () => ({
    toggle: () => setOpen((prev) => !prev),
    toggleOpen: () => !open && setOpen(true),
    close: () => setOpen(false),
    open: () => setOpen(true),
  }));

  useEffect(() => {
    console.log('Dropdown options mounted with id:', open);
  }, [open]);

  const dropdownOptionsElement = (
    <DropdownOptions
      id={id}
      boundingRectId={boundingRectId}
      open={open}
      onClose={() => setOpen(false)}
      {...rest}
      usePortal={usePortal}
    >
      {children}
    </DropdownOptions>
  );

  if (usePortal && typeof document !== 'undefined') {
    return createPortal(dropdownOptionsElement, document.body);
  }
  return dropdownOptionsElement;
});

type OptionProps = {
  readonly label: string;
  readonly icon?: React.ElementType;
  //readonly children: React.ReactNode;
  readonly onClick?: React.MouseEventHandler<HTMLButtonElement>;
  readonly disabled?: boolean;
  readonly color?: ColorType;
  readonly iconColor?: ColorType;
  readonly [key: string]: any;
};

function Option({
  label,
  icon: Icon,
  onClick,
  disabled = false,
  color,
  iconColor = 'defaultIcon',
  ...props
}: OptionProps) {
  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      sx={{
        fontSize: '1rem',
        fontWeight: 400,
        p: '0 16px',
        height: 48,
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        borderRadius: '0px',
        justifyContent: 'flex-start',
        cursor: disabled ? 'default' : 'pointer',
        gap: '10px',
        flexShrink: 0,
        color: getColor(color),
        opacity: disabled ? 0.5 : 1,
        pointerEvents: disabled ? 'none' : 'auto',
        bgcolor: 'transparent',
        '&:hover': {
          bgcolor: '#eee',
        },
        '&:active': {
          bgcolor: '#ccc',
        },
      }}
      variant="text"
      disableRipple
      disableElevation
      {...props}
    >
      <Box
        sx={{
          height: 24,
          width: 24,
          flexShrink: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {Icon && (
          <Icon
            style={{
              height: 24,
              width: 24,
              flexShrink: 0,
              pointerEvents: 'none',
              fill: getColor(iconColor),
            }}
          />
        )}
      </Box>
      <Box
        sx={{
          flexGrow: 1,
          pointerEvents: 'none',
          whiteSpace: 'nowrap',
          textAlign: 'left',
        }}
      >
        {label}
      </Box>
    </Button>
  );
}

const Divider = () => {
  return (
    <Box
      sx={{
        height: '1px',
        width: '100%',
        bgcolor: '#eee',
        flexShrink: 0,
      }}
    />
  );
};

const Label = ({ label, icon: Icon, iconColor = 'defaultIcon', color }: OptionProps) => {
  return (
    <Box
      sx={{
        fontSize: '1rem',
        fontWeight: 400,
        display: 'flex',
        width: '100%',
        alignItems: 'center',
        p: '0px 16px',
        gap: '10px',
        color: getColor(color),
        height: 48,
        pointerEvents: 'none',
      }}
    >
      <Box
        sx={{
          height: 24,
          width: 24,
          flexShrink: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {Icon && (
          <Icon
            style={{
              height: 24,
              width: 24,
              flexShrink: 0,
              pointerEvents: 'none',

              fill: getColor(iconColor),
            }}
          />
        )}
      </Box>
      {label}
    </Box>
  );
};

type SubOptionsProps = {
  label: React.ReactNode;
  children: React.ReactNode;
  xDirection?: 'left' | 'right';
  disabled?: boolean;
  color?: string;
  yBottomPoint?: 'menu' | 'option';
  width?: number;
  [key: string]: any;
};

type SubOptionsRef = {
  open: () => void;
  close: () => void;
  toggle: () => void;
  isOpen: () => boolean;
};

// missing autocalculate place to open
// xdirection is the direction for the submenu to open relative to the parent Element
// ybottom tells the submenu it will have a mininum point to start
// yBottomPoint tells the submenu where to calculate the y position from, if nothing is passed, it will use the parent menu, if option is passed, it will use the option button as reference
// rest is selfexplanatory
const SubOptions = forwardRef<SubOptionsRef, SubOptionsProps>(
  (
    {
      label,
      children,
      xDirection = 'right',
      disabled = false,
      color,
      yBottomPoint = undefined,
      width = 200,
      ...props
    },
    ref
  ) => {
    const [open, setOpen] = useState(false);
    const subMenuRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [subMenuTop, setSubMenuTop] = useState<number | null>(null);

    // expose open/close/toggle methods via ref if needed
    useImperativeHandle(
      ref,
      () => ({
        open: () => setOpen(true),
        close: () => setOpen(false),
        toggle: () => setOpen((prev) => !prev),
        isOpen: () => open,
      }),
      [open]
    );

    // useeffects is the best thing ever marcos
    useEffect(() => {
      if (!open || !yBottomPoint) {
        setSubMenuTop(0);
        return;
      }
      const submenu = subMenuRef.current;
      let parentMenu = null;
      if (buttonRef.current) {
        parentMenu =
          yBottomPoint === 'menu'
            ? buttonRef.current.parentElement?.parentElement
            : buttonRef.current?.parentElement;
      }
      if (parentMenu && submenu) {
        const parentRect = parentMenu.getBoundingClientRect();
        const submenuRect = submenu.getBoundingClientRect();
        const overflow = submenuRect.bottom - parentRect.bottom;
        if (overflow > 0) {
          setSubMenuTop(-overflow);
        } else {
          setSubMenuTop(0);
        }
      }
    }, [open]);
    // close submenu when focus leaves both button and submenu
    const handleBlur = (event: React.FocusEvent) => {
      if (
        !buttonRef.current?.contains(event.relatedTarget as Node) &&
        !subMenuRef.current?.contains(event.relatedTarget as Node)
      ) {
        setOpen(false);
      }
    };

    // open submenu on mouse enter, close on mouse leave
    const handleMouseEnter = () => {
      if (!disabled) setOpen(true);
    };
    const handleMouseLeave = () => {
      if (!disabled) setOpen(false);
    };

    return (
      <Box
        sx={{ position: 'relative' }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <Button
          ref={buttonRef}
          disabled={disabled}
          onClick={() => setOpen((prev) => !prev)}
          onBlur={handleBlur}
          sx={{
            fontSize: '1rem',
            fontWeight: 400,
            p: '0 16px',
            height: 48,
            width: '100%',
            borderRadius: '0px',
            display: 'flex',
            gap: '10px',
            alignItems: 'center',
            justifyContent: 'start',
            cursor: disabled ? 'default' : 'pointer',
            flexShrink: 0,
            color: getColor(color),
            opacity: disabled ? 0.5 : 1,
            pointerEvents: disabled ? 'none' : 'auto',
            bgcolor: open ? '#f5f5f5' : 'transparent', // selected color when submenu is open
            '&:hover': {
              bgcolor: '#eee',
            },
            '&:active': {
              bgcolor: '#ccc',
            },
          }}
          variant="text"
          disableRipple
          disableElevation
          {...props}
        >
          <Box
            sx={{
              height: 24,
              width: 24,
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {xDirection === 'right' && (
              <ArrowForwardIosIcon
                style={{
                  height: 16,
                  width: 16,
                  flexShrink: 0,
                  pointerEvents: 'none',
                  fill: getColor('defaultIcon'),
                }}
              />
            )}
            {xDirection === 'left' && (
              <ArrowBackIosNewIcon
                style={{
                  height: 16,
                  width: 16,
                  flexShrink: 0,
                  pointerEvents: 'none',
                  fill: getColor('defaultIcon'),
                }}
              />
            )}
          </Box>
          {label}
        </Button>
        <Fade in={open} timeout={320} unmountOnExit>
          <Box
            ref={subMenuRef}
            tabIndex={-1}
            sx={{
              position: 'absolute',
              p: '8px 0px',
              top: subMenuTop,
              width: width,
              gap: '4px',
              left: xDirection === 'right' ? '100%' : undefined,
              right: xDirection === 'left' ? '100%' : undefined,
              bgcolor: '#fff',
              boxShadow: '0 2px 8px rgba(0,0,0,0.35)',
              borderRadius: '8px 0',
              zIndex: 20,
              display: open ? 'flex' : 'none',
              flexDirection: 'column',
            }}
            onBlur={handleBlur}
          >
            {children}
          </Box>
        </Fade>
      </Box>
    );
  }
);

DropdownOptions.Option = Option;
DropdownOptions.Divider = Divider;
DropdownOptions.Label = Label;
DropdownOptions.SubOptions = SubOptions;