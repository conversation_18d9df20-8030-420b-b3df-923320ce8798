'use client';

import { Card, CardContent, Tab, Tabs } from '@mui/material';
import { identity } from 'lodash';
import { ReactNode, useMemo } from 'react';

export interface SectionCardInternalProps {
  headerColor?: string;
}

interface SectionCardLayoutProps {
  children: ReactNode;
  addSpacer?: boolean;
}

interface SectionCardProps extends SectionCardInternalProps, SectionCardLayoutProps {
  title: string;
}

export type SectionTab = { label: string; value: number | string; disabled?: boolean };

type InternalTabsProps<T extends string | number> = {
  headerColor?: string;
  tabs: SectionTab[];
  selectedTab: T;
  onSelectTab: (value: T) => any;
};

export function InternalTabs<T extends string | number>({
  headerColor,
  tabs,
  selectedTab,
  onSelectTab,
}: InternalTabsProps<T>) {
  return (
    <Tabs
      value={selectedTab}
      onChange={(_, val) => onSelectTab(val)}
      sx={{
        borderBottom: 1,
        mb: 2,
        borderColor: headerColor ?? 'divider',
        width: '100%',
      }}
      slotProps={{
        indicator: {
          sx: { mt: 1 },
        },
      }}
    >
      {tabs.map((tab) => (
        <Tab
          key={tab.value}
          sx={{ textTransform: 'none' }}
          disabled={tab.disabled}
          label={tab.label}
          value={tab.value}
        />
      ))}
    </Tabs>
  );
}

//addSpacer is necessary when you are on a page that includes footer actions
export function SectionCardLayout({ children, addSpacer = false }: SectionCardLayoutProps) {
  return (
    <Card sx={{ width: '100%', marginBottom: addSpacer ? '72px' : undefined }}>
      <CardContent
        sx={{
          py: 2,
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'column',
          gap: '1rem',
          width: 'inherit',
        }}
      >
        {children}
      </CardContent>
    </Card>
  );
}

export function SectionCardInternal<T extends string | number>({
  addSpacer,
  children,
  ...rest
}: SectionCardLayoutProps & InternalTabsProps<T>) {
  return (
    <SectionCardLayout addSpacer={addSpacer}>
      <InternalTabs {...rest} />
      {children}
    </SectionCardLayout>
  );
}

export default function SectionCard({ title, children, addSpacer }: SectionCardProps) {
  const tabs = useMemo<SectionTab[]>(() => [{ label: title, value: 0 }], [title]);

  return (
    <SectionCardLayout addSpacer={addSpacer}>
      <InternalTabs tabs={tabs} selectedTab={0} onSelectTab={identity} />
      {children}
    </SectionCardLayout>
  );
}
