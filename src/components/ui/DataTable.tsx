'use client';

import { useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
  ColumnDef,
  VisibilityState,
  PaginationState,
} from '@tanstack/react-table';
import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Menu,
  MenuItem,
  Checkbox,
  TextField,
  Button,
  Box,
  Stack,
  TablePagination,
  TableContainer,
  Paper,
  Tabs,
  Tab,
  InputAdornment,
} from '@mui/material';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import { Clear, KeyboardArrowDownRounded, SearchRounded } from '@mui/icons-material';
import { MuiStyledDatePicker } from './MuiStyledDatePicker';
import { TABNAV } from '@/constants/enums';
import { FilterMeta } from '@/app/[locale]/(protected)/dashboard/crm/leads/components/types';

export const defaultPaginationParams: PaginationState = {
  pageIndex: 0,
  pageSize: 10,
};

const rowsPerPageOptions = [5, 10, 25, 50];

/**
 * Reusable MUI + TanStack table component
 */

export interface DataTableProps<T extends object> {
  data: T[];
  total?: number;
  columns: ColumnDef<T, unknown>[];
  initialVisibility?: VisibilityState;
  enableFilters?: boolean;
  pagination?: PaginationState;
  onRowClick?: (row: T) => void;
  onPaginationChange?: (pagination: PaginationState) => void;
  removePaginationOnLowResults?: boolean;
  showTabs?: boolean;
  pageType?: string;
}

export default function DataTable<T extends object>({
  data,
  total,
  columns,
  initialVisibility = {},
  enableFilters = true,
  onRowClick,
  pagination = defaultPaginationParams,
  showTabs = false,
  onPaginationChange,
  removePaginationOnLowResults = false,
  pageType,
}: DataTableProps<T>) {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(initialVisibility);

  const [columnFilters, setColumnFilters] = useState<
    Record<string, string | [Date | null, Date | null]>
  >({});
  const statusFilter = columnFilters['status'] ?? '';
  const anyFilterActive = Object.values(columnFilters).some((v) => !!v);

  const table = useReactTable({
    data,
    rowCount: total,
    pageCount: Math.ceil((total ?? data.length) / pagination.pageSize),
    columns,
    manualPagination: true,
    state: {
      columnVisibility,
      columnFilters: Object.entries(columnFilters).map(([id, value]) => ({
        id,
        value,
      })),
      pagination,
    },
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: (next) => {
      if (typeof next === 'function') {
        throw new Error('wtf');
      }

      onPaginationChange?.(next);
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const toggleColumn = (id: string) =>
    setColumnVisibility((s) => {
      const currentlyVisible = s[id] ?? true;
      return { ...s, [id]: !currentlyVisible };
    });

  const HEADER_H = 52;

  const commonInputProps = {
    autoCorrect: 'off',
    spellCheck: false,
  };

  return (
    <Stack sx={{ overflow: 'visible', width: '100%' }} gap={2}>
      {showTabs && (
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Tabs
            value={statusFilter === 'fechadas' ? 1 : 0}
            textColor="primary"
            indicatorColor="primary"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              minHeight: 36,
              '& .MuiTab-root': { minHeight: 36, px: 2 },
            }}
          >
            <Tab label={(pageType === TABNAV.PEOPLE && 'Clientes') || 'Abertas'} />
            <Tab label={(pageType === TABNAV.PEOPLE && 'Não clientes') || 'Fechadas'} />
          </Tabs>

          <Box display="flex" gap={1} alignItems="center">
            {anyFilterActive && (
              <Button startIcon={<Clear />} size="small" onClick={() => setColumnFilters({})}>
                Limpar filtros
              </Button>
            )}
            <Button
              startIcon={<ViewColumnIcon />}
              endIcon={<KeyboardArrowDownRounded />}
              variant="outlined"
              onClick={(e) => setAnchorEl(e.currentTarget)}
            >
              Colunas
            </Button>
            <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={() => setAnchorEl(null)}>
              {table
                .getAllLeafColumns()
                .filter((c) => c.columnDef.enableHiding !== false)
                .map((col) => (
                  <MenuItem
                    key={col.id}
                    disableGutters
                    onClick={() => {
                      toggleColumn(col.id);
                    }}
                  >
                    <Checkbox size="small" checked={col.getIsVisible()} />
                    {col.columnDef.header as string}
                  </MenuItem>
                ))}
            </Menu>
          </Box>
        </Box>
      )}
      <Box
        sx={{
          width: 'inherit',
          position: 'relative',
        }}
      >
        <Paper
          sx={(theme) => ({
            borderRadius: 1,
            border: 1,
            borderColor: 'divider',
            overflow: 'hidden',
            width: '100%',
            [theme.breakpoints.down(1175.98)]: {
              maxWidth: '100%',
            },
            [theme.breakpoints.up(1176)]: {
              maxWidth: 'none',
            },
          })}
          elevation={1}
        >
          <TableContainer
            sx={{
              maxHeight: 628,
              overflow: 'auto',
            }}
          >
            <Table
              stickyHeader
              size="small"
              sx={{
                minWidth: 1176,
                tableLayout: 'fixed',
                '& .sticky-actions': {
                  position: 'sticky',
                  right: 0,
                  backgroundColor: (t) => t.palette.background.paper,
                  zIndex: 2,
                },
                ...(onRowClick && {
                  'tbody tr:hover td.sticky-actions': {
                    backgroundColor: (t) => t.palette.action.hover,
                  },
                }),
              }}
            >
              <TableHead
                sx={{
                  '& th': {
                    fontWeight: 600,
                    lineHeight: 1.3,
                    whiteSpace: 'nowrap',
                  },
                }}
              >
                {/* Header row */}
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow
                    key={headerGroup.id}
                    sx={{
                      '& th': {
                        position: 'sticky',
                        top: 0,
                        zIndex: 4,
                        fontWeight: 600,
                        lineHeight: 1.3,
                        whiteSpace: 'nowrap',
                        backgroundColor: (t) => t.palette.background.paper,
                      },
                    }}
                  >
                    {headerGroup.headers.map((header) => (
                      <TableCell
                        key={header.id}
                        colSpan={header.colSpan}
                        style={{
                          width: header.column.getSize(),
                          minWidth: header.column.getSize(),
                        }}
                        className="header-cell"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
                {/* Optional filter row */}
                {enableFilters && (
                  <TableRow
                    sx={{
                      '& th': {
                        position: 'sticky',
                        zIndex: 3,
                        backgroundColor: (t) => t.palette.background.paper,
                        boxShadow: 'none',
                        borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                        top: HEADER_H,
                      },
                    }}
                  >
                    {table.getVisibleLeafColumns().map((col) => {
                      const meta = col.columnDef.meta as FilterMeta | undefined;
                      if (!col.getCanFilter()) return <TableCell key={col.id} />;

                      const value = columnFilters[col.id] ?? '';

                      return (
                        <TableCell
                          key={col.id}
                          style={{ width: col.getSize(), minWidth: col.getSize() }}
                          className="filter-cell"
                          sx={{
                            backgroundColor: (t) => t.palette.background.paper,
                          }}
                        >
                          {meta?.filterType === 'select' && (
                            <TextField
                              select
                              size="small"
                              fullWidth
                              value={value}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                const selectedValue = e.target.value;

                                setColumnFilters((prev) => {
                                  const newFilters = { ...prev };
                                  if (selectedValue === '') {
                                    delete newFilters[col.id];
                                  } else {
                                    newFilters[col.id] = selectedValue;
                                  }

                                  return newFilters;
                                });
                              }}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <SearchRounded fontSize="small" />
                                  </InputAdornment>
                                ),
                                inputProps: commonInputProps,
                              }}
                              margin="none"
                            >
                              <MenuItem value="">Todos</MenuItem>
                              {meta.options.map((opt: { value: string; label: string }) => (
                                <MenuItem key={opt.value} value={opt.value}>
                                  {opt.label}
                                </MenuItem>
                              ))}
                            </TextField>
                          )}

                          {meta?.filterType === 'date' &&
                            (() => {
                              return (
                                <Box display="flex" gap={1}>
                                  <MuiStyledDatePicker
                                    value={
                                      (columnFilters[col.id] as [Date | null, Date | null]) || [
                                        null,
                                        null,
                                      ]
                                    }
                                    onChange={(update) =>
                                      setColumnFilters((prev) => ({
                                        ...prev,
                                        [col.id]: Array.isArray(update) ? update : '',
                                      }))
                                    }
                                    selectsRange
                                    placeholder="Selecione o intervalo"
                                  />
                                </Box>
                              );
                            })()}
                          {!meta || meta.filterType === 'text' ? (
                            <TextField
                              size="small"
                              placeholder="Pesquisar…"
                              fullWidth
                              value={value}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                setColumnFilters((p) => ({ ...p, [col.id]: e.target.value }))
                              }
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <SearchRounded fontSize="small" />
                                  </InputAdornment>
                                ),
                                inputProps: commonInputProps,
                              }}
                            />
                          ) : null}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                )}
              </TableHead>

              <TableBody>
                {table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    hover={!!onRowClick}
                    onClick={() => onRowClick?.(row.original)}
                    sx={{ cursor: onRowClick ? 'pointer' : 'default' }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        style={{ width: cell.column.getSize(), minWidth: cell.column.getSize() }}
                        className={cell.column.id === 'actions' ? 'sticky-actions' : undefined}
                        sx={
                          cell === row.getVisibleCells()[row.getVisibleCells().length - 1]
                            ? undefined
                            : {
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }
                        }
                      >
                        {/* renders objects also this way, label of it atm  */}
                        {(() => {
                          const value = cell.getValue();
                          if (value && typeof value === 'object' && 'label' in value) {
                            return String((value as { label: React.ReactNode }).label);
                          }
                          return flexRender(cell.column.columnDef.cell, cell.getContext());
                        })()}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {(!removePaginationOnLowResults ||
            (removePaginationOnLowResults && table.getFilteredRowModel().rows.length > 10)) && (
            <TablePagination
              component="div"
              padding="none"
              count={total || table.getFilteredRowModel().rows.length}
              page={pagination.pageIndex}
              rowsPerPage={pagination.pageSize}
              onPageChange={(_, newPage) =>
                onPaginationChange?.({ ...pagination, pageIndex: newPage })
              }
              onRowsPerPageChange={(e) =>
                onPaginationChange?.({ pageIndex: 0, pageSize: parseInt(e.target.value, 10) })
              }
              rowsPerPageOptions={rowsPerPageOptions}
            />
          )}
        </Paper>
      </Box>
    </Stack>
  );
}
