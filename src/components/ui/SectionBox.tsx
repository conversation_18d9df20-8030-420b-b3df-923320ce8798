'use client';

import { ReactNode } from 'react';
import { Box, Typography } from '@mui/material';

interface SectionBoxProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
}

export default function SectionBox({ title, subtitle, children }: SectionBoxProps) {
  return (
    <Box
      sx={(theme) => ({
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 1,
        p: 2.5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        backgroundColor: theme.palette.background.paper,
      })}
    >
      <Typography variant="subtitle1">{title}</Typography>
      {subtitle && (
        <Typography variant="body2" color="text.secondary">
          {subtitle}
        </Typography>
      )}
      {children}
    </Box>
  );
}
