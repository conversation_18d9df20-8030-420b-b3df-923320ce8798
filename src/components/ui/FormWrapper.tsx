import { Box } from '@mui/material';
import { ReactNode } from 'react';
import { ComposableForm } from '../form-builder/ComposableFormBuilder';

type FormProps = {
  readonly children: ReactNode;
  readonly onReset?: React.FormEventHandler<HTMLFormElement>;
  readonly onSubmit?: React.FormEventHandler<HTMLFormElement>;
  readonly methods: import('react-hook-form').UseFormReturn<any>;
  readonly footerSlot?: ReactNode;
  readonly columns?: number;
};

// create a form instance with this, creating buttons on the footer of the page
export default function ComposableFormWrapper({
  children,
  onReset,
  onSubmit,
  methods,
  columns = 1,
  footerSlot,
}: FormProps) {
  return (
    <Box sx={{ display: 'contents' }} component="form" onReset={onReset} onSubmit={onSubmit}>
      <ComposableForm.Provider columns={columns} methods={methods}>
        {children}
        {footerSlot}
      </ComposableForm.Provider>
    </Box>
  );
}
