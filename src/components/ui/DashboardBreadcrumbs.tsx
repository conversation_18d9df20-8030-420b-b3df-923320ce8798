'use client';
import { useBreadcrumbs } from '@/hooks/useBreadCrumbs';
import { Breadcrumbs, Link, Typography } from '@mui/material';

export default function DashboardBreadcrumbs() {
  const crumbs = useBreadcrumbs();

  if (crumbs.length === 0) return null;

  return (
    <Breadcrumbs sx={{ marginTop: '36px', marginBottom: '8px' }} separator="›">
      {crumbs.map((c, i) =>
        i === crumbs.length - 1 ? (
          <Typography key={i} variant="overline" color="text.secondary">
            {c.label}
          </Typography>
        ) : (
          <Link key={i} color="inherit" underline="hover" variant="overline" href={c.href ?? '#'}>
            {c.label}
          </Link>
        )
      )}
    </Breadcrumbs>
  );
}