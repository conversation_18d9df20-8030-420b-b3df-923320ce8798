'use client';
import { Box } from '@mui/material';

interface FloatingActionsProps {
  children: React.ReactNode;
}

export default function FloatingActions({ children }: FloatingActionsProps) {
  return (
    <Box
      component="footer"
      sx={(theme) => ({
        position: 'fixed',
        bottom: theme.spacing(3),
        right: theme.spacing(8),
        display: 'flex',
        gap: theme.spacing(2),
        zIndex: theme.zIndex.modal + 1,
      })}
    >
      {children}
    </Box>
  );
}
