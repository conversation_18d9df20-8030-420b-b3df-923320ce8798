import { CalendarMonthRounded } from '@mui/icons-material';
import { InputAdornment } from '@mui/material';
import TextField from '@mui/material/TextField';
import { forwardRef } from 'react';

export const DatePickerInput = forwardRef<HTMLInputElement, any>((props, ref) => (
  <TextField
    size="small"
    fullWidth
    inputRef={ref}
    {...props}
    InputProps={{
      ...props.InputProps,
      startAdornment: (
        <InputAdornment position="start">
          <CalendarMonthRounded fontSize="small" />
        </InputAdornment>
      ),
      ...props.InputProps,
    }}
  />
));
