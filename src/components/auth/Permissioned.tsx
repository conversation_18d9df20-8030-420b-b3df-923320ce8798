'use client';
import { ReactNode } from 'react';
import { useCan } from '@/hooks/useCan';
import type { PermissionCode } from '@/lib/auth/permissions';

interface Props {
  perm: PermissionCode;
  children: ReactNode;
}

/**
 * Renders children only if the current user passes the can() check.
 * Usage: <Permissioned perm="LEADS_EXPORT_EXCEL"><ExportBtn/></Permissioned>
 */
export default function Permissioned({ perm, children }: Props) {
  return useCan(perm) ? <>{children}</> : null;
}
