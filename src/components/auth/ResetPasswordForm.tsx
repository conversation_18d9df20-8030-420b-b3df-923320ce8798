'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/ToastProvider';
import { TextField, Button, Box, CardContent, Card, Container, Typography } from '@mui/material';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z, ZodError } from 'zod';
import { resetPasswordAction } from '@/app/actions/auth';
import { useTranslations } from 'next-intl';

const formSchema = z
  .object({
    newPassword: z.string().min(8, 'A senha deve ter ao menos 8 caracteres'),
    confirmPassword: z.string().min(8, 'A confirmação deve ter ao menos 8 caracteres'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'As senhas não coincidem',
    path: ['confirmPassword'],
  });

type FormValues = z.infer<typeof formSchema>;

interface ResetPasswordFormProps {
  token: string;
}

export default function ResetPasswordForm({ token }: ResetPasswordFormProps) {
  const router = useRouter();
  const { showToast } = useToast();
  const t = useTranslations('common');
  const tToast = useTranslations('toast');

  const [serverErrors, setServerErrors] = useState<Record<string, string>>({});

  const {
    register,
    handleSubmit,
    formState: { errors: clientErrors, isSubmitting },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  });

  const onSubmitClient = async (data: FormValues) => {
    setServerErrors({});

    try {
      const fd = new FormData();
      fd.append('newPassword', data.newPassword);
      fd.append('confirmPassword', data.confirmPassword);

      await resetPasswordAction(token, fd);

      showToast(tToast('resetPassword.successMessage'), 'success');
      router.push(`/sign-in`);
    } catch (err: unknown) {
      if (err instanceof ZodError) {
        const fieldErrors = err.flatten().fieldErrors;
        const mapped: Record<string, string> = {};
        Object.entries(fieldErrors).forEach(([key, msgs]) => {
          if (msgs?.[0]) {
            mapped[key] = msgs[0];
          }
        });
        setServerErrors(mapped);
      } else {
        const msg = err instanceof Error ? err.message : 'Falha ao redefinir senha';
        showToast(msg, 'error');
      }
    }
  };

  return (
    <Container maxWidth="sm" sx={{ py: 4 }}>
      <Card>
        <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5" textAlign="center">
            {t('forgotPassword.title')}
          </Typography>
          <Box
            component="form"
            onSubmit={handleSubmit(onSubmitClient)}
            noValidate
            sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
          >
            <TextField
              label={t('resetPassword.newPassword')}
              type="password"
              required
              fullWidth
              error={!!clientErrors.newPassword || !!serverErrors.newPassword}
              helperText={clientErrors.newPassword?.message ?? serverErrors.newPassword}
              {...register('newPassword')}
            />

            <TextField
              label={t('resetPassword.confirmPassword')}
              type="password"
              required
              fullWidth
              error={!!clientErrors.confirmPassword || !!serverErrors.confirmPassword}
              helperText={clientErrors.confirmPassword?.message ?? serverErrors.confirmPassword}
              {...register('confirmPassword')}
            />

            <Button type="submit" variant="contained" fullWidth disabled={isSubmitting}>
              {isSubmitting ? t('button.submitting') : t('button.submit')}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
