'use client';
import { ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import DashboardClientPage from '@/app/[locale]/(protected)/dashboard/components/DashboardClientPage';

interface Props {
  children: ReactNode;
}

/**
 * Client‑side wrapper that blocks rendering until the session arrives.
 * Redirects unauthenticated users to /sign-in.
 */
export default function AuthGuard({ children }: Props) {
  const { status } = useSession();

  if (status === 'loading') return null; // optional splash / spinner
  if (status === 'unauthenticated') redirect('/sign-in');

  return <DashboardClientPage>{children}</DashboardClientPage>;
}
