'use client';

import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import TextField from '@mui/material/TextField';
import { useState } from 'react';

interface Props {
  initialValue?: string;
  error?: boolean;
  helperText?: string;
}

export default function EmailInput({ initialValue = '', error, helperText }: Props) {
  const [value, setValue] = useState(initialValue);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const clearUrlParams = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete('error_email');
    params.delete('sent');
    router.replace(params.size ? `${pathname}?${params}` : pathname, { scroll: false });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (error) clearUrlParams();
    setValue(e.target.value);
  };

  return (
    <TextField
      name="email"
      label="Email"
      type="email"
      required
      fullWidth
      value={value}
      onChange={handleChange}
      error={error}
      helperText={helperText}
    />
  );
}
