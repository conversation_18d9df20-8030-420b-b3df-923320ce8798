import { useSession } from 'next-auth/react';
import { withPathPrefix } from '.';
import { Session } from 'next-auth';
import { redirect } from 'next/navigation';
import { merge } from 'lodash';
import { useCallback } from 'react';

export const fetchInstance = async (
  url: string,
  opts: RequestInit = {},
  session?: Session | null
) => {
  const { headers = {}, ...rest } = opts;

  const res = await fetch(withPathPrefix(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1`)(url), {
    headers: {
      'Content-Type': 'application/json',
      Authorization: session ? `Bearer ${session.accessToken}` : undefined,
      ...headers,
    } as HeadersInit,
    cache: 'no-store',
    ...rest,
  });

  if (res.status === 401) {
    return redirect('/sign-in');
  }

  if (!res.ok) {
    throw new Error(`Request failed with status ${res.status}`);
  }
  return res.json();
};

export const useSessionFetch = (defaultOptions: RequestInit = {}) => {
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      redirect('/sign-in');
    },
  });

  if (!session) {
    throw new Error('Not authenticated');
  }

  return useCallback(
    (url: string, options: RequestInit = {}) =>
      fetchInstance(url, merge({}, defaultOptions, options), session),
    [defaultOptions, session]
  );
};
