{"hello": "Ol<PERSON>!", "welcome": "Bem-vindo à Caixa Mágica", "subtitle": "Desenvolvendo software human-oriented para o setor público, fintechs e startups.", "homepage": {"title": "A tua área de formação começa aqui!", "subtitle": "Plataforma digital para formandos, formadores e equipas de gestão - tudo num só lugar."}, "gdpr": "RGPD", "signIn": {"title": "Bem-vindo!", "mobileTitle": "<PERSON><PERSON><PERSON>", "subtitle": "Inicia sessão para acederes à tua área SA Formação"}, "signUp": {"title": "C<PERSON><PERSON> conta", "subtitle": "Crie a sua conta na Caixa Mágica", "account": "Já tem conta? ", "successMessage": "Conta criada com sucesso. Já pode iniciar sessão."}, "forgotPassword": {"title": "Esqueceu a palavra-passe?", "instructions": "Consulte o seu e-mail para redefinir a palavra-passe.", "sentTitle": "Email a caminho!", "sentInstructions": "Enviámos-te um link para repor a palavra-passe. Não recebeste? Verifica a pasta de spam ou tenta novamente dentro de alguns minutos.", "resendLink": "Reenviar link", "backToSignIn": "Voltar para Início de sessão", "ResendLinkIn": "Reenviar email em {time}"}, "resetPassword": {"title": "Redefinir palavra-passe", "newPassword": "Nova palavra-passe", "confirmPassword": "Confirmar palavra-passe", "successMessage": "Palavra-passe alterada com sucesso. Já podes iniciar sessão."}, "button": {"submit": "Enviar", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "Registar", "cancel": "<PERSON><PERSON><PERSON>", "SigningUp": "A criar conta...", "forgotPassword": "<PERSON><PERSON><PERSON>-me da palavra-passe", "sendReset": "Enviar link de redefinição", "submitting": "A enviar...", "sent": "Enviado", "createEntity": "Criar Entidade"}, "nav": {"en": "EN", "pt": "PT"}, "form": {"email": "Email", "password": "Palavra-passe", "rememberMe": "<PERSON><PERSON><PERSON><PERSON>me"}, "errors": {"invalidCredentials": "Credenciais inválidas. Por favor, tente novamente.", "unexpectedError": "Ocorreu um erro inesperado. Por favor, tente novamente mais tarde.", "recoverPasswordEmailFailed": "Ocorreu um erro ao enviar o e-mail de recuperação."}, "dashboard": {"placeholder": {"new": {"profiles": "Novo perfil", "users": "Novo utilizador", "leads": "Nova lead", "opportunities": "Nova oportunidade", "entities": "Nova entidade"}}, "profiles": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Gere os diferentes perfis de utilizador e defina os seus níveis de acesso.", "label": "Novo perfil", "buttons": {"delete": "Eliminar", "duplicate": "Duplicar", "cancel": "<PERSON><PERSON><PERSON>"}, "deleteProfile": {"title": "Eliminar perfil", "subtitle": "Tem a certeza que pretende eliminar este perfil?", "description": "Esta ação é irreversível e os utilizadores que utilizam este perfil poderão perder o acesso à plataforma."}, "duplicateProfile": {"title": "Duplicar perfil", "subtitle": "Tem a certeza que pretende duplicar este perfil?", "description": "Esta ação irá criar uma cópia deste perfil, incluindo todas as suas permissões e níveis de acesso."}, "new": {"title": "<PERSON><PERSON><PERSON> perfil", "subtitle": "Cria um novo perfil e configura as suas permissões e níveis de acesso.", "cards": {"profileDataTitle": "Informação do perfil", "labelTextArea": "Nome do perfil", "accordionTitle": "Permissões e níveis de acesso"}, "buttons": {"createProfile": "<PERSON><PERSON><PERSON> perfil", "editProfile": "Guardar alterações", "cancel": "<PERSON><PERSON><PERSON>"}}, "edit": {"title": "Administrador", "subtitle": "Configura as permissões e níveis de acesso deste perfil na plataforma."}}, "crm": {"leads": {"title": "Leads", "subtitle": "Gere e acompanha todos os pedidos de informação recebidos.", "label": "Importar Leads", "lead": {"title": "<PERSON><PERSON><PERSON> da <PERSON>", "label": "Ver ficha de contacto", "secondaryLabel": "<PERSON><PERSON><PERSON>", "details": {"assignLeadDialog": {"title": "Reatribuir lead", "alertMessage": "Esta lead contém dados de contacto já existentes no sistema.", "instructionText": "Seleciona o novo utilizador a quem queres atribuir esta lead.", "searchPlaceholder": "Pesquisa utilizador…", "confirmLabel": "Reatribuir lead", "cancelLabel": "<PERSON><PERSON><PERSON>"}, "forms": {"title": "<PERSON><PERSON><PERSON> lead sem sucesso ({id})", "inputLabel": "Indica a razão do fecho e descreve o motivo", "selectReason": "Seleciona a razão", "textFieldPlaceholder": "Insere uma descrição", "buttons": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>"}}, "sections": {"basicInfo": "Informações básicas", "location": "Localização", "assignment": "Atribuição", "course": "Curso", "message": "Mensagem"}, "fields": {"id": "ID", "status": "Estado", "name": "Nome", "email": "Email", "contact": "Contacto", "nif": "NIF", "postalCode": "Código-Postal", "district": "Distrito", "locality": "Localidade", "municipality": "<PERSON><PERSON><PERSON>", "assignedTo": "Atribuído a", "assignmentDate": "Data de atribuição", "creationDate": "Data de criação", "course": "Curso", "assignedCampus": "<PERSON><PERSON><PERSON>", "campus": "<PERSON><PERSON><PERSON>", "educationLevel": "Nível de Escolaridade", "preferredSchedule": "<PERSON><PERSON><PERSON><PERSON>", "message": "Mensagem"}}}, "assignLeadDialog": {"dialogTitle": "Atribuir lead", "alertMessage": "Esta lead contém dados de contacto já existentes no sistema.", "instructionText": "Seleciona o utilizador a quem queres atribuir esta lead.", "searchPlaceholder": "Pesquisa utilizador…", "confirmLabel": "Atribuir lead", "cancelLabel": "<PERSON><PERSON><PERSON>"}}, "opportunities": {"title": "Oportunidades", "subtitle": "Gere e acompanha todas as oportunidades em curso.", "label": "Nova oportunidade", "opportunity": {"title": "Ficha de oportunidade"}, "interactions": {"editItem": "Editar interação", "deleteItem": "Eliminar interação", "editDialog": "Editar interação ({id})", "details": "Detalhes interação ({id})", "add": "Adicionar nova interação", "noneExisting": "Não existe nenhuma interação", "edit": {"successMessage": "Interação ({id}) atualizada com sucesso!", "fields": {"type": {"label": "Tipo", "placeholder": "Seleciona tipo"}, "state": {"label": "Estado", "placeholder": "Insere estado..."}, "scheduledDate": {"label": "Data agendada"}, "doneDate": {"label": "Data efetuada"}, "doneBy": {"label": "Efetuada por"}, "createdAt": {"label": "Data criação"}, "text": {"label": "Texto", "placeholder": "Insere texto..."}}}, "cancelLabel": "<PERSON><PERSON><PERSON>", "confirmWithoutContact": "<PERSON><PERSON><PERSON> s/ contacto", "confirmWithContact": "<PERSON><PERSON><PERSON> c/ contacto"}, "table": {"actions": {"view": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "assign": "Atribuir lead", "closeSuccess": "Fechar c/ sucesso", "closeFail": "<PERSON><PERSON>r s/ sucesso"}, "columns": {"id": "ID", "status": "Estado", "createdAt": "Data criação", "name": "Nome", "email": "Email", "contact": "Contacto", "campus": "Polo atribuição", "payment": "Pagamento", "course": "Curso", "assignedTo": "Atribuído a"}, "statusOptions": {"new": "New", "assigned": "Assigned", "inQualification": "In Qualification", "closedSuccess": "Closed with Success", "closedFail": "Closed without Success", "inValidation": "In Validation", "cancelled": "Cancelled"}}, "actions": {"view": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "delete": "Eliminar oportunidade"}, "dialog": {"confirmDeleteTitle": "Eliminar oportunidade", "confirmDeleteSubtitle": "Tens a certeza de que pretendes eliminar esta oportunidade?", "deleteDescription": "Esta ação é permanente e não poderá ser revertida.", "delete": "Eliminar", "cancel": "<PERSON><PERSON><PERSON>"}}, "contacts": {"users": {"title": "<PERSON>cha de contacto", "edit": "<PERSON><PERSON> ", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "newUser": "Novo utilizador"}, "entities": {"title": "Nova ficha de entidades"}}}, "contacts": {"users": {"title": "<PERSON>cha de contacto", "edit": "<PERSON><PERSON> ", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "newUser": "Novo utilizador", "enrollments": {"actions": {"enrollment": "<PERSON><PERSON><PERSON><PERSON>", "print": "Imprimir", "invoicing": "Faturação"}, "columns": {"id": "ID", "state": "Estado", "course": "Curso", "center": "Polo", "creationDate": "Data de Criação"}}, "crmHistory": {"addSuccess": "Interação adicionada com sucesso!", "editedSuccess": "Interação #{id} editada com sucesso!", "new": "Nova interação", "columns": {"category": "Categoria", "id": "ID", "description": "Descrição", "type": "Tipo", "state": "Estado", "madeBy": "Efetuado por", "creationDate": "Data criação", "enrollment": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"previewOpportunity": "Ver ficha de oportunidade", "previewInteraction": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "editInteraction": "Editar interação", "deleteInteraction": "Eliminar interação"}, "categories": {"OPPORTUNITY": "Oportunidade", "INTERACTION_OPPORTUNITY": "Interação oportunidade", "INTERACTION_SYSTEM": "Interação sistema", "INTERACTION_EXTERNAL": "Interação externa"}}}, "entities": {"title": "Nova ficha de entidades"}}}, "gesmat": {"enrollments": {"contract": "Contratualização Geral da Matrícula", "paymentEnrollment": "Pagamento Efetuado no ato da Inscrição", "groups": {"add": "Adicionar matrícula a grupo", "list": "Lista de grupo", "transfer": "Transferir Matrícula", "delete": "Eliminar matr<PERSON><PERSON>", "columns": {"ref": "Ref.", "status": "Estado", "course": "Curso", "center": "Polo", "modality": "Modal. formação", "schedule": "<PERSON><PERSON><PERSON><PERSON>", "expectedStart": "Prev. in<PERSON><PERSON>", "daysSinceOldestEnrollment": "Dias desde matrícula mais antiga", "numTrainees": "Nº formandos", "fundraisingDeadline": "Data limite angariação", "startDate": "Data início", "assignedTo": "Atribuido a", "createdBy": "<PERSON><PERSON><PERSON> por", "createdAt": "Data criação"}}, "fields": {"course": {"label": "Curso", "placeholder": "Insere curso..."}, "courseValue": {"label": "Valor do curso", "placeholder": "Insere valor..."}, "monthlyCount": {"label": "Número men<PERSON>", "placeholder": "Insere número..."}, "hasInsurance": {"label": "Tem seguro próprio"}, "insuranceValue": {"label": "Valor do seguro", "placeholder": "Oferta do seguro"}, "enrollmentValue": {"label": "Valor da inscrição"}, "differentInvoiceContact": {"label": "Dados de faturação diferentes dos dados do formando"}, "recurringPaymentTypes": {"label": "Forma de pagamento recorrente", "options": {"transfer": "Transferência", "reference": "Referência de multibanco", "presential": "Pagamento Presencial"}}, "paymentTypes": {"label": "Forma de pagamento", "options": {"transfer": "Transferência", "card": "Multibanco", "cash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "check": "Cheque", "reference": "Referência de multibanco"}}, "modality": {"label": "Modalidade Formação", "options": {"inPerson": "Presencial", "bLearning": "B-Learning", "eLearning": "E-Learning"}}, "monthlyPaymentValue": {"label": "Valor pago de mensalidade"}, "totalPaid": {"label": "Total pago no ato de inscrição"}, "paymentType": {"label": "Tipo de pagamento"}, "checkNumber": {"label": "Cheque número", "placeholder": "Insere número..."}, "bank": {"label": "Banco", "placeholder": "Insere banco..."}, "agency": {"label": "Agência", "placeholder": "Insere agência..."}, "opportunityState": {"label": "Estado da opportunidade", "placeholder": "Seleciona novo estado"}, "closingReason": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "seleciona razão"}, "closingResult": {"label": "<PERSON>sul<PERSON><PERSON>", "placeholder": "seleciona resultado"}, "closingDate": {"label": "Data Fecho"}, "closingDescription": {"label": "Descrição <PERSON>cho"}}}}, "forms": {"errors": {"name": {"required": "O nome é obrigatório."}, "email": {"invalidFormat": "O email deve ser válido."}, "phone": {"invalidFormat": "O telefone deve ser válido."}, "nif": {"wrongLength": "O NIF deve ser válido."}}, "label": {"cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "edit": "<PERSON><PERSON>"}, "subtab": {"details": "<PERSON><PERSON><PERSON>", "rgpd": "RGPD", "relationships": "Relações", "entities": "Entidades", "attachments": "Anexos", "crmHistory": "CRM-Histórico", "enrollments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsEnrollment": "<PERSON><PERSON><PERSON> matr<PERSON>cula", "payments": "Faturação", "basicInformation": "Informação Básica", "localization": "Localização", "atribution": "Atribuição", "course": "Curso", "message": "Mensagem"}, "placeholder": "Inserir", "name": {"label": "Nome", "placeholder": "Insere nome"}, "email": {"label": "Email", "placeholder": "Insere email"}, "mobile": {"label": "Telemóvel", "placeholder": "Insere telemóvel"}, "phone": {"label": "Telefone", "placeholder": "Insere telefone"}, "nif": {"label": "NIF", "placeholder": "Insere NIF"}, "socialSecurityNumber": {"label": "Nº Segurança Social", "placeholder": "Insere número"}, "identificationType": {"label": "Tipo Doc. Identificação", "placeholder": "Seleciona documento"}, "identificationValidity": {"label": "Validade Doc. Identificação", "placeholder": "MM/DD/YYYY"}, "identificationNumber": {"label": "N° Doc. de Identificação", "placeholder": "Insere número"}, "identificationFile": {"label": "Arquivo Doc. Identificação", "placeholder": "Insere arquivo"}, "address": {"label": "<PERSON><PERSON>", "placeholder": "Insere a morada"}, "postalCode": {"label": "Código Postal", "placeholder": "Insere código postal"}, "locality": {"label": "Localidade", "placeholder": "Insere localidade"}, "district": {"label": "Distrito", "placeholder": "Insere distrito"}, "municipality": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Insere concelho"}, "parish": {"label": "Freguesia", "placeholder": "Insere freguesia"}, "doneDate": {"label": "Data efetuada"}, "birthPlace": {"label": "Naturalidade", "placeholder": "Insere naturalidade"}, "profession": {"label": "Profissão", "placeholder": "Insere profissão"}, "employerEntity": {"label": "Entidade Empregadora", "placeholder": "Pesquisa entidade"}, "contractType": {"label": "Tipo de Contrato", "placeholder": "Seleciona tipo"}, "birthDate": {"label": "Data de Nascimento", "placeholder": "MM/DD/YYYY"}, "gender": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Seleciona género"}, "educationLevel": {"label": "Escolaridade", "placeholder": "Seleciona escolaridade"}, "iban": {"label": "IBAN", "placeholder": "Insere IBAN"}, "notes": {"label": "Notas", "placeholder": "Insere observações"}, "rgpd": {"consent_1": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei nº 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?", "consent_2": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo período em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este período reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos.", "consent_3": "Autoriza a Entidade Formadora, a fotocopiar o seu documento de identificação para fins de arquivo em Dossier Técnico-Pedagógico?", "consent_4": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?", "consent_5": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"}, "relationships": {"empty": "Não existem relações associadas a este contacto.", "add": "Adicionar <PERSON>", "label": "Nome Contacto", "placeholder": "Pesquisar contacto...", "type": {"label": "Relação", "placeholder": "Seleciona relação"}}, "observations": {"label": "Observações", "placeholder": "Insere observações..."}, "attachments": {"empty": "Não existem ficheiros associados a este contacto.", "label": "<PERSON><PERSON><PERSON><PERSON>", "subLabel": "Ficheiros importados", "modal": {"fileNamePlaceholder": "Insere nome", "filePlaceholder": "<PERSON><PERSON><PERSON>", "fileTypePlaceholder": "Seleciona tipo", "descriptionPlaceholder": "Insere descrição", "tagsPlaceholder": "Insere etiquetas"}}, "enrollmentYear": {"label": "<PERSON>o <PERSON>r<PERSON>", "placeholder": "Seleciona ano"}, "enrollmentNumber": {"label": "Nº de Matrícula", "placeholder": "Insere número"}, "crmHistory": {"addInteraction": "Adicionar interação"}, "contacts": {"entities": {"title": "Nova ficha de entidades"}}, "location": {"label": "Localidade", "placeholder": "Insere localidade..."}, "distrito": {"label": "Distrito", "placeholder": "Seleciona distrito..."}, "concelho": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Seleciona concelho"}, "freguesia": {"label": "Freguesia", "placeholder": "Seleciona freguesia"}, "country": {"label": "<PERSON><PERSON>", "placeholder": "PT"}, "opportunityState": {"label": "Estado da opportunidade", "placeholder": "Seleciona novo estado"}, "closingResult": {"label": "<PERSON>sul<PERSON><PERSON>", "placeholder": "seleciona resultado"}, "closingDate": {"label": "Data Fecho"}, "closingDescription": {"label": "Descrição <PERSON>cho"}, "statusDate": {"label": "Data E<PERSON>o"}}, "entities": {"subTabs": {"details": {"label": "<PERSON><PERSON><PERSON>"}, "protocols": {"label": "Protocolos", "subTabs": {"types": {"label": "Tipos de protocolo", "fields": {"protocolTypes": {"label": "Tipos de protocolo", "options": {"Práticas": "Práticas", "Estágios": "Est<PERSON><PERSON>s"}}}}, "addresses": {"label": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "noAddresses": "Se<PERSON> endereç<PERSON>", "success": "Endereço adicionado com sucesso!", "fields": {"type": {"label": "Tipo", "placeholder": "Insere tipo", "options": {}}}, "columns": {"type": "Tipo", "locality": "Localidade", "postalCode": "Código Postal", "parish": "Freguesia", "municipality": "<PERSON><PERSON><PERSON>", "district": "Distrito"}}, "attachments": {"label": "Anexos", "add": "Adicionar anexo", "attach": "Anexar", "noAttachments": "Sem anexos", "success": "Anexo adicionado com sucesso!", "fields": {"file": {"placeholder": "<PERSON><PERSON>", "buttonLabel": "<PERSON><PERSON><PERSON>"}, "type": {"label": "Tipo", "placeholder": "Insere tipo", "options": {}}, "labels": {"label": "Etiquetas"}, "startDate": {"label": "Data Início", "placeholder": "Seleciona Data"}, "endDate": {"label": "Data Fim", "placeholder": "Seleciona Data"}}, "columns": {"type": "Tipo", "description": "Descrição", "startDate": "Data de início", "endDate": "Data de fim", "file": "<PERSON><PERSON><PERSON>"}}}}}, "fields": {"website": {"label": "Site", "placeholder": "Insere url site..."}, "email": {"label": "Email", "placeholder": "Insere email"}, "mobile": {"label": "Telemóvel", "placeholder": "Insere telemóvel"}, "phone": {"label": "Telefone", "placeholder": "Insere telefone"}, "nif": {"label": "NIF", "placeholder": "Insere NIF"}, "socialSecurityNumber": {"label": "Nº Segurança Social", "placeholder": "Insere número"}, "identificationType": {"label": "Tipo Doc. Identificação", "placeholder": "Seleciona documento"}, "identificationValidity": {"label": "Validade Doc. Identificação", "placeholder": "MM/DD/YYYY"}, "identificationNumber": {"label": "N° Doc. de Identificação", "placeholder": "Insere número"}, "identificationFile": {"label": "Arquivo Doc. Identificação", "placeholder": "Insere arquivo"}, "address": {"label": "<PERSON><PERSON>", "placeholder": "Insere a morada"}, "postalCode": {"label": "Código Postal", "placeholder": "Insere código postal"}, "locality": {"label": "Localidade", "placeholder": "Insere localidade"}, "district": {"label": "Distrito", "placeholder": "Insere distrito"}, "municipality": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Insere concelho"}, "parish": {"label": "Freguesia", "placeholder": "Insere freguesia"}, "birthPlace": {"label": "Naturalidade", "placeholder": "Insere naturalidade"}, "profession": {"label": "Profissão", "placeholder": "Insere profissão"}, "employerEntity": {"label": "Entidade Empregadora", "placeholder": "Pesquisa entidade"}, "contractType": {"label": "Tipo de Contrato", "placeholder": "Seleciona tipo"}, "birthDate": {"label": "Data de Nascimento", "placeholder": "MM/DD/YYYY"}, "gender": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Seleciona género"}, "educationLevel": {"label": "Escolaridade", "placeholder": "Seleciona escolaridade"}, "iban": {"label": "IBAN", "placeholder": "Insere IBAN"}, "notes": {"label": "Notas", "placeholder": "Insere observações"}, "rgpd": {"consent_1": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei nº 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?", "consent_2": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo período em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este período reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos.", "consent_3": "Autoriza a Entidade Formadora, a fotocopiar o seu documento de identificação para fins de arquivo em Dossier Técnico-Pedagógico?", "consent_4": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?", "consent_5": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"}, "relationships": {"empty": "Não existem relações associadas a este contacto.", "add": "Adicionar <PERSON>", "label": "Nome Contacto", "placeholder": "Pesquisar contacto...", "type": {"label": "Relação", "placeholder": "Seleciona relação"}}, "observations": {"label": "Observações", "placeholder": "Insere observações..."}, "attachments": {"empty": "Não existem ficheiros associados a este contacto.", "label": "<PERSON><PERSON><PERSON><PERSON>", "subLabel": "Ficheiros importados", "modal": {"fileNamePlaceholder": "Insere nome", "filePlaceholder": "<PERSON><PERSON><PERSON>", "fileTypePlaceholder": "Seleciona tipo", "descriptionPlaceholder": "Insere descrição", "tagsPlaceholder": "Insere etiquetas"}}, "crmHistory": {"addInteraction": "Adicionar interação"}, "contacts": {"entities": {"title": "Nova ficha de entidades"}}, "location": {"label": "Localidade", "placeholder": "Insere localidade..."}, "distrito": {"label": "Distrito", "placeholder": "Seleciona distrito..."}, "concelho": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Seleciona concelho"}, "freguesia": {"label": "Freguesia", "placeholder": "Seleciona freguesia"}, "country": {"label": "<PERSON><PERSON>", "placeholder": "PT"}}, "entities": {"details": "<PERSON><PERSON><PERSON>", "protocols": "Protocolos", "fields": {"website": {"label": "Site", "placeholder": "Insere url site..."}, "type": {"label": "Tipo", "placeholder": "Seleciona tipo"}, "cliente_formacao": {"label": "Cliente formação"}, "cliente_faturacao": {"label": "Cliente faturação"}, "entidade_empregadora": {"label": "Entidade empregadora"}, "parceria": {"label": "Parceria"}, "capital_social": {"label": "Capital Social", "placeholder": "Inserir capital social..."}, "autorizar_dados_pessoais": {"label": "Autoriza a utilização dos meus dados pessoais, constantes nesta ficha, nos termos da Lei n° 67/98, de 26 de outubro, para efeito de tratamento informático dos processos e da homologação/certificação, de apuramento estatístico e de acompanhamento da formação realizada a efetuar pela entidade certificadora, nomeadamente a Direção Geral do Emprego (DGERT)?"}, "autorizar_processamento_dados": {"label": "Autoriza a Entidade Formadora a efetuar o processamento informático dos dados e a conservação dos mesmos pelo periodo em que o processo esteja aberto para efeitos administrativos, avaliativo e de auditoria? Durante este periodo reserva-se ao/à titular dos dados o direito de acesso e retificação dos mesmos."}, "autorizar_fotocopia": {"label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"}, "autorizar_conteudos_promocionais": {"label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de conteúdos comerciais e/ou promocionais?"}, "autorizar_comunicacoes_institucionais": {"label": "Autoriza que os dados constantes nesta ficha, sejam utilizados pela Entidade Formadora para envio de comunicações de carácter institucional e informativo?"}, "praticas": {"label": "Práticas"}, "estagios": {"label": "Est<PERSON><PERSON>s"}}}}}