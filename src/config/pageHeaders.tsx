import { DropdownOptions } from '@/components/ui/dropdown/Dropdown';
import { VIEW } from '@/constants/enums';
import { HandlerMap } from '@/store/PageHeader';
import { Translation } from '@/types/layout';
import {
  AddCircleRounded,
  CancelRounded,
  CheckCircleRounded,
  PersonRounded,
  RemoveRedEyeRounded,
  UploadRounded,
} from '@mui/icons-material';

export interface PageHeaderConfig {
  title: string;
  subtitle?: string;
  disableHeaderActions?: boolean;
  action?: {
    href?: string;
    view?: VIEW;
    label: string;
    shouldCloseTab?: boolean;
    onClick?: (() => void) | string;
    icon?: () => React.ReactNode;
    menuOptions?: (handlers: HandlerMap) => React.ReactNode;
    menu?: Array<{
      label: string;
      icon?: () => React.ReactNode;
      href?: string;
      onClick?: string;
    }>;
  };
  secondaryAction?: {
    href?: string;
    view?: VIEW;
    label: string;
    shouldCloseTab?: boolean;
    icon?: () => React.ReactNode;
    shown?: () => boolean;
    menuOptions?: (handlers: HandlerMap) => React.ReactNode;
    menu?: Array<{
      label: string;
      icon?: () => React.ReactNode;
      href?: string;
      onClick?: () => void;
    }>;
  };
  actions?: React.ReactNode;
}
//TODO: Add translated page headers
export const PAGE_HEADERS = (t: Translation, tMenu: Translation): [string, PageHeaderConfig][] => [
  [
    '/dashboard/profiles/new',
    {
      title: t('profiles.record.new'),
      subtitle: t('profiles.record.subtitleNew'),
    },
  ],
  [
    '/dashboard/profiles/:id',
    {
      title: t('profiles.record.title'),
      subtitle: t('profiles.record.subtitle'),
      // action: {
      //   label: `contacts.users.${view === VIEW.VIEW ? VIEW.EDIT : 'save'}`,
      //   icon: () => (view === VIEW.VIEW ? <EditIcon /> : <SaveIcon />),
      //   shouldCloseTab: true,
      // },
      // secondaryAction: {
      //   shown: () => view !== VIEW.VIEW,
      //   label: `contacts.users.cancel`,
      //   shouldCloseTab: true,
      // },
    },
  ],
  [
    '/dashboard/profiles/',
    {
      title: t('profiles.main.title'),
      subtitle: t('profiles.main.subtitle'),
      action: {
        href: '/dashboard/profiles/new',
        label: t('profiles.main.action'),
        icon: () => <AddCircleRounded />,
      },
      // exemplo de actions customizadas
      // actions: (
      //   <Button
      //     component={Link}
      //     href="/dashboard/profiles/new"
      //     variant="contained"
      //     startIcon={<AddCircleRounded />}
      //   >
      //     Novo Perfil
      //   </Button>
      // ),
    },
  ],
  [
    '/dashboard/teams/',
    {
      title: t('teams.main.title'),
      subtitle: t('teams.main.subtitle'),
      action: {
        label: t('teams.main.action'),
        icon: () => <AddCircleRounded />,
        onClick: 'newTeam',
      },
    },
  ],
  [
    '/dashboard/contacts/people/:id',
    {
      title: t('contacts.people.record.title'),
      //subtitle: 'profiles.edit.subtitle',
    },
  ],
  [
    '/dashboard/contacts/people/new/:id',
    {
      title: t('contacts.people.record.new'),
      //subtitle: 'profiles.edit.subtitle',
    },
  ],
  [
    '/dashboard/crm/leads',
    {
      title: t('crm.leads.main.title'),
      subtitle: t('crm.leads.main.subtitle'),
      action: {
        href: '/dashboard/crm/leads',
        label: t('crm.leads.main.action'),
        icon: () => <UploadRounded />,
      },
    },
  ],
  [
    '/dashboard/crm/leads/:id',
    {
      title: t('crm.leads.record.title'),
      action: {
        href: '/dashboard/crm/leads/:id/edit',
        label: t('crm.leads.record.action'),
        menuOptions: (handlers) => (
          <>
            <DropdownOptions.Option
              key="reassignLead"
              label={tMenu('reassignLead')}
              icon={PersonRounded}
              onClick={handlers.reassignLead}
            />
            <DropdownOptions.Option
              key={'closeSuccess'}
              icon={CheckCircleRounded}
              iconColor="success"
              label={tMenu('closeSuccess')}
              onClick={handlers.closeSuccess}
            />
            <DropdownOptions.Option
              key={'closeFail'}
              icon={CancelRounded}
              iconColor="error"
              label={tMenu('closeFail')}
              onClick={handlers.closeFail}
            />
          </>
        ),
        /*
        menu: [
          { label: 'Reatribuir lead', icon: () => <PersonRounded />, onClick: 'reassignLead' },
          {
            label: 'Fechar c/ sucesso',
            icon: () => <CheckCircleRounded color="success" />,
            onClick: 'closeSuccess',
          },
          {
            label: 'Fechar s/ sucesso',
            icon: () => <CancelRounded color="error" />,
            onClick: 'closeFail',
          },
        ],*/
      },
      secondaryAction: {
        href: '/dashboard/contacts/people/:id',
        label: t('crm.leads.record.secondaryAction'),
        icon: () => <RemoveRedEyeRounded />,
      },
    },
  ],
  [
    '/dashboard/contacts/entities/new/:id',
    {
      title: t('contacts.entities.record.new'),
    },
  ],
  [
    '/dashboard/contacts/entities/:id',
    {
      title: t('contacts.entities.record.title'),
    },
  ],
  [
    '/dashboard/contacts/entities',
    {
      title: t('contacts.entities.main.title'),
      subtitle: t('contacts.entities.main.subtitle'),
      action: {
        href: '/dashboard/contacts/entities/new',
        label: t('contacts.entities.main.action'),
        icon: () => <AddCircleRounded />,
      },
    },
  ],
  [
    '/dashboard/crm/opportunities',
    {
      title: t('crm.opportunities.main.title'),
      subtitle: t('crm.opportunities.main.subtitle'),
      action: {
        href: '/dashboard/crm/opportunities/new',
        label: t('crm.opportunities.main.action'),
        icon: () => <AddCircleRounded />,
      },
    },
  ],
  [
    '/dashboard/crm/opportunities/:id',
    {
      title: t('crm.opportunities.record.title'),
    },
  ],
  [
    '/dashboard/crm/opportunities/new/:id',
    {
      title: t('crm.opportunities.record.new'),
      subtitle: t('crm.opportunities.new.subtitle'),
    },
  ],
  [
    '/dashboard/contacts/people',
    {
      title: t('contacts.people.main.title'),
      subtitle: t('contacts.people.main.subtitle'),
      action: {
        href: '/dashboard/contacts/people/new',
        label: t('contacts.people.main.action'),
        icon: () => <AddCircleRounded />,
      },
    },
  ],
  [
    '/dashboard/contacts/people/new/',
    {
      title: t('contacts.people.record.new'),
    },
  ],
  [
    '/dashboard/contacts/people/:id',
    {
      title: t('contacts.people.record.title'),
      subtitle: t('contacts.people.record.subtitle'),
    },
  ],
];
