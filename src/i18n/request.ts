import { getRequestConfig } from 'next-intl/server';
import { hasLocale } from 'next-intl';
import { routing } from './routing';
import fs from 'fs';
import path from 'path';

export default getRequestConfig(async ({ requestLocale }) => {
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested) ? requested : routing.defaultLocale;

  const dir = path.join(process.cwd(), 'src/locales', locale);
  const files = fs.readdirSync(dir).filter((file) => file.endsWith('.json'));
  const messages: Record<string, any> = {};

  for (const file of files) {
    const key = path.basename(file, '.json');
    messages[key] = (await import(`../locales/${locale}/${file}`)).default;
  }

  return {
    locale,
    messages,
  };
});
