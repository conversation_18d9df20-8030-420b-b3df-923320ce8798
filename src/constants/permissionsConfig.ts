import { ERole } from './role';

const storePermissions: Permission[] = [
  { id: '1', name: 'all_permissions', enabled: false },
  { id: '2', name: 'create_manage_apps', enabled: false },
  { id: '3', name: 'submit_version', enabled: false, requiredPermissions: ['create_manage_apps'] },
  { id: '4', name: 'create_manage_categories', enabled: false },
  { id: '5', name: 'create_manage_promotions', enabled: false },
];

const managementPermissions = [
  { id: '1', name: 'all_permissions', enabled: false },
  { id: '2', name: 'create_manage_users', enabled: false },
  { id: '3', name: 'profile_permissions', enabled: false },
  { id: '4', name: 'password_reset', enabled: false },
  { id: '5', name: 'create_locales', enabled: false },
];

const metricsPermissions: Permission[] = [{ id: '1', name: 'consult_metrics', enabled: false }];

// TODO: see if this is how the permissions should be structured, probably not
export const rolePermissionMap: Record<ERole, PermissionModule[]> = {
  [ERole.MERCHANT]: [
    { moduleName: 'metrics', permissions: metricsPermissions },
    { moduleName: 'store', permissions: [] },
    { moduleName: 'management', permissions: [] },
  ],
  [ERole.SPONSOR]: [
    { moduleName: 'metrics', permissions: metricsPermissions },
    { moduleName: 'store', permissions: [] },
    { moduleName: 'management', permissions: [] },
  ],
  [ERole.APP_DEVELOPER]: [
    { moduleName: 'metrics', permissions: metricsPermissions },
    { moduleName: 'store', permissions: storePermissions },
    { moduleName: 'management', permissions: managementPermissions },
  ],
  [ERole.MASTER]: [
    { moduleName: 'metrics', permissions: metricsPermissions },
    { moduleName: 'store', permissions: storePermissions },
    { moduleName: 'management', permissions: managementPermissions },
  ],
};
