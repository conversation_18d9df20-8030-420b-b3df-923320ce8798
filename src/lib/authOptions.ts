import { NextAuthOptions } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import ENDPOINTS from '@/constants/endpoints';
import { Ability, RawUser } from './types/ability';
import { buildAbility } from './auth/ability';
import { DecodedTokenV1, DecodedTokenV2, PermObject } from '@/types/next-auth';

function isV1Token(token: DecodedTokenV1 | DecodedTokenV2): token is DecodedTokenV1 {
  return 'profiles' in token;
}
function isPermObject(entry: string | PermObject): entry is PermObject {
  return typeof entry === 'object' && entry !== null && 'code' in entry;
}

const decodeJwt = <T = unknown>(jwt: string): T => {
  const [, payload] = jwt.split('.');
  return JSON.parse(Buffer.from(payload, 'base64url').toString('utf8')) as T;
};

export const authOptions: NextAuthOptions = {
  session: { strategy: 'jwt' },

  providers: [
    Credentials({
      name: 'Credentials',
      credentials: {
        email: { label: 'E-mail', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) return null;

        const res = await fetch(ENDPOINTS.AUTH.LOGIN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: credentials.email,
            password: credentials.password,
          }),
        });
        if (!res.ok) return null;

        const data = await res.json();

        //  TOKEN V1 / V2 FLOW (NEW)
        //  If the API does **NOT** return "user" separately,
        //  we're in the new flow — and need to detect whether
        //  the decoded token matches V1 or V2.

        if (!('user' in data)) {
          const { accessToken, refreshToken } = data;
          const payload = decodeJwt<DecodedTokenV1 | DecodedTokenV2>(accessToken);

          /* ---------- TOKEN V1 ---------- */
          if (isV1Token(payload)) {
            const roles = payload.profiles.map(({ role, subrole }) =>
              subrole ? `${role}:${subrole}` : role
            );

            const ability = buildAbility({
              roles,
              functionalities: payload.functionalities,
            });

            return {
              id: payload.userId,
              name: payload.name,
              roles,
              ability,
              accessToken,
              refreshToken,
            };
          }

          /* ---------- TOKEN V2 ---------- */
          const roleEntries = payload.role;

          // split simple roles x detailed functionality objects
          const roles: string[] = [];
          const functionalities: string[] = [];

          roleEntries.forEach((entry) => {
            if (typeof entry === 'string') {
              roles.push(entry);
            } else if (isPermObject(entry)) {
              functionalities.push(entry.code);
              // for full granular permissions,
              // pass `entry.permissions` to buildAbility.
              // For now, keep 1:1 compatibility with V1
            }
          });

          const ability = buildAbility({ roles, functionalities });

          return {
            id: payload.userId,
            name: credentials.email, // V2 token lacks name; use email
            roles,
            ability,
            accessToken,
            refreshToken,
          };
        }

        // LEGACY FLOW (separate user object)
        const { token, user: rawArr } = data;
        const rawData: RawUser = rawArr[0].user_data;

        const ability = buildAbility(rawData);

        return {
          id: rawData.user_id,
          name: rawData.user_name,
          roles: Array.from(ability.tags),
          ability,
          accessToken: token.accessToken,
          refreshToken: token.refreshToken,
        };
      },
    }),
  ],

  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.roles = user.roles;
        token.ability = user.ability;
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
      }
      return token;
    },
    async session({ session, token }) {
      session.user = {
        ...session.user!,
        id: token.id as string,
        name: token.name as string,
        roles: token.roles as string[],
      };
      session.ability = token.ability as Ability;
      session.accessToken = token.accessToken as string;
      session.refreshToken = token.refreshToken as string;
      return session;
    },
  },

  pages: { signIn: '/sign-in' },
  secret: process.env.NEXTAUTH_SECRET,
};
