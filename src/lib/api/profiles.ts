export type PermissionLevel = 'none' | 'own' | 'team' | 'all';
export type PermissionResource =
  | 'crm.matriculas'
  | 'crm.leads'
  | 'crm.contactos'
  | 'crm.oportunidades'
  | 'crm.relatorios'
  | 'crm.campanhas'
  | 'gestao.acoes.catalogo'
  | 'gestao.acoes.sessoes'
  | 'gestao.acoes.instrutores'
  | 'gestao.acoes.avaliacoes'
  | 'gestao.perfis.criar'
  | 'gestao.perfis.editar'
  | 'gestao.perfis.atribuir'
  | 'gestao.utilizadores.listar'
  | 'gestao.utilizadores.convidar'
  | 'gestao.utilizadores.reposenha'
  | 'gestao.contacto.clientes'
  | 'gestao.contacto.fornecedores'
  | 'gestao.contacto.parceiros'
  | 'gesmat.faturacao'
  | 'gesmat.matriculas'
  | 'gesmat.validacoes'
  | 'gesmat.cobrancas'
  | 'gesmat.ocorrencias'
  | 'gesmat.reembolsos'
  | 'gesmat.descontos'
  | 'gesped.avaliacoes'
  | 'gesped.planos'
  | 'gesped.turmas'
  | 'gesped.documentos'
  | 'gesped.calendario'
  | 'config.definicoes.geral'
  | 'config.definicoes.integracoes'
  | 'config.definicoes.emails'
  | 'config.definicoes.idiomas'
  | 'config.definicoes.tema'
  | 'config.documentos.modelos'
  | 'config.documentos.assinaturas'
  | 'config.documentos.versionamento'
  | 'config.tarefas.modelos'
  | 'config.tarefas.automatizacoes'
  | 'config.tarefas.notificacoes'
  | 'config.agenda.eventos'
  | 'config.agenda.feriados'
  | 'config.agenda.disponibilidade';
export type PermissionType = 'ver' | 'criar' | 'apagar' | 'editar';
export type PermissionCode = `${PermissionResource}.${PermissionType}`;

export interface ProfileRow {
  id: string;
  name: string;
  permissions: Partial<Record<PermissionCode, PermissionLevel>> | '*';
}

export interface ProfileTableRow {
  id: string;
  name: string;
  modules: boolean;
  management: boolean;
  config: boolean;
}

export async function fetchProfiles(): Promise<ProfileRow[]> {
  return [
    {
      id: '1',
      name: 'Administrador',
      permissions: '*', // super-admin
    },
    {
      id: '2',
      name: 'Gestor CRM',
      permissions: {
        'crm.matriculas.ver': 'team',
        'crm.matriculas.criar': 'team',
        'crm.matriculas.editar': 'team',
        'crm.matriculas.apagar': 'none',
        'crm.leads.ver': 'all',
        'crm.leads.criar': 'team',
        'crm.leads.editar': 'team',
      },
    },
    {
      id: '3',
      name: 'Financeiro',
      permissions: {
        'crm.matriculas.ver': 'all',
        'crm.matriculas.editar': 'team',
      },
    },
  ];
}
