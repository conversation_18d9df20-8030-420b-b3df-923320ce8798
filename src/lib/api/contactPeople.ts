import { ContactPersonType, SelectOptions } from '@/types/contactUser';

export async function fetchContactPeople(): Promise<ContactPersonType[]> {
  return [
    {
      id: '39848',
      name: '<PERSON><PERSON><PERSON>',
      email: 'filip<PERSON><EMAIL>',
      mobile: '*********',
      phone: '*********',
      nif: '*********',
      socialSecurityNumber: '*********',
      identificationType: 'Passport',
      identificationValidity: new Date('2030-12-31'),
      identificationNumber: 'X1234567',
      identificationFile: 'cc',
      address: '123 Main Street',
      postalCode: '1000-001',
      locality: 'Lisboa',
      district: 'Lisboa',
      municipality: 'municipality-1',
      parish: 'parish-1',
      country: 'Portugal',
      birthPlace: 'Porto',
      profession: 'Auxiliar de reabilitação',
      employerEntity: '9517532468',
      contractType: 'contractType-1',
      birthDate: new Date('1990-05-15'),
      gender: 'gender-1',
      iban: '*************************',
      educationLevel: 'educationLevel-1',
      notes: 'Test user for demo purposes.',
      clientId: '2019/0739',
      rgpd: {
        consent_1: true,
        consent_2: true,
        consent_3: true,
        consent_4: false,
        consent_5: true,
      },
      relationships: {
        '111555': {
          user: 'a1b2c3d4-e5f6-7890-abcd-1234567890ef',
          relationshipType: 'yyy',
        },
      },
      entities: [{ id: 'yyyyyyy', name: 'Casapia', description: 'sem descricao' }],
      attachments: {},
      crmHistory: [
        {
          category: 'OPPORTUNITY',
          id: 'opportunity-1',
          description: 'xxx',
          type: 'opportunities',
          state: 'eee',
          madeBy: '58881',
          creationDate: new Date('2023-10-01'),
          enrollment: 'Initial contact made.',
        },
        {
          category: 'OPPORTUNITY',
          id: 'opportunity-2',
          description: 'yyy',
          type: 'leads',
          state: 'fff',
          madeBy: '58882',
          creationDate: new Date('2023-11-15'),
          enrollment: 'Follow-up email sent.',
        },
        {
          category: 'INTERACTION_SYSTEM',
          id: '1',
          description: 'interaction system bruv',
          type: 'opportunities-extra',
          state: 'ggg',
          madeBy: '58883',
          creationDate: new Date('2023-12-05'),
          enrollment: 'Scheduled a meeting.',
        },
        {
          category: 'INTERACTION_OPPORTUNITY',
          id: '2',
          description: 'interaction opportunity bruv',
          type: '',
          state: 'ggg',
          madeBy: '58883',
          creationDate: new Date('2023-12-05'),
          enrollment: 'Scheduled a meeting.',
          extraData: '2',
        },
        {
          category: 'INTERACTION_EXTERNAL',
          id: '2',
          description: 'interaction external bruv',
          type: '',
          state: 'ggg',
          madeBy: '58883',
          creationDate: new Date('2023-12-05'),
          enrollment: 'Scheduled a meeting.',
        },
      ],
      enrollments: {},
    },
    {
      id: '39849',
      name: 'João Silva',
      email: '<EMAIL>',
      mobile: '923456789',
      phone: '223456789',
      nif: '123456789',
      socialSecurityNumber: '123456789',
      identificationType: 'Citizen Card',
      identificationValidity: new Date('2028-07-20'),
      identificationNumber: 'Y7654321',
      identificationFile: 'cc',
      address: '456 Side Avenue',
      postalCode: '2000-002',
      locality: 'Porto',
      district: 'Porto',
      municipality: 'municipality-1',
      parish: 'parish-1',
      country: 'Portugal',
      birthPlace: 'Lisbon',
      profession: 'Product Manager',
      employerEntity: 'Innovatech SA',
      contractType: 'contractType-1',
      birthDate: new Date('1985-11-23'),
      gender: 'gender-1',
      iban: '*************************',
      educationLevel: 'educationLevel-1',
      notes: 'Second test user for demo.',
      clientId: '2020/0123',
      rgpd: {
        consent_1: true,
        consent_2: false,
        consent_3: true,
        consent_4: true,
        consent_5: false,
      },
      relationships: {
        '111555': {
          user: 'f92a1c6e-3b47-4f10-bd9c-2b7c5c4a1e9b',
          relationshipType: 'yyy',
        },
      },
      entities: [{ id: 'wwwwwww', name: 'Inova', description: 'sem descricao' }],
      attachments: {},
      crmHistory: [],
      enrollments: {},
    },
    {
      id: '39850',
      name: 'Maria Santos',
      email: '<EMAIL>',
      mobile: '934567890',
      phone: '234567890',
      nif: '456789123',
      socialSecurityNumber: '456789123',
      identificationType: 'Citizen Card',
      identificationValidity: new Date('2029-03-15'),
      identificationNumber: 'Z9876543',
      identificationFile: 'cc',
      address: '789 Oak Street',
      postalCode: '3000-003',
      locality: 'Coimbra',
      district: 'Coimbra',
      municipality: 'municipality-1',
      parish: 'parish-1',
      country: 'Portugal',
      birthPlace: 'Coimbra',
      profession: 'Software Engineer',
      employerEntity: 'TechCorp',
      contractType: 'contractType-1',
      birthDate: new Date('1988-07-10'),
      gender: 'gender-1',
      iban: '*************************',
      educationLevel: 'educationLevel-1',
      notes: 'Third test user for demo.',
      clientId: '2021/0456',
      rgpd: {
        consent_1: true,
        consent_2: true,
        consent_3: false,
        consent_4: true,
        consent_5: true,
      },
      relationships: {},
      entities: [],
      attachments: {},
      crmHistory: [],
      enrollments: {},
    },
    {
      id: '39851',
      name: 'Pedro Costa',
      email: '<EMAIL>',
      mobile: '*********',
      phone: '*********',
      nif: '*********',
      socialSecurityNumber: '*********',
      identificationType: 'Passport',
      identificationValidity: new Date('2031-08-22'),
      identificationNumber: 'A1234567',
      identificationFile: 'cc',
      address: '321 Pine Avenue',
      postalCode: '4000-004',
      locality: 'Braga',
      district: 'Braga',
      municipality: 'municipality-1',
      parish: 'parish-1',
      country: 'Portugal',
      birthPlace: 'Braga',
      profession: 'Marketing Manager',
      employerEntity: 'MarketingPro',
      contractType: 'contractType-1',
      birthDate: new Date('1983-12-05'),
      gender: 'gender-1',
      iban: '*************************',
      educationLevel: 'educationLevel-1',
      notes: 'Fourth test user for demo.',
      clientId: '2022/0789',
      rgpd: {
        consent_1: false,
        consent_2: true,
        consent_3: true,
        consent_4: false,
        consent_5: true,
      },
      relationships: {},
      entities: [],
      attachments: {},
      crmHistory: [],
      enrollments: {},
    },
  ];
}

export async function fetchContactPeopleList(): Promise<Array<{ id: string; name: string }>> {
  const users = await fetchContactPeople();
  return users.map((user: ContactPersonType) => ({
    id: user.id,
    name: user.name || ' - ',
  }));
}

export async function fetchContactPeopleOptions(): Promise<SelectOptions> {
  return {
    district: [{ value: 'district-1', label: 'Lisbon' }],
    municipality: [{ value: 'municipality-1', label: 'Lisbon' }],
    parish: [{ value: 'parish-1', label: 'Santo António' }],
    country: [{ value: 'country-1', label: 'Portugal' }],
    contractType: [{ value: 'contractType-1', label: 'Full-time' }],
    gender: [{ value: 'gender-1', label: 'Male' }],
    educationLevel: [{ value: 'educationLevel-1', label: "Master's Degree" }],
    relationshipTypes: [
      { label: 'Pai', value: 'xxx' },
      { label: 'Mãe', value: 'yyy' },
    ],
  };
}

export async function fetchRelationshipType(): Promise<any | null> {
  return [
    { label: 'Pai', value: 'xxx' },
    { label: 'Mãe', value: 'yyy' },
  ];
}

export async function fetchFileTypes(): Promise<any | null> {
  return [
    { label: 'Texto', value: 'text' },
    { label: 'Imagem', value: 'image' },
    { label: 'Pdf', value: 'pdf' },
    { label: 'Excel', value: 'excel' },
  ];
}

export async function fetchCenters() {
  return ['Coimbra', 'Lisboa', 'Faro', 'Braga', 'Porto'];
}
