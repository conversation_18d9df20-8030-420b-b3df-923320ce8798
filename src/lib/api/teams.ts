export interface TeamRow {
  id: string;
  name: string;
  pole: string;
  assignedUsers: number;
}

export interface TeamTableRow {
  id: string;
  name: string;
  pole: string;
  assignedUsers: number;
}

export interface User {
  id: string;
  name: string;
  role: string;
}

export interface NewTeamFormData {
  name: string;
  pole: string;
  assignedUsers: string[];
}

export async function fetchTeams(): Promise<TeamRow[]> {
  return [
    {
      id: '1',
      name: 'Departamento comercial',
      pole: '<PERSON><PERSON>',
      assignedUsers: 8,
    },
    {
      id: '2',
      name: 'Departamento comercial',
      pole: '<PERSON><PERSON><PERSON>',
      assignedUsers: 9,
    },
    {
      id: '3',
      name: 'Departamento comercial',
      pole: 'Beja',
      assignedUsers: 10,
    },
    {
      id: '4',
      name: 'Departamento comercial',
      pole: 'Braga',
      assignedUsers: 2,
    },
    {
      id: '5',
      name: 'Departamento comercial',
      pole: '<PERSON><PERSON><PERSON><PERSON>',
      assignedUsers: 8,
    },
    {
      id: '6',
      name: 'Departamento comercial',
      pole: '<PERSON><PERSON>',
      assignedUsers: 12,
    },
    {
      id: '7',
      name: 'Departamento comercial',
      pole: 'Co<PERSON><PERSON>',
      assignedUsers: 6,
    },
    {
      id: '8',
      name: 'Departamento comercial',
      pole: 'Funchal',
      assignedUsers: 15,
    },
  ];
}

export async function fetchUsers(): Promise<User[]> {
  return [
    { id: '1', name: 'Joana Silva', role: 'Administrativo' },
    { id: '2', name: 'Liliana Campo', role: 'Comercial' },
    { id: '3', name: 'Alexandra Ferreira', role: 'Coordenação Pedagógica' },
    { id: '4', name: 'Tomás Santos', role: 'Administrativo - Financeiro' },
    { id: '5', name: 'Rita Costa', role: 'Administrativo' },
    { id: '6', name: 'Miguel Oliveira', role: 'Comercial' },
    { id: '7', name: 'Ana Martins', role: 'Coordenação Pedagógica' },
    { id: '8', name: 'Pedro Rodrigues', role: 'Administrativo' },
    { id: '9', name: 'Sofia Lima', role: 'Comercial' },
    { id: '10', name: 'Carlos Ferreira', role: 'Administrativo - Financeiro' },
    { id: '11', name: 'Maria Santos', role: 'Administrativo' },
    { id: '12', name: 'João Silva', role: 'Comercial' },
    { id: '13', name: 'Teresa Oliveira', role: 'Coordenação Pedagógica' },
    { id: '14', name: 'António Costa', role: 'Administrativo' },
    { id: '15', name: 'Isabel Martins', role: 'Comercial' },
    { id: '16', name: 'Francisco Rodrigues', role: 'Administrativo - Financeiro' },
    { id: '17', name: 'Catarina Lima', role: 'Administrativo' },
    { id: '18', name: 'Manuel Ferreira', role: 'Comercial' },
    { id: '19', name: 'Inês Santos', role: 'Coordenação Pedagógica' },
    { id: '20', name: 'Diogo Silva', role: 'Administrativo' },
    { id: '21', name: 'Beatriz Oliveira', role: 'Comercial' },
    { id: '22', name: 'Gonçalo Costa', role: 'Administrativo - Financeiro' },
    { id: '23', name: 'Mariana Martins', role: 'Administrativo' },
    { id: '24', name: 'Tiago Rodrigues', role: 'Comercial' },
    { id: '25', name: 'Carolina Lima', role: 'Coordenação Pedagógica' },
    { id: '26', name: 'Rafael Ferreira', role: 'Administrativo' },
    { id: '27', name: 'Leonor Santos', role: 'Comercial' },
    { id: '28', name: 'Duarte Silva', role: 'Administrativo - Financeiro' },
    { id: '29', name: 'Matilde Oliveira', role: 'Administrativo' },
  ];
}

export async function fetchPoles(): Promise<{ value: string; label: string }[]> {
  return [
    { value: 'almada', label: 'Almada' },
    { value: 'alverca', label: 'Alverca' },
    { value: 'beja', label: 'Beja' },
    { value: 'braga', label: 'Braga' },
    { value: 'carcavelos', label: 'Carcavelos' },
    { value: 'chaves', label: 'Chaves' },
    { value: 'coimbra', label: 'Coimbra' },
    { value: 'funchal', label: 'Funchal' },
    { value: 'lisboa', label: 'Lisboa' },
    { value: 'porto', label: 'Porto' },
  ];
}

// Mock function to get assigned users for a specific team
export async function fetchTeamAssignedUsers(teamId: string): Promise<string[]> {
  // Simulate different assigned users for different teams
  const teamAssignments: Record<string, string[]> = {
    '1': ['1', '2', '3', '4', '5', '6', '7', '8'], // Almada team
    '2': ['9', '10', '11', '12', '13', '14', '15', '16', '17'], // Alverca team
    '3': ['18', '19', '20', '21', '22', '23', '24', '25', '26', '27'], // Beja team
    '4': ['28', '29'], // Braga team
    '5': ['1', '2', '3', '4', '5', '6', '7', '8'], // Carcavelos team
    '6': ['9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'], // Chaves team
    '7': ['21', '22', '23', '24', '25', '26'], // Coimbra team
    '8': ['27', '28', '29', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], // Funchal team
  };

  return teamAssignments[teamId] || [];
}
