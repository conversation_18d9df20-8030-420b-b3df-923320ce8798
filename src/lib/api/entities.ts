import { SelectOption } from '@/types/contactUser';
import { EntityType } from '@/schemas/entitySchema';

export async function fetchEmployerEntities(): Promise<SelectOption[]> {
  return [
    { value: '9517532468', label: 'Santa casa' },
    { value: '1234567890', label: 'Casa santa' },
  ];
}

export async function fetchEntities(): Promise<EntityType[]> {
  return [
    {
      id: '1',
      name: 'Empresa Alpha',
      email: '<EMAIL>',
      phone: '912345678',
      country: 'PT',
      distrito: 'Lisboa',
      nif: '123456789',
      website: 'https://alpha.pt',
      location: 'Lisboa',
      concelho: 'Lisboa',
      freguesia: 'Santo António',
      capital_social: '100000',
      type: 'cliente_formacao',
      rgpd: JSON.stringify({
        consent_1: true,
        consent_2: true,
        consent_3: false,
        consent_4: true,
        consent_5: false,
      }),
      observations: 'Empresa líder no setor tecnológico',
      protocolType: 'pratical',
      addresses: JSON.stringify([]),
    },
    {
      id: '2',
      name: '<PERSON> Lda',
      email: '<EMAIL>',
      phone: '987654321',
      country: 'PT',
      distrito: 'Porto',
      nif: '987654321',
      website: 'https://beta.pt',
      location: 'Porto',
      concelho: 'Porto',
      freguesia: 'Miragaia',
      capital_social: '50000',
      type: 'cliente_faturacao',
      rgpd: JSON.stringify({
        consent_1: true,
        consent_2: true,
        consent_3: false,
        consent_4: true,
        consent_5: false,
      }),
      observations: 'Empresa de consultoria',
      protocolType: 'internship',
      addresses: JSON.stringify([]),
    },
    {
      id: '3',
      name: 'Gamma Solutions',
      email: '<EMAIL>',
      phone: '934567890',
      country: 'PT',
      distrito: 'Coimbra',
      nif: '456789123',
      website: 'https://gamma.pt',
      location: 'Coimbra',
      concelho: 'Coimbra',
      freguesia: 'Sé Nova',
      capital_social: '75000',
      type: 'entidade_empregadora',
      rgpd: JSON.stringify({
        consent_1: true,
        consent_2: true,
        consent_3: false,
        consent_4: true,
        consent_5: false,
      }),
      observations: 'Empresa de desenvolvimento de software',
      protocolType: 'pratical',
      addresses: JSON.stringify([]),
    },
    {
      id: '4',
      name: 'Delta Partners',
      email: '<EMAIL>',
      phone: '945678901',
      country: 'PT',
      distrito: 'Braga',
      nif: '789123456',
      website: 'https://delta.pt',
      location: 'Braga',
      concelho: 'Braga',
      freguesia: 'São João do Souto',
      capital_social: '120000',
      type: 'parceria',
      rgpd: JSON.stringify({
        consent_1: true,
        consent_2: true,
        consent_3: false,
        consent_4: true,
        consent_5: false,
      }),
      observations: 'Empresa de parcerias estratégicas',
      protocolType: 'internship',
      addresses: JSON.stringify([]),
    },
  ];
}
