import 'server-only';
import { loadAsync } from 'jszip';
import { cacheTag } from 'next/dist/server/use-cache/cache-tag';
import <PERSON> from 'papaparse';

const GEONAMES_ZIP_URL = 'https://download.geonames.org/export/zip/PT.zip';

export type Localidade = {
  postalCode: string;
  freguesia: string;
  concelho: string;
  distrito: string;
};

export async function fetchGeoInfo() {
  'use cache';
  cacheTag('forever');

  const res = await fetch(GEONAMES_ZIP_URL, {
    cache: 'force-cache',
  });

  if (!res.ok) {
    throw new Error('Failed to fetch geo data');
  }

  const buffer = await res.arrayBuffer();

  const zip = await loadAsync(buffer);

  const file = zip.file('PT.txt');

  if (!file) {
    throw new Error('Failed to extract geo zip');
  }

  const fileData = await file.async('text');

  const parsedData = Papa.parse<string[]>(fileData, {
    header: false,
    skipEmptyLines: true,
    delimiter: '\t',
  });

  if (parsedData.errors.length) {
    console.error(parsedData.errors.map((e) => e.message));
    throw new Error('Failed to parse csv file');
  }

  return parsedData.data.map<Localidade>((d) => ({
    //country: d[0],
    postalCode: d[1],
    //localidade: d[2],
    distrito: d[3],
    freguesia: d[7],
    concelho: d[5],
  }));
}
