import { Ability, CrudOp, PermLvl, RawUser } from '../types/ability';

/* ---------- Modelos ---------- */
type AbilitySource = {
  // novo fluxo
  roles: string[];
  functionalities: string[];
};

/* ---------- Helpers ---------- */
const CRUD_OPS: CrudOp[] = ['create', 'read', 'update', 'delete'];

function isRawUser(obj: RawUser | AbilitySource): obj is RawUser {
  // se 1.º item de roles for objeto, é o modelo antigo
  return Array.isArray(obj?.roles) && typeof obj.roles[0] === 'object';
}

//todo implement
export function can(roles: string[], permission: string) {
  return true;
}

/* ---------- Build ---------- */
export function buildAbility(src: RawUser | AbilitySource): Ability {
  /* coleções mutáveis */
  const modules = new Set<string>();
  const tags = new Set<string>();
  const grants: Record<string, PermLvl> = {};

  /* ======== RAMO ANTIGO ======== */
  if (isRawUser(src)) {
    src.roles.forEach((role) => {
      tags.add(role.role.toUpperCase());

      role.subroles.forEach((sr) => {
        tags.add(sr.subrole_code.toUpperCase());

        sr.functionalities.forEach((fn) => {
          const mod = fn.functionality_module.toUpperCase();
          modules.add(mod);

          fn.permissions.forEach((p) => {
            const lvl = (p.permission_value === 'null' ? 'none' : p.permission_value) as PermLvl;
            grants[`${mod}.${fn.functionality_code.toUpperCase()}.${p.permission_type}`] = lvl;
          });
        });
      });
    });

    return { modules, tags, grants };
  }

  /* ======== RAMO NOVO ======== */
  src.roles.forEach((r) => {
    const [role, subrole] = r.toUpperCase().split(':');
    tags.add(role);
    if (subrole) tags.add(subrole);
  });

  src.functionalities.forEach((fn) => {
    const fnCode = fn.toUpperCase(); // ex. USER_MANAGEMENT
    const mod = fnCode.split('_')[0]; // USER
    modules.add(mod);

    CRUD_OPS.forEach((op) => {
      grants[`${mod}.${fnCode}.${op}`] = 'all';
    });
  });

  return { modules, tags, grants };
}
