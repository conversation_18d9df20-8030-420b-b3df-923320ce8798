import type { PermissionCode } from './permissions.ts';

export interface RoleDefinition {
  label: string;
  permissions: PermissionCode[] | '*';
  isSuper?: boolean;
}

export const roles: Record<string, RoleDefinition> = {
  ADMIN: { label: 'Administrator', permissions: '*', isSuper: true },
  SALES_MANAGER: {
    label: 'Sales Manager',
    permissions: ['LEADS_VIEW', 'LEADS_EDIT', 'OPPORTUNITIES_ADD', 'OPPORTUNITIES_EDIT'],
  },
};
