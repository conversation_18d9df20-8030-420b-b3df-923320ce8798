export type CrudOp = 'create' | 'read' | 'update' | 'delete';
export type PermLvl = 'none' | 'own' | 'team' | 'all';

/** exatamente o que vem do backend */
export interface RawPermission {
  permission_type: CrudOp; // «create», «read»…
  permission_value: PermLvl | 'null'; // "null" == nenhum
}

export interface RawFunctionality {
  functionality_id: string;
  functionality_code: string; // «OPPORTUNITIES»
  functionality_module: string; // «CRM»
  permissions: RawPermission[];
}

export interface RawSubRole {
  subrole_id: string;
  subrole_code: string; // «COMERCIAL»
  functionalities: RawFunctionality[];
}

export interface RawRole {
  role_id: string;
  role: string; // «SA-FO»
  subroles: RawSubRole[];
}

export interface RawUser {
  user_id: string;
  user_name: string;
  roles: RawRole[];
}

export interface Ability {
  /** Módulos que o usuário *pode ver no menu* */
  modules: Set<string>; // «CRM», «GESPED»…

  /**
   * Mapa rápido de permissões:
   * key = `${MODULE}.${FUNC}.${OP}`
   * val = nível de acesso ('none' | 'own' | 'team' | 'all')
   */
  grants: Record<string, PermLvl>;

  /** rótulos flat para RBAC simples (roles + subroles) */
  tags: Set<string>; // «SA-FO», «COMERCIAL»…

  /**
   * test if tem pelo menos minLvl
   * ex: ability.can('read', 'CRM', 'LEADS', 'own')
   */
}

export type RouteRule = {
  op: CrudOp; // 'read' | 'update' …
  mod: string; // 'CRM'
  func: string; // 'LEADS'
  minLvl?: PermLvl; // opcional: 'team' | 'all'
};
