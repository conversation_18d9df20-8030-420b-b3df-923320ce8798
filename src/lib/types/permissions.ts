import permissions from '@/data/permissions.json';
export type PermissionLevel = 'semAcesso' | 'proprio' | 'equipa' | 'todos';

export type PermissionAction = 'ver' | 'criar' | 'editar' | 'apagar'; // acrescenta outras do design

export interface APIPermission {
  functionality_id: string;
  functionality: string; // ex.: "Leads"
  is_inactive: boolean;
  permission_type: PermissionAction; // cuidado: no JSON vêm “permission**s**_type” em alguns nós
  permissions_value: PermissionLevel;
}

/** Estrutura vinda do servidor */
export interface APIProfile {
  id: string;
  name: string;
  permissions: APIPermission[];
}
/** Cada string vinda do JSON vira uma PermissionCode  */
export type PermissionCode = (typeof permissions)[number];

/** Perfil completo */
