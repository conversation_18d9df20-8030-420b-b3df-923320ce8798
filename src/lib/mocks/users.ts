export type User = {
  id: string;
  name?: string;
  username?: string;
  email?: string;
  language?: string;
  profile?: string;
  status?: string;
  hubs?: string[];
};

export const mockUsers: User[] = [
  {
    id: '58880',
    name: '<PERSON><PERSON><PERSON>',
    username: 'filipa.castro',
    email: 'filip<PERSON><EMAIL>',
    language: 'Portuguê<PERSON>',
    profile: 'Administra<PERSON>',
    status: 'Ativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58881',
    name: '<PERSON>',
    username: 'bruno.silva',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Supervisor Comercial',
    status: 'Inativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58882',
    name: '<PERSON>',
    username: 'carla.mendes',
    email: '<EMAIL>',
    language: 'Portuguê<PERSON>',
    profile: 'Administrador',
    status: 'Ativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58883',
    name: '<PERSON><PERSON>',
    username: 'diogo.ferraz',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Supervisor Comercial',
    status: 'Inativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58884',
    name: '<PERSON>a <PERSON>',
    username: 'eduarda.lima',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Administrador',
    status: 'Ativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58885',
    name: 'Fábio Rocha',
    username: 'fabio.rocha',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Supervisor Comercial',
    status: 'Inativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58886',
    name: 'Gabriela Sousa',
    username: 'gabriela.sousa',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Administrador',
    status: 'Ativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58887',
    name: 'Henrique Alves',
    username: 'henrique.alves',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Supervisor Comercial',
    status: 'Inativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58888',
    name: 'Inês Faria',
    username: 'ines.faria',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Administrador',
    status: 'Ativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
  {
    id: '58889',
    name: 'João Costa',
    username: 'joao.costa',
    email: '<EMAIL>',
    language: 'Português',
    profile: 'Supervisor Comercial',
    status: 'Inativo',
    hubs: ['opt1', 'opt2', 'opt3'],
  },
];
