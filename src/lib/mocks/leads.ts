export type DuplicateContact = {
  id: string;
  nome: string;
  email?: string;
  contato?: string;
  nif?: string;
  criadoPor: string;
};

export type Lead = {
  id: string;
  estado: string;
  createdAt: string;
  nome: string;
  email: string;
  contato: string;
  polo: string;
  curso: string;
  atribuido: string;
  duplicateContacts?: DuplicateContact[];
};

export const mockLeads: Lead[] = [
  {
    id: '58880',
    estado: 'Nova',
    createdAt: '2025-07-07T18:21:00Z',
    nome: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    contato: '917470173',
    polo: 'Lis<PERSON>',
    curso: 'Auxiliar de reabilitação',
    atribuido: '<PERSON><PERSON>',
    duplicateContacts: [
      {
        id: '29485',
        nome: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        contato: '917470173',
        nif: '210683285',
        criadoPor: '<PERSON>',
      },
      {
        id: '29486',
        nome: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        contato: '917470173',
        nif: '210683285',
        criadoPor: '<PERSON> <PERSON>valho',
      },
      {
        id: '29487',
        nome: 'Filipa Castro',
        email: '<EMAIL>',
        contato: '917470173',
        nif: '210683285',
        criadoPor: 'Maria Carvalho',
      },
    ],
  },
  {
    id: '58881',
    estado: 'Atribuída',
    createdAt: '2025-07-07T18:22:00Z',
    nome: 'Bruno Silva',
    email: '<EMAIL>',
    contato: '912345678',
    polo: 'Porto',
    curso: 'Técnico de informática',
    atribuido: 'Miguel Azevedo',
  },
  {
    id: '58882',
    estado: 'Fechada c/ sucesso',
    createdAt: '2025-07-07T18:25:00Z',
    nome: 'Carla Mendes',
    email: '<EMAIL>',
    contato: '934567890',
    polo: 'Coimbra',
    curso: 'Enfermagem',
    atribuido: 'Joana Pereira',
  },
  {
    id: '58883',
    estado: 'Fechada s/ sucesso',
    createdAt: '2025-07-07T18:30:00Z',
    nome: 'Diogo Ferraz',
    email: '<EMAIL>',
    contato: '926543210',
    polo: 'Lisboa',
    curso: 'Marketing Digital',
    atribuido: 'Rita Gomes',
  },
  {
    id: '58884',
    estado: 'Anulada',
    createdAt: '2025-07-08T09:10:00Z',
    nome: 'Eduarda Lima',
    email: '<EMAIL>',
    contato: '938887766',
    polo: 'Faro',
    curso: 'Turismo',
    atribuido: 'Joana Pereira',
  },
  {
    id: '58885',
    estado: 'Em qualificação',
    createdAt: '2025-07-08T09:12:00Z',
    nome: 'Fábio Rocha',
    email: '<EMAIL>',
    contato: '915556677',
    polo: 'Braga',
    curso: 'Gestão de RH',
    atribuido: 'Miguel Azevedo',
  },
  {
    id: '58886',
    estado: 'Em validação',
    createdAt: '2025-07-08T09:15:00Z',
    nome: 'Gabriela Sousa',
    email: '<EMAIL>',
    contato: '917112233',
    polo: 'Lisboa',
    curso: 'Design Gráfico',
    atribuido: 'Rita Gomes',
  },
  {
    id: '58887',
    estado: 'Atribuída',
    createdAt: '2025-07-08T09:17:00Z',
    nome: 'Henrique Alves',
    email: '<EMAIL>',
    contato: '914998877',
    polo: 'Porto',
    curso: 'Engenharia Civil',
    atribuido: 'Joana Pereira',
  },
  {
    id: '58888',
    estado: 'Nova',
    createdAt: '2025-07-08T09:20:00Z',
    nome: 'Inês Faria',
    email: '<EMAIL>',
    contato: '931223344',
    polo: 'Coimbra',
    curso: 'Arquitetura',
    atribuido: 'Miguel Azevedo',
  },
  {
    id: '58889',
    estado: 'Em qualificação',
    createdAt: '2025-07-08T09:25:00Z',
    nome: 'João Costa',
    email: '<EMAIL>',
    contato: '936554433',
    polo: 'Lisboa',
    curso: 'Ciência de Dados',
    atribuido: 'Rita Gomes',
  },
];
