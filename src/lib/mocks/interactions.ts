import { addDays } from 'date-fns';
import { InteractionTableType } from '../../schemas/interactionSchema';
import { ContactPersonType as ContactUserType } from '../../types/contactUser';

export const interactionMocks: InteractionTableType[] = [
  {
    id: '0',
    description: 'idk',
    type: 'Chamada Telefónica',
    state: 'Agendado',
    scheduledDate: new Date(),
    doneDate: addDays(new Date(), 2),
    doneBy: 'Ana Vasconcelos',
    enrollment: 'idk?',
  },
  {
    id: '2',
    description: 'idk',
    type: 'Chamada Telefónica',
    state: 'Agendado',
    scheduledDate: new Date(),
    doneDate: addDays(new Date(), 2),
    doneBy: 'Ana Vasconcelos',
    enrollment: 'idk?',
  },
];

// Mock system interactions
export const systemInteractionMocks = [
  {
    id: 'sys-001',
    description: 'Atualização automática de dados do contacto',
    type: 'Atualização de Sistema',
    state: 'Concluído',
    scheduledDate: new Date('2024-01-15T10:00:00'),
    doneDate: new Date('2024-01-15T10:05:00'),
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactUserType,
    enrollment: 'ENR-2024-001',
    text: 'O sistema atualizou automaticamente os dados do contacto com base nas informações recebidas do sistema externo. Foram atualizados os campos: telefone, morada e código postal.',
    createdAt: new Date('2024-01-15T10:00:00'),
  },
  {
    id: 'sys-002',
    description: 'Sincronização de dados com sistema externo',
    type: 'Sincronização',
    state: 'Concluído',
    scheduledDate: new Date('2024-01-20T14:30:00'),
    doneDate: new Date('2024-01-20T14:32:00'),
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactUserType,
    enrollment: 'ENR-2024-002',
    text: 'Sincronização bem-sucedida com o sistema externo. Foram importados novos dados de matrícula e atualizadas as informações de pagamento.',
    createdAt: new Date('2024-01-20T14:30:00'),
  },
  {
    id: 'sys-003',
    description: 'Validação automática de documentos',
    type: 'Validação',
    state: 'Erro',
    scheduledDate: new Date('2024-01-25T09:15:00'),
    doneDate: new Date('2024-01-25T09:20:00'),
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactUserType,
    enrollment: 'ENR-2024-003',
    text: 'Erro na validação automática dos documentos. O documento de identificação não pôde ser verificado automaticamente. Requer intervenção manual.',
    createdAt: new Date('2024-01-25T09:15:00'),
  },
  {
    id: 'sys-004',
    description: 'Backup automático de dados',
    type: 'Backup',
    state: 'Concluído',
    scheduledDate: new Date('2024-02-01T02:00:00'),
    doneDate: new Date('2024-02-01T02:15:00'),
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactUserType,
    enrollment: 'ENR-2024-004',
    text: 'Backup automático dos dados do contacto realizado com sucesso. Todos os dados foram salvaguardados no sistema de backup.',
    createdAt: new Date('2024-02-01T02:00:00'),
  },
  {
    id: 'sys-005',
    description: 'Notificação automática de vencimento',
    type: 'Notificação',
    state: 'Enviado',
    scheduledDate: new Date('2024-02-05T08:00:00'),
    doneDate: new Date('2024-02-05T08:01:00'),
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactUserType,
    enrollment: 'ENR-2024-005',
    text: 'Notificação automática enviada ao contacto sobre o vencimento próximo do documento de identificação. Email enviado com sucesso.',
    createdAt: new Date('2024-02-05T08:00:00'),
  },
  {
    id: 'sys-006',
    description: 'Atualização de estado de matrícula',
    type: 'Atualização',
    state: 'Processando',
    scheduledDate: new Date('2024-02-10T16:30:00'),
    doneDate: new Date('2024-02-10T16:35:00'),
    doneBy: {
      id: 'system',
      name: 'Sistema Automático',
      email: '<EMAIL>',
    } as ContactUserType,
    enrollment: 'ENR-2024-006',
    text: 'Atualização automática do estado da matrícula em curso. O sistema está a processar as alterações necessárias.',
    createdAt: new Date('2024-02-10T16:30:00'),
  },
];
