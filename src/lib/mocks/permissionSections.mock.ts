import { PermissionRowDef } from '@/app/[locale]/(protected)/dashboard/profiles/components/PermissionTable';
import { PermissionResource } from '../api/profiles';
export type PermissionAction = 'ver' | 'criar' | 'editar' | 'apagar';

export type PermissionCode = `${string}.${PermissionAction}`;
/** Utilitário para gerar o array Ver/Criar/Editar/Apagar */
const CRUD = (prefix: PermissionResource): PermissionRowDef[] => [
  { label: 'Ver', code: `${prefix}.ver` as const },
  { label: 'Criar', code: `${prefix}.criar` as const },
  { label: 'Editar', code: `${prefix}.editar` as const },
  { label: 'Apagar', code: `${prefix}.apagar` as const },
];

/**
 * Estrutura usada pelo ProfileForm:
 *
 * section → vários grupos (accordion pai)
 * grupo   → vários subAccordions (filhos) + descrição
 * feature → rows a injectar no <PermissionTable>
 */
export interface FeatureMock {
  key: string;
  label: string;
  rows: PermissionRowDef[];
}

export interface GroupMock {
  key: string;
  label: string;
  description: string;
  features: FeatureMock[];
}

export interface SectionMock {
  key: string;
  label: string;
  groups: GroupMock[];
}

export const permissionSections: SectionMock[] = [
  /* ──────────────────────── MÓDULOS ─────────────────────── */
  {
    key: 'modulos',
    label: 'Módulos',
    groups: [
      {
        key: 'crm',
        label: 'CRM',
        description: 'Customer Relationship Management',
        features: [
          { key: 'leads', label: 'Leads', rows: CRUD('crm.leads') },
          { key: 'oportunidades', label: 'Oportunidades', rows: CRUD('crm.oportunidades') },
          { key: 'contactos', label: 'Contactos', rows: CRUD('crm.contactos') },
          { key: 'relatorios', label: 'Relatórios', rows: CRUD('crm.relatorios') },
          { key: 'campanhas', label: 'Campanhas', rows: CRUD('crm.campanhas') },
        ],
      },
      {
        key: 'gesped',
        label: 'GesPed',
        description: 'Gestão Pedagógica',
        features: [
          { key: 'turmas', label: 'Turmas', rows: CRUD('gesped.turmas') },
          { key: 'planos', label: 'Planos Curriculares', rows: CRUD('gesped.planos') },
          { key: 'avaliacoes', label: 'Avaliações', rows: CRUD('gesped.avaliacoes') },
          { key: 'calendario', label: 'Calendário', rows: CRUD('gesped.calendario') },
          { key: 'documentos', label: 'Documentos', rows: CRUD('gesped.documentos') },
        ],
      },
      {
        key: 'gesmat',
        label: 'GesMat',
        description: 'Gestão de Matrículas',
        features: [
          { key: 'matriculas', label: 'Matrículas', rows: CRUD('gesmat.matriculas') },
          { key: 'faturacao', label: 'Faturação', rows: CRUD('gesmat.faturacao') },
          { key: 'validacoes', label: 'Validações Financeiras', rows: CRUD('gesmat.validacoes') },
          { key: 'cobrancas', label: 'Cobranças', rows: CRUD('gesmat.cobrancas') },
          { key: 'ocorrencias', label: 'Ocorrências', rows: CRUD('gesmat.ocorrencias') },
          { key: 'reembolsos', label: 'Reembolsos', rows: CRUD('gesmat.reembolsos') },
          { key: 'descontos', label: 'Descontos', rows: CRUD('gesmat.descontos') },
        ],
      },
    ],
  },

  /* ──────────────────────── GESTÃO ──────────────────────── */
  {
    key: 'gestao',
    label: 'Gestão',
    groups: [
      {
        key: 'acoesFormacao',
        label: 'Ações de Formação',
        description: 'Gestão das ações de formação',
        features: [
          { key: 'catalogo', label: 'Catálogo', rows: CRUD('gestao.acoes.catalogo') },
          { key: 'sessoes', label: 'Sessões', rows: CRUD('gestao.acoes.sessoes') },
          { key: 'instrutores', label: 'Instrutores', rows: CRUD('gestao.acoes.instrutores') },
          { key: 'avaliacoes', label: 'Avaliações', rows: CRUD('gestao.acoes.avaliacoes') },
        ],
      },
      {
        key: 'perfis',
        label: 'Perfis',
        description: 'Gestão de perfis e permissões',
        features: [
          { key: 'criar', label: 'Criar Perfil', rows: CRUD('gestao.perfis.criar') },
          { key: 'editar', label: 'Editar Perfil', rows: CRUD('gestao.perfis.editar') },
          { key: 'atribuir', label: 'Atribuir Permissões', rows: CRUD('gestao.perfis.atribuir') },
        ],
      },
      {
        key: 'utilizadores',
        label: 'Utilizadores',
        description: 'Gestão de utilizadores',
        features: [
          { key: 'listar', label: 'Listar Utilizadores', rows: CRUD('gestao.utilizadores.listar') },
          { key: 'convidar', label: 'Convidar', rows: CRUD('gestao.utilizadores.convidar') },
          { key: 'reposenha', label: 'Repor Senha', rows: CRUD('gestao.utilizadores.reposenha') },
        ],
      },
      {
        key: 'contacto',
        label: 'Contacto',
        description: 'Gestão de contactos',
        features: [
          { key: 'clientes', label: 'Clientes', rows: CRUD('gestao.contacto.clientes') },
          {
            key: 'fornecedores',
            label: 'Fornecedores',
            rows: CRUD('gestao.contacto.fornecedores'),
          },
          { key: 'parceiros', label: 'Parceiros', rows: CRUD('gestao.contacto.parceiros') },
        ],
      },
    ],
  },

  /* ──────────────────── CONFIGURAÇÕES ───────────────────── */
  {
    key: 'config',
    label: 'Configurações',
    groups: [
      {
        key: 'definicoes',
        label: 'Definições',
        description: 'Configurações gerais',
        features: [
          { key: 'geral', label: 'Geral', rows: CRUD('config.definicoes.geral') },
          { key: 'integracoes', label: 'Integrações', rows: CRUD('config.definicoes.integracoes') },
          { key: 'emails', label: 'Emails', rows: CRUD('config.definicoes.emails') },
          { key: 'idiomas', label: 'Idiomas', rows: CRUD('config.definicoes.idiomas') },
          { key: 'tema', label: 'Tema', rows: CRUD('config.definicoes.tema') },
        ],
      },
      {
        key: 'documentos',
        label: 'Documentos',
        description: 'Modelos e versionamento',
        features: [
          { key: 'modelos', label: 'Modelos', rows: CRUD('config.documentos.modelos') },
          { key: 'assinaturas', label: 'Assinaturas', rows: CRUD('config.documentos.assinaturas') },
          {
            key: 'versionamento',
            label: 'Versionamento',
            rows: CRUD('config.documentos.versionamento'),
          },
        ],
      },
      {
        key: 'tarefas',
        label: 'Tarefas',
        description: 'Automatizações de tarefas',
        features: [
          { key: 'modelos', label: 'Modelos', rows: CRUD('config.tarefas.modelos') },
          {
            key: 'automatizacoes',
            label: 'Automatizações',
            rows: CRUD('config.tarefas.automatizacoes'),
          },
          { key: 'notificacoes', label: 'Notificações', rows: CRUD('config.tarefas.notificacoes') },
        ],
      },
      {
        key: 'agenda',
        label: 'Agenda',
        description: 'Eventos e disponibilidade',
        features: [
          { key: 'eventos', label: 'Eventos', rows: CRUD('config.agenda.eventos') },
          { key: 'feriados', label: 'Feriados', rows: CRUD('config.agenda.feriados') },
          {
            key: 'disponibilidade',
            label: 'Disponibilidade',
            rows: CRUD('config.agenda.disponibilidade'),
          },
        ],
      },
    ],
  },
];
