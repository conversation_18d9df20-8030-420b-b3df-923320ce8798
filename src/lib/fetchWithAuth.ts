import { cookies } from 'next/headers';
import { auth } from './auth';
import type { NextRequest } from 'next/server'; // Replace with a relevant type if needed

/**
 * Wrapper around the built‑in fetch that automatically attaches the
 * access‑token stored in the NextAuth JWT cookie.
 *
 * ‑ On “public” calls just omit `opts.authenticated` (default = false).
 * ‑ For protected calls pass `{ authenticated: true }`.
 */
export async function fetchWithAuth(
  url: string,
  opts: RequestInit & { authenticated?: boolean; next?: NextRequest } = {}
) {
  const headers = new Headers(opts.headers);

  if (opts.body && !headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  if (opts.authenticated) {
    // Prefer getServerSession (works in Server Components & Server Actions)
    const session = await auth();
    const token =
      session?.accessToken ??
      (await cookies()).get('next-auth.session-token')?.value ?? // fallback for edge
      '';

    if (token) headers.set('Authorization', `Bearer ${token}`);
  }

  const next = opts.next ?? { revalidate: 0 }; // disable static cache by default

  return fetch(url, { ...opts, headers, next });
}
