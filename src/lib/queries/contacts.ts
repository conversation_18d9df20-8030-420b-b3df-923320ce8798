'use client';

import { useQuery } from '@tanstack/react-query';
import { useSessionFetch } from '../../utils/fetch';
import { UserList } from '../../schemas/userManagementSchema';

export const useContactsQuery = () => {
  const fetch = useSessionFetch();

  const { data: { users: contacts = [], total = 0 } = {}, isLoading: loading } = useQuery<{
    users: UserList[];
    total: number;
  }>({
    queryKey: ['contactUsers'],
    queryFn: () => {
      return fetch('users/list');
    },
    staleTime: Infinity,
  });

  return { contacts, total, loading };
};
