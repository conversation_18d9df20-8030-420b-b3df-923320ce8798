'use client';

import { useQuery } from '@tanstack/react-query';
import { mockEntities } from '../mocks/entities';
import { sleep } from '../../utils';

export const useEntitiesQuery = () => {
  const { data: entities = [], isLoading: loading } = useQuery({
    queryKey: ['entities'],
    queryFn: () => sleep(2000).then(() => mockEntities),
    staleTime: Infinity,
  });

  return { entities, loading };
};
