'use client';

import { useQuery } from '@tanstack/react-query';
import { Localidade } from '../api/geo';

export const useRegionalData = () => {
  const { data: regions = [], isLoading: loading = false } = useQuery<Localidade[]>({
    queryKey: ['geoInfo'],
    queryFn: async () => {
      const resp = await fetch('/api/geo', { method: 'GET' });

      return resp.json();
    },
    staleTime: Infinity,
  });

  return { regions, loading };
};
