import { useSuspenseQuery } from '@tanstack/react-query';
import { useSessionFetch } from '../../utils/fetch';
import { WithTotal } from '../../types';
import { Opportunity } from '../../schemas/opportunitySchema';
import { PaginationState } from '@tanstack/react-table';

export const useOpportunity = (id?: string) => {
  const fetch = useSessionFetch();

  return useSuspenseQuery<Opportunity | null>({
    queryKey: ['opportunities', id],
    queryFn: async () => {
      return await fetch(`opportunities/${id}`, {
        method: 'GET',
      });
    },
  }).data;
};

export const useOpportunities = (pagination?: PaginationState) => {
  const fetch = useSessionFetch();

  const { data } = useSuspenseQuery<WithTotal<Opportunity>>({
    queryKey: ['opportunitiesTable', pagination],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (pagination) {
        searchParams.set('offset', (pagination.pageIndex * pagination.pageSize).toString());
        searchParams.set('limit', pagination.pageSize.toString());
      }

      return await fetch(`opportunities/list?${searchParams.toString()}`);
    },
  });

  return data;
};
