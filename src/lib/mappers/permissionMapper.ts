import type { APIPermission, PermissionLevel } from '@/lib/types/permissions';

export type PermissionCode = `${string}.${string}`; // ex.: "leads.ver"
export type PermissionMap = Partial<Record<PermissionCode, PermissionLevel>>;

/** Converte a lista da API num mapa flat */
export function toPermissionMap(list: APIPermission[]): PermissionMap {
  const map: PermissionMap = {};

  list.forEach((p) => {
    if (p.is_inactive) return;

    // resolve nomes incoerentes
    const action =
      (p.permission_type as string) ||
      ((p as { permissions_type?: string }).permissions_type ?? '');

    const code = `${slugify(p.functionality)}.${action}` as PermissionCode;
    map[code] = p.permissions_value;
  });

  return map;
}

/** Converte o mapa de volta para o formato exigido pela API */
export function toAPIPermissionList(
  map: PermissionMap,
  baseInfo: Pick<APIPermission, 'functionality_id' | 'functionality'>
): APIPermission[] {
  return Object.entries(map).map(([code, value]) => {
    const [func, action] = code.split('.') as [string, string];

    return {
      functionality_id: baseInfo.functionality_id,
      functionality: func,
      is_inactive: false,
      permission_type: action as APIPermission['permission_type'],
      permissions_value: value!,
    };
  });
}

/* util simples; melhora conforme precisares */
function slugify(txt: string) {
  return txt
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // remove acentos
    .toLowerCase()
    .replace(/\s+/g, '-');
}
