import { z } from 'zod';
import { passwordSchema } from './globalsSchema';

export const updateProfile = z.object({
  email: z.string().email('Endereço de correio eletrónico inválido'),
  name: z.string(),
  phone: z.string().min(9, 'O telefone deve ter pelo menos 9 caracteres').optional(),
});

export const baseUpdatePasswordSchema = z
  .object({
    currentPassword: passwordSchema,
    newPassword: passwordSchema,
    confirmPassword: passwordSchema,
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })
  .refine((data) => data.currentPassword !== data.newPassword, {
    message: 'Passwords are equals to the old Password',
    path: ['newPassword'],
  });

export const activateUpdatePasswordSchema = baseUpdatePasswordSchema.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { confirmPassword, ...rest } = data;
  return rest;
}); // strip confirmPassword

export type UpdatePasswordFormValues = z.infer<typeof baseUpdatePasswordSchema>;
export type UpdatePasswordFormData = z.infer<typeof activateUpdatePasswordSchema>;
