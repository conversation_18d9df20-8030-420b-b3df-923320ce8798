import { z } from 'zod';

export const passwordSchema = z
  .string({
    required_error: 'Insira a Password ',
  })
  .min(12, { message: 'A palavra‑passe deve ter pelo menos 12 caracteres' })
  .regex(/[A-Z]/, { message: 'Deve conter pelo menos uma letra maiúscula' })
  .regex(/[a-z]/, { message: 'Deve conter pelo menos uma letra minúscula' })
  .regex(/[0-9]/, { message: 'Deve conter pelo menos um número' })
  .regex(/[^A-Za-z0-9]/, { message: 'Deve conter pelo menos um carácter especial' });
