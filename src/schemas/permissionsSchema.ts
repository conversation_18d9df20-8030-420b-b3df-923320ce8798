import { z } from 'zod';
import { ERole } from '../constants/role';
import { rolePermissionMap } from '../constants/permissionsConfig';

export const generatePermissionsList = (userProfile: ERole): PermissionModule[] => {
  return rolePermissionMap[userProfile] ?? [];
};

export const AvailablePermissions: PermissionModule[] = [
  {
    moduleName: 'metrics',
    permissions: [
      {
        id: '1',
        name: 'consult_metrics',
        enabled: false,
      },
    ],
  },
  {
    moduleName: 'store',
    permissions: [
      {
        id: '1',
        name: 'all_permissions',
        enabled: false,
      },
      {
        id: '2',
        name: 'create_manage_apps',
        enabled: false,
      },
      {
        id: '3',
        name: 'submit_version',
        enabled: false,
        requiredPermissions: ['create_manage_apps'],
      },
      {
        id: '4',
        name: 'create_manage_categories',
        enabled: false,
      },
      {
        id: '5',
        name: 'create_manage_promotions',
        enabled: false,
      },
    ],
  },
  {
    moduleName: 'management',
    permissions: [
      {
        id: '1',
        name: 'all_permissions',
        enabled: false,
      },
      {
        id: '2',
        name: 'create_manage_users',
        enabled: false,
      },
      {
        id: '3',
        name: 'profile_permissions',
        enabled: false,
      },
      {
        id: '4',
        name: 'password_reset',
        enabled: false,
      },
      {
        id: '5',
        name: 'create_locales',
        enabled: false,
      },
    ],
  },
];

export const permissionsSchema = z.object({
  permissions: z.object(
    Object.fromEntries(
      AvailablePermissions.map((module) => [
        module.moduleName,
        z
          .object(
            Object.fromEntries(
              module.permissions.map((permission) => [
                permission.name,
                z
                  .boolean()
                  .optional()
                  .refine((value) => value, {
                    message: `permissions.${module.moduleName}.${permission.name}`,
                  }),
              ])
            )
          )
          .refine(
            (data) => {
              const allPermissionsChecked = Object.values(data).some((value) => value);

              if (allPermissionsChecked) {
                return true;
              }
              return Object.values(data).every((value) => !value);
            },
            {
              message: `permissions.${module.moduleName}.all_permissions`,
            }
          )
          .refine(
            (data) => {
              const requiredPermissions = module.permissions.filter(
                (permission) => permission.requiredPermissions
              );
              return requiredPermissions.every((permission) => {
                const requiredPermissionName = permission?.requiredPermissions?.[0];
                if (!requiredPermissionName) {
                  return true;
                }
                const requiredPermissionValue = data[requiredPermissionName];
                return requiredPermissionValue || data[permission.name];
              });
            },
            {
              message: `permissions.${module.moduleName}.required_permissions`,
            }
          ),
      ])
    )
  ),
});

export type PermissionsFormData = z.infer<typeof permissionsSchema>;
