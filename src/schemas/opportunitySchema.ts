import z from 'zod';
import { mapLabel } from '../utils';
import { useOptionsIntl } from '../hooks/useFieldOptionsIntl';

export const opportunityStates = [
  'P2 + 3B',
  'P1',
  'Successo',
  'Insuccesso',
  'F. Up - Quente',
  'F. Up - Morno',
  'F. Up - Frio',
] as const;

export const opportunityStateOptions = mapLabel(opportunityStates);

export type OpportunityState = (typeof opportunityStates)[number];

export const opportunityInputSchema = z.object({
  description: z.string(),
  userId: z.string(),
  type: z.string(),
  entity: z.string().nullish(),
  assignedTo: z.string().nullish(),
  observations: z.string().nullish().default(''),
  status: z.enum(opportunityStates).nullish(),
  course: z.string().nullish(),
  center: z.string().nullish(),
  modality: z.string().nullish(),
  shift: z.string().nullish(),
  baseEducation: z.string().nullish(),
  baseType9: z.string().nullish(),
  baseType12: z.string().nullish(),
});

export type OpportunityInput = z.infer<typeof opportunityInputSchema>;

export const opportunityTypes = ['registration'] as const;

export const useOpportunityTypesOptions = () =>
  useOptionsIntl(opportunityTypes, 'forms.opportunityTypes');

export type OpportunityType = (typeof opportunityTypes)[number];

export const interactionTypes = ['call', 'sms', 'mail'] as const;
export const useInteractionTypesOptions = () =>
  useOptionsIntl(interactionTypes, 'forms.interactionTypes');

export type InteractionType = (typeof interactionTypes)[number];

export const interactionStates = ['success', 'insuccess'] as const;

export const useInteractionStateOptions = () =>
  useOptionsIntl(interactionStates, 'forms.state.options');

export type InteractionState = (typeof interactionStates)[number];

export const opportunityEditSchema = opportunityInputSchema.merge(
  z.object({
    closingDate: z.coerce.date().nullish(),
    closingState: z.enum(opportunityStates).nullish(),
    closingReason: z.string().nullish(),
    closingDescription: z.string().nullish(),
    sourceLead: z.string().nullish(),
  })
);

export type OpportunityEdit = z.infer<typeof opportunityEditSchema>;

export const opportunitySchema = z.object({
  id: z.string(),
  userId: z.string(),
  description: z.string(),
  status: z.string().nullish(),
  statusDate: z.coerce.date().optional(),
  entity: z.string().optional(),
  assignedTo: z.string().optional(),
  closingResult: z.string().optional(),
  closingDate: z.coerce.date().optional(),
  closingReason: z.string().optional(),
  closingText: z.string().optional(),
  sourceLead: z.string().optional(),
  lastInteractionDate: z.coerce.date().optional(),
  observations: z.string().optional(),
});

export type Opportunity = z.infer<typeof opportunitySchema>;
