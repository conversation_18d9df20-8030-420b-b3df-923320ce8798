import { isEmptyField } from '@/utils';
import { z } from 'zod';

export const zNameField = {
  name: z.string().nonempty({ message: 'forms.errors.name.required' }).default(''),
};
export const zEmailField = {
  email: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'forms.errors.email.invalidFormat',
    }),
};

export const zPhoneField = (nameField?: string) => ({
  [nameField ?? 'phone']: z
    .string()
    .optional()
    .refine((val) => isEmptyField(val) || (typeof val === 'string' && val.length >= 9), {
      message: 'forms.errors.phone.invalidFormat',
    }),
});

export const zNifField = {
  nif: z
    .string()
    .optional()
    .refine((val) => isEmptyField(val) || val?.length === 9, {
      message: 'forms.errors.nif.wrongLength',
    }),
};

export const baseFields = {
  ...zNameField,
  ...zEmailField,
  ...zPhoneField(),
  ...zNifField,
};
