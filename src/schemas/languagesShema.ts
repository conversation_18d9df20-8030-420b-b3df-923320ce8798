import { z } from 'zod';
const errorsKeys = {
  code: 'languages.errors.code',
  languageName: 'languages.errors.name',
  file: 'languages.errors.file',
};
export const languagesSchema = z.array(
  z.object({
    code: z.string().min(1, errorsKeys.code),
    languageName: z.string().min(1, errorsKeys.languageName),
    file: z.instanceof(File, { message: errorsKeys.file }).refine((file) => file.size > 0, {
      message: errorsKeys.file,
    }),
  })
);

export type LanguagesFormData = z.infer<typeof languagesSchema>;
