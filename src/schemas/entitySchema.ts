import { z } from 'zod';
import { baseFields, zPhoneField } from './zodFields';

export const ProtocolTypeSchema = z.enum(['pratical', 'internship']);

export type ProtocolType = z.infer<typeof ProtocolTypeSchema>;

export const EntityInputSchema = z.object({
  ...baseFields,
  website: z.string().optional(),
  location: z.string().optional(),
  distrito: z.string().optional(),
  concelho: z.string().optional(),
  freguesia: z.string().optional(),
  capital_social: z.string().optional(),
  country: z.literal('PT').optional().default('PT'),
  type: z
    .enum(['cliente_formacao', 'cliente_faturacao', 'entidade_empregadora', 'parceria'])
    .optional(),
  rgpd: z
    .object({
      consent_1: z.boolean().default(false),
      consent_2: z.boolean().default(false),
      consent_3: z.boolean().default(false),
      consent_4: z.boolean().default(false),
      consent_5: z.boolean().default(false),
    })
    .optional(),
  observations: z.string().optional(),
  protocolType: ProtocolTypeSchema,
  addresses: z
    .array(
      z.object({
        file: z.string(),
        type: ProtocolTypeSchema,
        description: z.string().default(''),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      })
    )
    .default([]),
});
export type EntityInput = z.infer<typeof EntityInputSchema>;

export const EntityEditSchema = EntityInputSchema.merge(
  z.object({
    addresses: z
      .array(
        z.object({
          file: z.string(),
          type: ProtocolTypeSchema,
          description: z.string().default(''),
          startDate: z.date().optional(),
          endDate: z.date().optional(),
        })
      )
      .default([]),
  })
);

export type EntityEditType = z.infer<typeof EntityEditSchema>;

export const EntityAttachmentInput = z
  .object({
    description: z.string().default(''),
    type: ProtocolTypeSchema,
    labels: z.string().default(''),
    startDate: z.coerce.date().optional(),
    endDate: z.coerce.date().optional(),
    file: z.instanceof(File, { message: 'Invalid File' }),
  })
  .refine((data) => data.startDate && data.endDate && data.startDate > data.endDate, {
    message: 'Start date must be lower than End date',
  });

export const EntitySchema = EntityInputSchema.merge(
  z.object({
    id: z.string(),
  })
);
export const EntitySchemaWithZDynamic = EntitySchema.merge(z.object(zPhoneField()));

export type EntityType = z.infer<typeof EntitySchemaWithZDynamic>;
