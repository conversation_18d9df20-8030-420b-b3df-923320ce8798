import { z } from 'zod';
import { baseFields, zPhoneField } from './zodFields';

const optionSchema = z.object({
  value: z.string(),
  label: z.string(),
});

export const rgpdSchema = z.object({
  consent_1: z.boolean().optional(),
  consent_2: z.boolean().optional(),
  consent_3: z.boolean().optional(),
  consent_4: z.boolean().optional(),
  consent_5: z.boolean().optional(),
});

const relationshipSchema = z.object({
  user: z.string().optional(),
  relationshipType: z.string().optional(),
});

const relationshipsSchema = z.record(relationshipSchema).optional();

const entitySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  description: z.string().optional(),
});

export const contactUserSchema = z.object({
  ...baseFields,
  ...zPhoneField('mobile'),
  socialSecurityNumber: z.string().optional(),
  identificationType: z.string().optional(),
  identificationValidity: z.date().optional(),
  identificationNumber: z.string().optional(),
  identificationFile: z.string().optional(),
  address: z.string().optional(),
  postalCode: z.string().optional(),
  locality: z.string().optional(),
  district: optionSchema.optional(),
  municipality: optionSchema.optional(),
  parish: optionSchema.optional(),
  country: optionSchema.optional(),
  birthPlace: z.string().optional(),
  profession: z.string().optional(),
  employerEntity: z.string().optional(),
  contractType: optionSchema.optional(),
  birthDate: z.date().optional(),
  gender: optionSchema.optional(),
  iban: z.string().optional(),
  educationLevel: optionSchema.optional(),
  notes: z.string().optional(),
  rgpd: rgpdSchema.optional(),
  relationships: relationshipsSchema,
  entities: z.array(entitySchema).optional(),
  attachments: z.record(z.any()).optional(),
  crmHistory: z.record(z.any()).optional(),
  enrollments: z.record(z.any()).optional(),
});