import { z } from 'zod';

// User display information
export const UserInfoSchema = z.object({
  email: z.string().email(),
  name: z.string(),
  phone: z.string().optional(),
  entityId: z.string().optional(),
  entityName: z.string().optional(),
  roles: z.array(z.string()),
  subRoles: z.array(z.string()),
  state: z.string(),
});
export type UserInfo = z.infer<typeof UserInfoSchema>;

// User editing/adding form data
export const UserDataSchema = z.object({
  user_id: z.string().optional(),
  email: z.string().email(),
  nif: z.string().optional(),
  name: z.string(),
  phone: z.string(),
  entityId: z.string(),
  roleCode: z.string(),
  subRoleCode: z.string(),
});
export type UserData = z.infer<typeof UserDataSchema>;

// User list data (used in the user management table)
export const UserListSchema = z.object({
  id: z.string(),
  email: z.string(),
  name: z.string(),
  state: z.string(),
  languageCode: z.string().optional(),
  roles: z.string().optional().array(),
  subRoles: z.string().optional().array(),
  entityName: z.string(),
  entityId: z.string().optional(),
  profile: z.string().optional(),
});
export type UserList = z.infer<typeof UserListSchema>;

export const TableParamsSchema = z.object({
  roleCode: z.string(),
  limit: z.number(),
  orderBy: z.string().optional(),
  sortOrder: z.string().optional(),
  name: z.string().optional(),
  email: z.string().optional(),
  page: z.number(),
});
export type TableParams = z.infer<typeof TableParamsSchema>;

// User Form Schema
export const UserFormSchema = z.object({
  id: z.string().optional(),
  roleCode: z.string().min(1, 'Role code is required'),
  email: z.string().email('Invalid email format'),
  name: z.string().regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  phone: z
    .string()
    .regex(/^\+?[0-9]*$/, "Phone can only contain numbers and '+' sign")
    .refine(
      (val) => val === '' || val.replace(/\D/g, '').length >= 9,
      'Phone must have at least 9 digits'
    )
    .optional(),
  entity: z.string().min(1, 'Entity is required'),
  entityName: z.string().optional(),
  profileType: z.string().min(1, 'Profile type is required'),
  state: z.string().optional(),
  password1: z
    .string()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_])[A-Za-z\d@$!%*?&_]{12,}$/,
      'Password must be at least 12 characters and include uppercase, lowercase, number, and special character'
    )
    .optional(),
  password2: z
    .string()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_])[A-Za-z\d@$!%*?&_]{12,}$/,
      'Password must be at least 12 characters and include uppercase, lowercase, number, and special character'
    )
    .optional(),
});

export type UserForm = z.infer<typeof UserFormSchema>;

export interface PasswordUpdate {
  password: string;
}
