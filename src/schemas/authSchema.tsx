import { z } from 'zod';
import { passwordSchema } from './globalsSchema';

export const loginSchema = z.object({
  email: z.string().email('Endereço de correio eletrónico inválido'),
  password: passwordSchema,
  reminderLogin: z.preprocess((value) => value === 'on', z.boolean()),
});

export type LoginFormData = z.infer<typeof loginSchema>;

export const loginFormApi = loginSchema.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { reminderLogin, ...rest } = data;
  return rest;
});

export const recoverPasswordSchema = z.object({
  email: z.string().email('Endereço de correio eletrónico inválido'),
  languageCode: z.enum(['pt', 'en']),
});

export type recoverPasswordData = z.infer<typeof recoverPasswordSchema>;

export const baseResetPasswordSchema = z
  .object({
    newPassword: passwordSchema,
    confirmPassword: passwordSchema,
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export type baseResetPasswordData = z.infer<typeof baseResetPasswordSchema>;

export const resetPasswordSchema = baseResetPasswordSchema.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { confirmPassword, ...rest } = data;
  return rest;
}); // strip confirmPassword

export type resetPasswordData = z.infer<typeof resetPasswordSchema>;

// Activate Account Schema
export const createUserSchema = z
  .object({
    name: z.string().nonempty('Insira o nome do usuário'),
    email: z.string().email('Endereço de correio eletrónico inválido'),
    password: passwordSchema,
    confirmPassword: passwordSchema,
    phone: z.string().min(9, 'O telefone deve ter pelo menos 9 caracteres').optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords does not match',
    path: ['confirmPassword'],
  });

export type CreateUserFormData = z.infer<typeof createUserSchema>;

export const apiCreateUserSchema = z.object({
  name: z.string().nonempty(),
  email: z.string().email(),
  password: z.string().min(8),
  phone: z.string().min(9).optional(),
});

export type ApiCreateUserData = z.infer<typeof apiCreateUserSchema>;

// Activate Account Schema
export const baseActivateAccountSchema = z
  .object({
    email: z.string().email('Endereço de correio eletrónico inválido'),
    otp: z.string().length(6, 'OTP must be exactly 6 digits'),
    password: passwordSchema,
    confirmPassword: passwordSchema,
    phone: z.string().min(9, 'Phone must be at least 9 characters').optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export const activateAccountSchema = baseActivateAccountSchema.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { confirmPassword, ...rest } = data;
  return rest;
}); // strip confirmPassword

export type ActivateAccountFormValues = z.infer<typeof baseActivateAccountSchema>;
export type ActivateAccountData = z.infer<typeof activateAccountSchema>;

export const formSchema = z
  .object({
    newPassword: z.string().min(8, 'A nova senha deve ter ao menos 8 caracteres'),
    confirmPassword: z.string().min(8, 'A confirmação deve ter ao menos 8 caracteres'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'As senhas não coincidem',
    path: ['confirmPassword'],
  });
