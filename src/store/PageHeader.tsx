'use client';

import { create } from 'zustand';

export type HandlerMap = Record<string, () => void>;

interface PageHeaderState {
  handlers: HandlerMap;
  setHandlers: (h: HandlerMap) => void;
  reset: () => void;
}

const usePageHeaderStore = create<PageHeaderState>((set) => ({
  handlers: {},
  setHandlers: (h) => set({ handlers: h }),
  reset: () => set({ handlers: {} }),
}));

export default usePageHeaderStore;
