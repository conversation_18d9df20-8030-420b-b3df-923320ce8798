'use client';
import { HREF_TAB } from '@/components/ui/sidebar/SidebarItems';
import { BOOL, VIEW } from '@/constants/enums';
import { Tab, TabStore } from '@/types/tabType';
import { create } from 'zustand';

// add get/post methods after
export const useTabStore = create<TabStore>((set, get) => ({
  // only needs to be called after tab changes, on the end of the methods, dont call before
  saveContext: () => {},
  formGetValues: {},
  // used to store getValues function for forms, on use, clear it always to avoid errors
  setFormGetValues: ({ id, getValues, clear = false }) =>
    set(() => {
      if (clear) return { formGetValues: {} };
      return {
        formGetValues: { id, getValues },
      };
    }),
  formContext: {},
  maxOpenTabs: 10,
  openTabs: [],
  openTab: () => get().openTabs.find((tab: Tab) => tab.isOpen),
  openSubTab: () => get().openTab()?.subTab,
  updateSubTab: ({ subTab }) => {
    set((state) => {
      const openTab = state.openTab();
      if (!openTab) return {};

      return {
        openTabs: state.openTabs.map((tab) =>
          tab.id === openTab.id ? { ...openTab, subTab } : tab
        ),
      };
    });
  },
  // here we add dependency relations if needed
  checkIfTabExists: ({ tab, changeOpenTab, isReplace, shouldAddDependencyRelation }) => {
    const openTabs = get().openTabs;
    const tabExists = openTabs.some((t) => t.id === tab.id);

    // here we construct the new tab objects accordingly if the tab exists or not, and add dependency accordingly
    if (shouldAddDependencyRelation) {
      const dependentTab = openTabs.find((t) => t.isOpen);
      if (dependentTab) {
        const { updatedDependentTab, updatedDependedTab } = get().addDependencyRelation({
          dependentTab,
          dependedTab: tab,
        });

        if (tabExists) {
          set({
            openTabs: openTabs.map((t) =>
              t.id === updatedDependentTab.id
                ? updatedDependentTab
                : t.id === updatedDependedTab.id
                  ? updatedDependedTab
                  : t
            ),
          });
          return false;
        } else {
          set({
            openTabs: openTabs.map((t) =>
              t.id === updatedDependentTab.id ? updatedDependentTab : t
            ),
          });
          get().addTab({ tab: updatedDependedTab, changeOpenTab, isReplace });
          return true;
        }
      }
    }

    // path that doesn't add dependency relations, old one
    if (!tabExists) {
      get().addTab({ tab, changeOpenTab, isReplace });
      return true;
    }
    return false;
  },
  // this ones cycles through checkIfTabExists and addtab when needed
  // when passing shoudlAddDependencyRelation, the new tab becomes dependent on the current open tab
  changeOpenTab: ({ tab, changeOpenTab, isReplace, shouldAddDependencyRelation = false }) =>
    set((state) => {
      const shouldStop = state.checkIfTabExists({
        tab,
        changeOpenTab,
        isReplace,
        shouldAddDependencyRelation,
      });

      if (shouldStop) {
        get().saveContext();
        return {};
      }

      const newTabs = state.openTabs.map((iterateTab) => ({
        ...iterateTab,
        isOpen: iterateTab.id === tab.id,
      }));
      get().saveContext();
      return { openTabs: newTabs };
    }),
  // if passed changeOpenTab to false, it will not change the open tab
  // if isReplace is false, it will add the new tab without replacing the current open tab
  addTab: ({ tab, changeOpenTab = true, isReplace = false }) =>
    set((state) => {
      if (isReplace) {
        // Replace the current open tab with the new one
        const idx = state.openTabs.findIndex((t) => t.isOpen);
        if (idx !== -1) {
          const newTabs = [...state.openTabs];
          newTabs[idx] = { ...tab, isOpen: true };
          return { openTabs: newTabs };
        }
      }
      return {
        openTabs: state.openTabs
          .map((iterateTab) => ({ ...iterateTab, isOpen: !changeOpenTab }))
          .concat({ ...tab, isOpen: changeOpenTab }),
      };
    }),
  // use returned boolean to launch error if needed
  removeTab: ({ tab }) => {
    // check if tab can be closed before proceeding
    if (!get().checkIfTabCanBeClosed({ tab })) {
      return false;
    }
    set((state) => {
      const idx = state.openTabs.findIndex((iterateTab) => iterateTab.id === tab.id);
      console.log('idx and tab is: ', idx, tab);
      const filtered = state.openTabs.filter((iterateTab) => iterateTab.id !== tab.id);
      let newTabs = filtered;
      console.log('filtered is:', filtered, tab);
      if (state.openTabs[idx]?.isOpen && filtered.length > 0) {
        // if the removed tab was open, open the next tab, or previous if last
        const newOpenIdx = idx < filtered.length ? idx : filtered.length - 1;
        newTabs = filtered.map((iterateTab, i) => ({
          ...iterateTab,
          isOpen: i === newOpenIdx,
        }));
      }
      return { openTabs: newTabs };
    });
    get().saveContext();
    return true;
  },
  isLimitReached: () => get().openTabs.length >= get().maxOpenTabs,
  addDependencyRelation: ({ dependentTab, dependedTab }) => {
    // create updated copies of the two tabs with the dependency relationship
    const updatedDependentTab = {
      ...dependentTab,
      dependsOn: dependentTab.dependsOn
        ? dependentTab.dependsOn.some((dep) => dep.id === dependedTab.id)
          ? [...dependentTab.dependsOn]
          : [...dependentTab.dependsOn, { id: dependedTab.id, canClose: false }]
        : [{ id: dependedTab.id, canClose: false }],
    };

    const updatedDependedTab = {
      ...dependedTab,
      dependentBy: dependedTab.dependentBy
        ? dependedTab.dependentBy.some((dep) => dep.id === dependentTab.id)
          ? [...dependedTab.dependentBy]
          : [...dependedTab.dependentBy, { id: dependentTab.id }]
        : [{ id: dependentTab.id }],
    };

    return { updatedDependentTab, updatedDependedTab };
  },
  setDependentByTabsCanClose: ({ tab }) => {
    set((state) => {
      // TODO needs more testing, but should work now
      const newTabs = state.openTabs.map((t) => {
        if (tab?.dependentBy?.some((dep) => dep.id === t.id)) {
          return {
            ...t,
            dependsOn: t.dependsOn?.filter((dep) => dep.id !== tab.id) ?? [],
          };
        }
        return t;
      });
      return { openTabs: newTabs };
    });
  },
  checkIfTabCanBeClosed: ({ tab }) => {
    const checkedTab = get().openTabs.find((iterateTab) => iterateTab.id === tab.id);
    // check if tab exists
    if (!checkedTab) return false;
    // Check dependsOn: if any canClose is false, return false
    if (checkedTab?.dependsOn?.some((dep) => dep.canClose === false)) return false;
    // checks dependentBy: finishes the dependecies
    if (checkedTab?.dependentBy && checkedTab.dependentBy.length > 0)
      get().setDependentByTabsCanClose({ tab: checkedTab });
    return true;
  },
  constructStaticTab: ({ id, view = VIEW.VIEW }) => {
    return {
      ...HREF_TAB[id],
      view: view,
    };
  },
  constructDynamicTab: ({ id, title, view = VIEW.VIEW }) => {
    return {
      id,
      title: title ?? 'INSERT LOADING',
      hasChanges: false,
      view: view,
    };
  },
  // if you know the type of url that will be generated, isStatic should be passed as BOOL.TRUE or BOOL.FALSE
  // if you don't know, isStatic can be BOOL.UNDEFINED or just ignored, it will check both and discover for itself, less perfomrtative but more flexible
  constructTab: ({ id, isStatic = BOOL.UNDEFINED, title, view }) => {
    if (id === '/dashboard') return undefined;
    if (isStatic === BOOL.TRUE) return get().constructStaticTab({ id });
    if (!isStatic || isStatic === BOOL.UNDEFINED) {
      if (HREF_TAB[id]) return get().constructStaticTab({ id });
      if (id) return get().constructDynamicTab({ id, title, view });
    }
    if (isStatic === BOOL.FALSE) {
      return get().constructDynamicTab({ id, title, view });
    }
    // add more conditions here as needed, will be needed when we integrate dynamic pages with ids from bd
    return undefined;
  },
  // if passed id, use tab with that id, if not passed, use the opentab
  // if hasChanges is passed, it will update the tab with that value, if not passed, it will toggle the current value
  updateHasChanges: ({ id, hasChanges }: { id?: string; hasChanges?: boolean }) =>
    set((state) => {
      const targetTab = id
        ? state.openTabs.find((tab: Tab) => tab.id === id)
        : state.openTabs.find((tab: Tab) => tab.isOpen);

      if (!targetTab) return {};

      const newHasChanges =
        typeof hasChanges === 'boolean'
          ? hasChanges
          : typeof targetTab.hasChanges === 'boolean'
            ? !targetTab.hasChanges
            : true;

      const updatedTabs = state.openTabs.map((tab: any) =>
        tab.id === targetTab.id ? { ...tab, hasChanges: newHasChanges } : tab
      );
      return { openTabs: updatedTabs };
    }),
  setFormContext: ({ id, formValues, clear }) =>
    set((state) => {
      const targetId = id ?? get().openTab()?.id;
      if (!targetId) return {};
      if (clear) {
        const { [targetId]: _, ...rest } = state.formContext;
        return {
          formContext: rest,
        };
      }
      return {
        formContext: {
          ...state.formContext,
          [targetId]: formValues,
        },
      };
    }),
  // updates currentTab view, if you want to update a specific tab, call handleChangeTab with the tab object and view
  updateTabView: ({ id, view }) =>
    set((state) => {
      const openTab = id ? state.openTabs.find((tab) => tab.id === id) : state.openTab();
      if (!openTab) return {};
      openTab.view = view;
      return {
        openTabs: state.openTabs.map((tab) => (tab.id === openTab.id ? { ...openTab, view } : tab)),
      };
    }),
}));
