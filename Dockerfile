###############################################################################
# 1. deps stage – install production dependencies only once
###############################################################################
FROM node:20-alpine AS deps

WORKDIR /app

# Copy lockfiles and install deps (no devDependencies)
COPY package.json package-lock.json ./
RUN npm ci --omit=dev

###############################################################################
# 2. builder stage – compile the Next.js app
###############################################################################
FROM node:20-alpine AS builder

WORKDIR /app

# Re‑use node_modules from the previous layer
COPY --from=deps /app/node_modules ./node_modules

# Copy the entire source code and build
COPY . .
RUN npm run build       

###############################################################################
# 3. runner stage – lightweight image for runtime
###############################################################################
FROM node:20-alpine AS runner

WORKDIR /app
ENV NODE_ENV=production

# Copy only what is required to run the app
COPY --from=builder /app/next.config.* ./
COPY --from=builder /app/package.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public

EXPOSE 3000                
CMD ["npm", "start"]       
