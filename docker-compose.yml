version: '3.8'
services:
  react-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: ${APP_ENV} # Use environment variable to select target (dev or prod)
    image: caixa-magica-frontend-${APP_ENV}:${VERSION} # Tag the image with the build number
    ports:
      - '5173:5173' # Expose dev port
      # - "80:80" # Expose prod port
    env_file:
      - .env # Ensure this points to your actual .env file
    environment:
      - APP_ENV=${APP_ENV}
      - VITE_ENVIRONMENT_DEV=${VITE_ENVIRONMENT_DEV}
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./src:/app/src # For dev: sync the source code
      - /app/node_modules # Persist node_modules
    stdin_open: true
    tty: true
    restart: always