###############################################################################
# 1. ConfigMap – environment variables
###############################################################################
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: appstore-frontend
data:
  VERSION: "0.0.1"
  APP_ENV: "dev"

  # Public‑side variables (injected into the client bundle)
  NEXT_PUBLIC_API_URL_DEV: "http://localhost:3001/"
  NEXT_PUBLIC_API_URL_TST: "https://api.test.com/"
  NEXT_PUBLIC_API_URL_PRD: "https://api.production.com/"

  # Server‑side‑only variable (not exposed in the client)
  INTERNAL_API_BASE: "http://backend-internal:4000/"
---
###############################################################################
# 2. Service – exposes the app inside the cluster
###############################################################################
apiVersion: v1
kind: Service
metadata:
  name: frontend
  namespace: appstore-frontend
spec:
  selector:
    app: frontend
  ports:
    - name: http
      port: 80           # Cluster‑internal port
      targetPort: 3000   # Port opened by the container (Next.js)
      protocol: TCP
  type: ClusterIP
---
###############################################################################
# 3. Deployment – runs two Next.js replicas
###############################################################################
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: appstore-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
        - name: frontend
          image: qlyaksregistry-c3fjerdqhmhhb9fr.azurecr.io/frontend:latest
          ports:
            - containerPort: 3000
              name: http
          envFrom:
            - configMapRef:
                name: frontend-config
          resources:
            requests:
              cpu: "250m"
              memory: "256Mi"
            limits:
              memory: "512Mi"
          # Wait until the health‑check endpoint is ready
          readinessProbe:
            httpGet:
              path: /api/healthz
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
          # Restart the pod if the app becomes unresponsive
          livenessProbe:
            httpGet:
              path: /api/healthz
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
